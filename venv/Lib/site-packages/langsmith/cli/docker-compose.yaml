services:
  langchain-playground:
    image: langchain/langsmith-playground:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    ports:
      - 3001:3001
    environment:
      - PORT=3001
      - LANGCHAIN_ENV=local_docker
      - LOG_LEVEL=${LOG_LEVEL:-info}
  langchain-frontend:
    image: langchain/langsmith-frontend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    environment:
      - VITE_BACKEND_AUTH_TYPE=${AUTH_TYPE:-none}
      - VITE_BASIC_AUTH_ENABLED=${BASIC_AUTH_ENABLED:-false}
      - VITE_OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID}
      - VITE_OAUTH_ISSUER_URL=${OAUTH_ISSUER_URL}
    ports:
      - 1980:1980
    depends_on:
      - langchain-backend
      - langchain-playground
  langchain-ace-backend:
    image: langchain/langsmith-ace-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    ports:
      - 1987:1987
    environment:
      - PORT=1987
    command:
      - "deno"
      - "run"
      - "--unstable-worker-options"
      - "--allow-env"
      - "--allow-net=0.0.0.0:1987"
      - "--node-modules-dir"
      - "-R"
      - "src/main.ts"
      - "-R"
      - "src/python_worker.ts"
  langchain-backend:
    image: langchain/langsmith-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    environment:
      - PORT=1984
      - LANGCHAIN_ENV=local_docker
      - LANGSMITH_URL=${LANGSMITH_URL:-http://langchain-frontend:1980}
      - GO_ENDPOINT=http://langchain-platform-backend:1986
      - SMITH_BACKEND_ENDPOINT=${SMITH_BACKEND_ENDPOINT:-http://langchain-backend:1984}
      - LANGSMITH_LICENSE_KEY=${LANGSMITH_LICENSE_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - AUTH_TYPE=${AUTH_TYPE:-none}
      - OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID}
      - OAUTH_CLIENT_SECRET=${OAUTH_CLIENT_SECRET}
      - OAUTH_ISSUER_URL=${OAUTH_ISSUER_URL}
      - API_KEY_SALT=${API_KEY_SALT}
      - X_SERVICE_AUTH_JWT_SECRET=${API_KEY_SALT}
      - POSTGRES_DATABASE_URI=${POSTGRES_DATABASE_URI:-postgres:postgres@langchain-db:5432/postgres}
      - REDIS_DATABASE_URI=${REDIS_DATABASE_URI:-redis://langchain-redis:6379}
      - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-langchain-clickhouse}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-password}
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-default}
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_TLS=${CLICKHOUSE_TLS:-false}
      - FF_ORG_CREATION_DISABLED=${ORG_CREATION_DISABLED:-false}
      - FF_TRACE_TIERS_ENABLED=${TTL_ENABLED:-true}
      - FF_UPGRADE_TRACE_TIER_ENABLED=${TTL_ENABLED:-true}
      - FF_S3_STORAGE_ENABLED=${BLOB_STORAGE_ENABLED:-false}
      - S3_BUCKET_NAME=${BLOB_STORAGE_BUCKET_NAME:-langsmith-s3-assets}
      - S3_RUN_MANIFEST_BUCKET_NAME=${BLOB_STORAGE_BUCKET_NAME:-langsmith-s3-assets}
      - S3_API_URL=${BLOB_STORAGE_API_URL:-https://s3.us-west-2.amazonaws.com}
      - S3_ACCESS_KEY=${BLOB_STORAGE_ACCESS_KEY}
      - S3_ACCESS_KEY_SECRET=${BLOB_STORAGE_ACCESS_KEY_SECRET}
      - FF_CH_SEARCH_ENABLED=${CH_SEARCH_ENABLED:-true}
      - BASIC_AUTH_ENABLED=${BASIC_AUTH_ENABLED:-false}
      - BASIC_AUTH_JWT_SECRET=${BASIC_AUTH_JWT_SECRET}
      - INITIAL_ORG_ADMIN_EMAIL=${INITIAL_ORG_ADMIN_EMAIL}
      - INITIAL_ORG_ADMIN_PASSWORD=${INITIAL_ORG_ADMIN_PASSWORD}
    ports:
      - 1984:1984
    depends_on:
      langchain-db:
        condition: service_healthy
      langchain-redis:
        condition: service_healthy
      clickhouse-setup:
        condition: service_completed_successfully
      postgres-setup:
        condition: service_completed_successfully
    restart: always
  langchain-platform-backend:
    image: langchain/langsmith-go-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    environment:
      - PORT=1986
      - LANGCHAIN_ENV=local_docker
      - LANGSMITH_URL=${LANGSMITH_URL:-http://langchain-frontend:1980}
      - SMITH_BACKEND_ENDPOINT=${SMITH_BACKEND_ENDPOINT:-http://langchain-backend:1984}
      - LANGSMITH_LICENSE_KEY=${LANGSMITH_LICENSE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-warning}
      - AUTH_TYPE=${AUTH_TYPE:-none}
      - OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID}
      - OAUTH_CLIENT_SECRET=${OAUTH_CLIENT_SECRET}
      - OAUTH_ISSUER_URL=${OAUTH_ISSUER_URL}
      - API_KEY_SALT=${API_KEY_SALT}
      - X_SERVICE_AUTH_JWT_SECRET=${API_KEY_SALT}
      - POSTGRES_DATABASE_URI=${POSTGRES_DATABASE_URI:-postgres:postgres@langchain-db:5432/postgres}
      - REDIS_DATABASE_URI=${REDIS_DATABASE_URI:-redis://langchain-redis:6379}
      - BASIC_AUTH_ENABLED=${BASIC_AUTH_ENABLED:-false}
      - BASIC_AUTH_JWT_SECRET=${BASIC_AUTH_JWT_SECRET}
    ports:
      - 1986:1986
    depends_on:
      langchain-db:
        condition: service_healthy
      langchain-redis:
        condition: service_healthy
      clickhouse-setup:
        condition: service_completed_successfully
      postgres-setup:
        condition: service_completed_successfully
    restart: always
  langchain-queue:
    image: langchain/langsmith-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    environment:
      - LANGCHAIN_ENV=local_docker
      - GO_ENDPOINT=http://langchain-platform-backend:1986
      - SMITH_BACKEND_ENDPOINT=http://langchain-backend:1984
      - LANGSMITH_LICENSE_KEY=${LANGSMITH_LICENSE_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - AUTH_TYPE=${AUTH_TYPE:-none}
      - OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID}
      - OAUTH_ISSUER_URL=${OAUTH_ISSUER_URL}
      - API_KEY_SALT=${API_KEY_SALT}
      - X_SERVICE_AUTH_JWT_SECRET=${API_KEY_SALT}
      - POSTGRES_DATABASE_URI=${POSTGRES_DATABASE_URI:-postgres:postgres@langchain-db:5432/postgres}
      - REDIS_DATABASE_URI=${REDIS_DATABASE_URI:-redis://langchain-redis:6379}
      - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-langchain-clickhouse}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-password}
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-default}
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_TLS=${CLICKHOUSE_TLS:-false}
      - FF_ORG_CREATION_DISABLED=${ORG_CREATION_DISABLED:-false}
      - FF_TRACE_TIERS_ENABLED=${TTL_ENABLED:-true}
      - FF_UPGRADE_TRACE_TIER_ENABLED=${TTL_ENABLED:-true}
      - FF_S3_STORAGE_ENABLED=${BLOB_STORAGE_ENABLED:-false}
      - S3_BUCKET_NAME=${BLOB_STORAGE_BUCKET_NAME:-langsmith-s3-assets}
      - S3_RUN_MANIFEST_BUCKET_NAME=${BLOB_STORAGE_BUCKET_NAME:-langsmith-s3-assets}
      - S3_API_URL=${BLOB_STORAGE_API_URL:-https://s3.us-west-2.amazonaws.com}
      - S3_ACCESS_KEY=${BLOB_STORAGE_ACCESS_KEY}
      - S3_ACCESS_KEY_SECRET=${BLOB_STORAGE_ACCESS_KEY_SECRET}
      - FF_CH_SEARCH_ENABLED=${CH_SEARCH_ENABLED:-true}
      - BASIC_AUTH_ENABLED=${BASIC_AUTH_ENABLED:-false}
      - BASIC_AUTH_JWT_SECRET=${BASIC_AUTH_JWT_SECRET}
    command:
      - "saq"
      - "app.workers.queues.single_queue_worker.settings"
      - "--quiet"
    depends_on:
      langchain-db:
        condition: service_healthy
      langchain-redis:
        condition: service_healthy
      clickhouse-setup:
        condition: service_completed_successfully
      postgres-setup:
        condition: service_completed_successfully
    restart: always
  langchain-db:
    image: postgres:14.7
    command:
      [
        "postgres",
        "-c",
        "log_min_messages=WARNING",
        "-c",
        "client_min_messages=WARNING",
      ]
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=postgres
    volumes:
      - langchain-db-data:/var/lib/postgresql/data
    ports:
      - 5433:5432
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 2s
      timeout: 2s
      retries: 30
  langchain-redis:
    image: redis:7
    ports:
      - 63791:6379
    volumes:
      - langchain-redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 2s
      timeout: 2s
      retries: 30
  langchain-clickhouse:
    image: clickhouse/clickhouse-server:24.5
    user: "101:101"
    restart: always
    environment:
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-default}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-password}
    volumes:
      - langchain-clickhouse-data:/var/lib/clickhouse
      - ./users.xml:/etc/clickhouse-server/users.d/users.xml
    ports:
      - 8124:8123
      - 9001:9000
    healthcheck:
      test: ["CMD", "clickhouse-client", "--query", "SELECT 1"]
      interval: 2s
      timeout: 2s
      retries: 30
  clickhouse-setup:
    image: langchain/langsmith-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    depends_on:
      langchain-clickhouse:
        condition: service_healthy
    restart: "on-failure:10"
    environment:
      - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-langchain-clickhouse}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-password}
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-default}
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_NATIVE_PORT=${CLICKHOUSE_NATIVE_PORT:-9000}
      - CLICKHOUSE_TLS=${CLICKHOUSE_TLS:-false}
    command:
      [
        "bash",
        "scripts/wait_for_clickhouse_and_migrate.sh"
      ]
  postgres-setup:
    image: langchain/langsmith-backend:${_LANGSMITH_IMAGE_VERSION:-0.8.12}
    depends_on:
      langchain-db:
        condition: service_healthy
    environment:
      - LANGCHAIN_ENV=local_docker
      - LANGSMITH_LICENSE_KEY=${LANGSMITH_LICENSE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-warning}
      - AUTH_TYPE=${AUTH_TYPE:-none}
      - OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID}
      - OAUTH_ISSUER_URL=${OAUTH_ISSUER_URL}
      - API_KEY_SALT=${API_KEY_SALT}
      - POSTGRES_DATABASE_URI=${POSTGRES_DATABASE_URI:-postgres:postgres@langchain-db:5432/postgres}
      - REDIS_DATABASE_URI=${REDIS_DATABASE_URI:-redis://langchain-redis:6379}
      - MAX_ASYNC_JOBS_PER_WORKER=${MAX_ASYNC_JOBS_PER_WORKER:-10}
      - ASYNCPG_POOL_MAX_SIZE=${ASYNCPG_POOL_MAX_SIZE:-3}
      - CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-langchain-clickhouse}
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-password}
      - CLICKHOUSE_DB=${CLICKHOUSE_DB:-default}
      - CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
      - CLICKHOUSE_NATIVE_PORT=${CLICKHOUSE_NATIVE_PORT:-9000}
      - CLICKHOUSE_TLS=${CLICKHOUSE_TLS:-false}
    restart: "on-failure:10"
    command:
      [
        "bash",
        "-c",
        "alembic upgrade head",
      ]
volumes:
  langchain-db-data:
  langchain-redis-data:
  langchain-clickhouse-data:
