Metadata-Version: 2.1
Name: DBUtils
Version: 3.1.0
Summary: Database connections for multi-threaded environments.
Author-email: <PERSON> <<EMAIL>>
License: MIT License
Project-URL: Homepage, https://webwareforpython.github.io/DBUtils/
Project-URL: Download, https://pypi.org/project/DBUtils/
Project-URL: Documentation, https://webwareforpython.github.io/DBUtils/main.html
Project-URL: Changelog, https://webwareforpython.github.io/DBUtils/changelog.html
Project-URL: Issue Tracker, https://github.com/WebwareForPython/DBUtils/issues
Project-URL: Source Code, https://github.com/WebwareForPython/DBUtils
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Provides-Extra: docs
Requires-Dist: docutils ; extra == 'docs'
Provides-Extra: pg
Requires-Dist: PyGreSQL >=5 ; extra == 'pg'
Provides-Extra: tests
Requires-Dist: pytest >=7 ; extra == 'tests'
Requires-Dist: ruff ; extra == 'tests'

DBUtils
=======

DBUtils is a suite of tools providing solid, persistent and pooled connections
to a database that can be used in all kinds of multi-threaded environments.

The suite supports DB-API 2 compliant database interfaces
and the classic PyGreSQL interface.

The current version 3.1.0 of DBUtils supports Python versions 3.7 to 3.12.

**Please have a look at the [changelog](https://webwareforpython.github.io/DBUtils/changelog.html), because there were some breaking changes in version 2.0.**

The DBUtils home page can be found at https://webwareforpython.github.io/DBUtils/
