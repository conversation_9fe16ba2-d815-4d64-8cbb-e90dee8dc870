<template>
  <el-form
    class="login-form"
    status-icon
    :rules="loginRules"
    ref="loginForm"
    :model="loginForm"
    label-width="0"
  >
    <el-form-item v-if="tenantMode" prop="tenantName">
      <el-input
        size="small"
        @keyup.enter.native="handleLogin"
        v-model="loginForm.tenantName"
        :placeholder="$t('login.businessName')"
      >
        <i slot="prefix" class="icon-quanxian" />
      </el-input>
    </el-form-item>
    <el-form-item prop="username">
      <el-input
        size="small"
        @keyup.enter.native="handleLogin"
        v-model="loginForm.username"
        maxlength="11"
        :placeholder="$t('login.username')"
      >
        <i slot="prefix" class="icon-yonghu" />
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        size="small"
        @keyup.enter.native="handleLogin"
        :type="passwordType"
        v-model="loginForm.password"
        :placeholder="$t('login.password')"
        autocomplete="new-password"
      >
        <i
          class="el-icon-view el-input__icon"
          slot="suffix"
          @click="showPassword"
        />
        <i slot="prefix" class="icon-mima" />
      </el-input>
    </el-form-item>
    <el-form-item
      v-if="this.website.captchaMode && this.website.captchaType === 'input'"
      prop="code"
    >
      <el-row :span="24">
        <el-col :span="16">
          <el-input
            size="small"
            @keyup.enter.native="handleLogin"
            v-model="loginForm.code"
            auto-complete="off"
            :placeholder="$t('login.code')"
          >
            <i slot="prefix" class="icon-yanzhengma" />
          </el-input>
        </el-col>
        <el-col :span="8">
          <div class="login-code">
            <img
              :src="loginForm.image"
              class="login-code-img"
              @click="refreshCode"
            />
          </div>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click.native.prevent="handleLogin"
        class="login-submit"
        >{{ $t('login.submit') }}
      </el-button>
    </el-form-item>
    <div class="link-register">
      <el-link type="primary" @click="handleRegister">没有账号，请注册</el-link>
    </div>
    <el-dialog
      title="用户信息选择"
      append-to-body
      :visible.sync="userBox"
      width="350px"
    >
      <avue-form
        :option="userOption"
        v-model="userForm"
        @submit="submitLogin"
      />
    </el-dialog>

    <el-dialog
      :modal="false"
      :close-on-click-modal="true"
      :show-close="false"
      append-to-body
      :visible.sync="captchaShow"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      class="transparent-dialog"
      width="280px"
    >
      <yk-captcha-click
        v-if="this.website.captchaType === 'click'"
        ref="captchaSliderRef"
        @goLogin="goLogin"
      />
      <yk-captcha-slider
        v-if="this.website.captchaType === 'slider'"
        ref="captchaSliderRef"
        @goLogin="goLogin"
      />
    </el-dialog>
  </el-form>
</template>

<script>
  import Cookies from 'js-cookie';
  import { mapGetters } from 'vuex';
  import { info } from '@/api/system/tenant';
  import { getCaptcha } from '@/api/user';
  import { getTopUrl } from '@/util/util';
  // import { regexp } from '@/constant/common';
  import YkCaptchaSlider from '@/components/yk-captcha-slider';
  import YkCaptchaClick from '@/components/yk-captcha-click';
  import { dateFormat } from '@/util/date';
  import website from '@/config/website';
  const tagWel = website.fistPage;
  export default {
    name: 'userlogin',
    components: { YkCaptchaSlider, YkCaptchaClick },
    data() {
      // const validatePass = (rule, value, callback) => {
      //   if (!regexp.test(value)) {
      //     callback(
      //       new Error(
      //         '密码必须包含大写字母、小写字母、特殊符号、数字四种类型的8~16位字符。'
      //       )
      //     );
      //   } else {
      //     callback();
      //   }
      // };
      return {
        tagWel,
        tenantMode: this.website.tenantMode,
        captchaShow: false,
        loginForm: {
          //企业名称
          tenantName: '',
          //部门ID
          deptId: '',
          //角色ID
          roleId: '',
          //用户名
          username: '',
          //密码
          password: '',
          //账号类型
          type: 'account',
          //验证码的值
          code: '',
          // 滑动轨迹滑动时间等数据
          trackData: '',
          //验证码的索引
          key: '',
          //预加载白色背景
          image:
            'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
        },
        loginRules: {
          tenantName: [
            { required: true, message: '请输入企业名称', trigger: 'blur' }
          ],
          username: [
            { required: true, message: '请输入账号', trigger: 'blur' }
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' }
            // { validator: validatePass, trigger: 'blur' }
          ]
        },
        passwordType: 'password',
        userBox: false,
        userForm: {
          deptId: '',
          roleId: ''
        },
        userOption: {
          labelWidth: 70,
          submitBtn: true,
          emptyBtn: false,
          submitText: '登录',
          column: [
            {
              label: '部门',
              prop: 'deptId',
              type: 'select',
              props: {
                label: 'deptName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/dept/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择部门',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '角色',
              prop: 'roleId',
              type: 'select',
              props: {
                label: 'roleName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/role/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择角色',
                  trigger: 'blur'
                }
              ]
            }
          ]
        }
      };
    },
    created() {
      this.getTenant();
      if (this.website.captchaMode && this.website.captchaType === 'input') {
        this.refreshCode();
      }
    },
    mounted() {
      let alreadyAccount = this.$route.query.alreadyAccount;
      if (!alreadyAccount) {
        this.loginForm.tenantName = '';
        this.loginForm.username = '';
        this.loginForm.password = '';
        return;
      }
      let loginForm = Cookies.get('login-info');
      if (loginForm) {
        loginForm = JSON.parse(loginForm);
        let { tenantName, username, password } = loginForm;
        this.loginForm.tenantName = tenantName;
        this.loginForm.username = username;
        this.loginForm.password = password;
      }
    },
    watch: {
      'loginForm.deptId'() {
        const column = this.findObject(this.userOption.column, 'deptId');
        if (this.loginForm.deptId.includes(',')) {
          column.dicUrl = `/api/szyk-system/dept/select?deptId=${this.loginForm.deptId}`;
          column.display = true;
        } else {
          column.dicUrl = '';
        }
      },
      'loginForm.roleId'() {
        const column = this.findObject(this.userOption.column, 'roleId');
        if (this.loginForm.roleId.includes(',')) {
          column.dicUrl = `/api/szyk-system/role/select?roleId=${this.loginForm.roleId}`;
          column.display = true;
        } else {
          column.dicUrl = '';
        }
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    props: [],
    methods: {
      randomName() {
        return Math.random().toString(36).substring(2, 15);
      },

      handleRegister() {
        this.$router.push({ path: '/enroll' });
      },
      refreshCode() {
        if (this.website.captchaMode) {
          getCaptcha().then((res) => {
            const data = res.data;
            this.loginForm.key = data.key;
            this.loginForm.image = data.image;
          });
        }
      },
      showPassword() {
        this.passwordType === ''
          ? (this.passwordType = 'password')
          : (this.passwordType = '');
      },
      submitLogin(form, done) {
        if (form.deptId !== '') {
          this.loginForm.deptId = form.deptId;
        }
        if (form.roleId !== '') {
          this.loginForm.roleId = form.roleId;
        }
        this.handleLogin();
        done();
      },
      handleLogin() {
        const captchaType = this.website.captchaType;
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            if (captchaType === 'click' || captchaType === 'slider') {
              if (!this.captchaShow) {
                this.captchaShow = true;
              }
            }
            if (captchaType === 'input') {
              const loading = this.$loading({
                lock: true,
                text: '登录中,请稍后。。。',
                spinner: 'el-icon-loading'
              });
              this.$store
                .dispatch('LoginByUsername', this.loginForm)
                .then(() => {
                  if (this.website.switchMode) {
                    const deptId = this.userInfo.dept_id;
                    const roleId = this.userInfo.role_id;
                    if (deptId.includes(',') || roleId.includes(',')) {
                      this.loginForm.deptId = deptId;
                      this.loginForm.roleId = roleId;
                      this.userBox = true;
                      this.$store.dispatch('LogOut').then(() => {
                        loading.close();
                      });
                      return false;
                    }
                  }
                  this.$router.push({ path: this.tagWel.value });
                  loading.close();
                })
                .catch(() => {
                  loading.close();
                  this.refreshCode();
                });
            }
          }
        });
      },
      goLogin(id, trackData) {
        trackData.startSlidingTime = dateFormat(trackData.startSlidingTime);
        trackData.endSlidingTime = dateFormat(trackData.endSlidingTime);
        this.loginForm.key = id;
        this.loginForm.trackData = trackData;
        // debugger;
        const loading = this.$loading({
          lock: true,
          text: '登录中,请稍后。。。',
          spinner: 'el-icon-loading'
        });
        this.$store
          .dispatch('LoginByUsernameCode', this.loginForm)
          .then(() => {
            // 保存登陆数据
            let { tenantName, username, password } = this.loginForm;
            let params = { tenantName, username, password };
            Cookies.set('login-info', JSON.stringify(params), { expires: 1 });
            // 保存登陆数据end
            if (this.website.switchMode) {
              const deptId = this.userInfo.dept_id;
              const roleId = this.userInfo.role_id;
              // debugger;
              if (deptId.includes(',') || roleId.includes(',')) {
                this.loginForm.deptId = deptId;
                this.loginForm.roleId = roleId;
                this.userBox = true;
                this.$store.dispatch('LogOut').then(() => {
                  loading.close();
                });
                return false;
              }
            }
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          })
          .catch(() => {
            loading.close();
            // this.refreshCode();
            if (this.captchaShow) {
              this.captchaShow = false;
            }
            // this.$refs.captchaSliderRef.refresh();
          });
      },
      getTenant() {
        let domain = getTopUrl();
        // 临时指定域名，方便测试
        //domain = "https://www.snszyk.com/";
        info(domain).then((res) => {
          const data = res.data;
          if (data.success && data.data.tenantName) {
            this.tenantMode = false;
            this.loginForm.tenantName = data.data.tenantName;
            this.$parent.$refs.login.style.backgroundImage = `url(${data.data.backgroundUrl})`;
          }
        });
      }
    }
  };
</script>

<style>
  .link-register {
    text-align: center;
  }

  .transparent-dialog .el-dialog__wrapper {
    background-color: transparent;
  }

  .transparent-dialog .el-dialog {
    background-color: transparent;
    box-shadow: none;
  }

  .transparent-dialog .el-dialog__header {
    display: none;
  }

  .transparent-dialog .el-dialog__body {
    padding: 0;
  }
</style>
