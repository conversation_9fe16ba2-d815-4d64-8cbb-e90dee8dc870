<template>
  <div class="alarm-handle">
    <i
      :class="`icon iconfont ${
        item.bizType === 'EQUIPMENT_ABNORMAL' || item.bizType === 'ABNORMAL_CLOSED'
          ? `alarmLevel${item.parseContent.abnormalLevel}`
          : 'db_coll'
      }  `"
    ></i>
    <section>
      <div class="title">
        <span class="_title">
          {{ item.title }}
          <i
            class="ai"
            v-if="
              item.parseContent.abnormalReason && item.parseContent.abnormalReason.includes('0')
            "
          ></i>
        </span>
        <span class="time">
          {{ dayjs(item.parseContent.lastTime).format('YYYY-MM-DD HH:mm:ss') }}
        </span>
      </div>

      <!--  如果是报警 展示内容 -->
      <el-tooltip
        v-if="item.bizType === 'EQUIPMENT_ABNORMAL' || item.bizType === 'ABNORMAL_CLOSED'"
        class="item"
        effect="dark"
        :content="
          item.bizType === 'EQUIPMENT_ABNORMAL'
            ? item.parseContent.pathName +
              '，' +
              '产生了' +
              item.parseContent.abnormalLevel +
              '级异常报警，原因为：' +
              item.parseContent.reasonName +
              ',请及时处理！'
            : item.parseContent.pathName + '异常已关闭'
        "
        placement="top-start"
      >
        <div class="content" v-if="item.bizType === 'EQUIPMENT_ABNORMAL'">
          <span class="device">【{{ item.parseContent.pathName }}】</span>
          产生了
          <span :class="`el-alarmLevel${item.parseContent.abnormalLevel}`"
            >{{ item.parseContent.abnormalLevel }}级</span
          >
          异常报警，原因为：{{ item.parseContent.reasonName }},请及时处理！
        </div>
        <div class="content" v-else>
          <span class="device">【{{ item.parseContent.pathName }}】</span>
          异常已关闭
        </div>
      </el-tooltip>
    </section>

    <div class="button" v-if="item.hasRead === 0">
      <el-button
        v-if="item.bizType === 'EQUIPMENT_ABNORMAL'"
        type="primary"
        size="small"
        @click="handle(item)"
        :loading="loading"
        >{{ '去处理' }}
      </el-button>
      <el-button v-else type="primary" size="small" @click="handle(item)" :loading="loading"
        >知道了
      </el-button>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { message_readMessage } from '@/api/message/message';
import { alarmDetail } from '@/api/alarm-manage';
import { mapGetters } from 'vuex';
export default {
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    ...mapGetters(['permission']),
  },
  data() {
    return {
      dayjs,
      loading: false,
      details: {},
      content: '',
    };
  },
  mounted() {},
  methods: {
    convertBigIntsInObject(obj) {
      if (typeof obj !== 'object' || obj === null) {
        return;
      }
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];
          if (typeof value === 'bigint') {
            obj[key] = value.toString();
          } else if (typeof value === 'object' && value !== null) {
            this.convertBigIntsInObject(value);
          }
        }
      }
    },

    // 点击去处理
    async handle(item) {
      // 点击去处理，直接跳转报警 ，根据报警的详情接口，判断是跳转报警详情还是报警处理
      // 跳转之前，掉已读消息接口
      await this.message_readMessage(item);

      if (item.bizType === 'EQUIPMENT_ABNORMAL') {
        let id = item.parseContent.id.toString();
        this.$router.push({
          path: `/device-exception-manager/view/${id}`,
        });
        this.$emit('closeDialog');
      } else {
        this.$emit('iKnow');
      }
    },

    async message_readMessage(item) {
      this.loading = true;
      try {
        let params = {
          ...item,
        };
        let res = await message_readMessage(params);
        this.loading = false;
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.alarm-handle {
  display: grid;
  grid-template-columns: 70px calc(100% - 150px) 80px;
  padding: 10px;
  border-bottom: 1px solid #d1dbe5 !important;

  .icon {
    width: 48px;
    height: 48px;
  }
}

.content {
  overflow: hidden;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.time {
  margin-left: 25px;
  font-size: 12px;
}

.button {
  position: relative;

  ::v-deep {
    .el-button {
      position: absolute;
      right: 5px;
      bottom: 10px;
    }
  }
}

.device {
  color: #409eff;
}

.title {
  font-weight: 600;
}

::v-deep {
}

.db_coll {
  background: url('../../../assets/images/coll_icon.png') no-repeat;
  background-size: 100% 100%;
}

._title {
  position: relative;
}

.ai {
  position: absolute;
  top: -10px;
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url('../../../assets/images/ai.png') no-repeat;
  background-size: 100% 100%;
}
</style>
