<template>
  <div class="alarm-handle">
    <i :class="`icon iconfont ${'db_coll'}`"></i>
    <section>
      <div class="title" style="padding: 5px 0">
        <span class="_title">{{ item.title }} </span>
        <span class="time">
          {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </span>
      </div>
      <!--  如果是报警 展示内容 -->

      <!--			<el-tooltip-->
      <!--				class="item"-->
      <!--				effect="dark"-->
      <!--				:content="`${item.parseContent.join(',')}, 已离线，请及时处理！`"-->
      <!--				placement="top-start"-->
      <!--			>-->
      <div
        v-if="item.expand"
        style="color: #409eff; cursor: pointer"
        @click="item.expand = !item.expand"
      >
        <div v-for="sen in item.parseContent" :key="sen">
          {{ sen }}
        </div>
      </div>
      <div v-else class="content" style="cursor: pointer" @click="item.expand = !item.expand">
        <span class="device" v-for="sensor in item.parseContent" :key="sensor"
          >【{{ sensor }}】</span
        >
      </div>
    </section>
    <div class="button" v-if="item.hasRead === 0">
      <el-button type="primary" size="small" @click="handle(item)" :loading="loading"
        >{{ '我知道了' }}
      </el-button>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { message_readMessage } from '@/api/message/message';
import { mapGetters } from 'vuex';
import { setCopyContentNew } from '@/utils/util';
export default {
  name: 'default-item',
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    ...mapGetters(['permission']),
  },
  data() {
    return {
      dayjs,
      loading: false,
      details: {},
      content: '',
    };
  },
  mounted() {},
  methods: {
    setCopyContentNew,
    // 点击去处理
    async handle(item) {
      await this.message_readMessage(item);
      this.$emit('iKnow');
    },
    async message_readMessage(item) {
      this.loading = true;
      try {
        let params = {
          ...item,
        };
        await message_readMessage(params);
        this.loading = false;
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.alarm-handle {
  display: grid;
  grid-template-columns: 70px calc(100% - 150px) 80px;
  padding: 10px;
  border-bottom: 1px solid #314162 !important;

  .icon {
    width: 48px;
    height: 48px;
  }
}

.content {
  overflow: hidden;
  color: #efefef;
  font-size: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.time {
  margin-left: 25px;
  color: #fff;
  font-size: 12px;
}

.button {
  position: relative;

  ::v-deep {
    .el-button {
      position: absolute;
      right: 5px;
      bottom: 10px;
    }
  }
}

.device {
  color: #409eff;
}

.title {
  color: #fff;
  font-weight: 600;
}

::v-deep {
  .el-button--primary {
    background: url('../../../assets/images/db_btn.png') no-repeat;
    background-size: 100% 100%;
    border: unset;
  }
}

.db_coll {
  background: url('../../../assets/images/coll_icon.png') no-repeat;
  background-size: 100% 100%;
}

._title {
  position: relative;
}

.ai {
  position: absolute;
  top: -10px;
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url('../../../assets/images/ai.png') no-repeat;
  background-size: 100% 100%;
}
</style>
