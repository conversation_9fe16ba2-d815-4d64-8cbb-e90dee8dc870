import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/szyk-desk/notice/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
    cryptoToken: true,
  });
};

export const remove = ids => {
  return request({
    url: '/szyk-desk/notice/remove',
    method: 'post',
    params: {
      ids,
    },
    cryptoToken: true,
  });
};

export const add = row => {
  return request({
    url: '/szyk-desk/notice/submit',
    method: 'post',
    data: row,
    cryptoToken: true,
  });
};

export const update = row => {
  return request({
    url: '/szyk-desk/notice/submit',
    method: 'post',
    data: row,
    cryptoToken: true,
  });
};

export const getNotice = id => {
  return request({
    url: '/szyk-desk/notice/detail',
    method: 'get',
    params: {
      id,
    },
    cryptoToken: true,
  });
};
