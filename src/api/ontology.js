import request from '@/router/axios';
export const getOntologiesPage = (params) =>
  request({
    url: '/api/szyk-kg/ontologies/page',
    method: 'get',
    params
  });

export const saveOntology = (data) =>
  request({
    url: '/api/szyk-kg/ontologies/save',
    method: 'post',
    data
  });
// 获取本体详情
export const getOntologyDetail = (id) =>
  request({
    url: `/api/llm/api/ontologiesGetById?graphId=${id}`,
    method: 'get'
  });
// 删除本体
export const deleteOntology = (params) =>
  request({
    url: '/api/szyk-kg/ontologies/deleteById',
    method: 'post',
    params
  });
// 图谱分页
export const getGraphPage = (params) =>
  request({
    url: '/api/szyk-kg/graph/page',
    method: 'get',
    params
  });
// 图谱列表
export const getGraphList = (params) =>
  request({
    url: '/api/szyk-kg/graph/list',
    method: 'get',
    params
  });
// 图谱可视化分页列表
export const getGraphViewPage = (params) =>
  request({
    url: '/api/szyk-kg/graph/graph-view-page',
    method: 'get',
    params
  });
// 图谱详情
export const getGraphDetail = (params) =>
  request({
    url: '/api/llm/api/graphGetById',
    method: 'get',
    params
  });

// 图谱保存
export const saveGraph = (data) =>
  request({
    url: '/api/szyk-kg/graph/save',
    method: 'post',
    data
  });

// 图谱删除
export const deleteGraph = (params) =>
  request({
    url: '/api/szyk-kg/graph/deleteById',
    method: 'post',
    params
  });

// 图谱预览
export const previewGraph = (data) =>
  request({
    url: '/api/llm/api/graphPreview',
    method: 'post',
    data
  });

// 新增实体
export const addEntity = (data) =>
  request({
    url: '/api/szyk-kg/manager/vertices/save',
    method: 'post',
    data
  });

// 修改实体
export const editEntity = (data) =>
  request({
    url: '/api/szyk-kg/manager/vertices/update',
    method: 'post',
    data
  });

// 新建关系
export const saveRelation = (data) =>
  request({
    url: '/api/szyk-kg/manager/relation/save',
    method: 'post',
    data
  });

// 删除实体
export const deleteVertices = (data) =>
  request({
    url: '/api/szyk-kg/manager/vertices/delete',
    method: 'post',
    data
  });

// 删除实体
export const delRelation = (data) =>
  request({
    url: '/api/szyk-kg/manager/relation/delete',
    method: 'post',
    data
  });

// 实体和关系删除列表
export const getDelLogList = (params) =>
  request({
    url: '/api/szyk-kg/manager/vertices/operate-log-page',
    method: 'get',
    params
  });

// 实体和关系删除列表--撤回
export const getDelLogRevoke = (params) =>
  request({
    url: '/api/szyk-kg/manager/vertices/recall',
    method: 'get',
    params
  });

// 图谱关系自动匹配
export const postAutoRelation = (data) =>
  request({
    url: '/api/szyk-kg/manager/auto-match-relation',
    method: 'post',
    data
  });

// 图谱下钻
export const drillingGraph = (data) =>
  request({
    url: '/api/szyk-kg/manager/drilling',
    method: 'post',
    data
  });
