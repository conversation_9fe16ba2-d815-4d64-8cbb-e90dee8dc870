import request from '@/axios';

/**
 * 报警管理相关
 * @param url 接口地址
 */

// 列表
export const getAlarmList = params => {
  return request({
    url: '/alarm/page',
    method: 'get',
    params,
  });
};
// 详情页
export const alarmDetail = id => {
  return request({
    url: `/alarm/detail/${id}`,
    method: 'get',
  });
};

// 新增专家诊断
export const addResult = data => {
  return request({
    url: `/diagnosisRecord/submit`,
    method: 'post',
    data,
  });
};

// 诊断详情
export const resultDetail = id => {
  return request({
    url: `/diagnosisRecord/detail/${id}`,
    method: 'get',
  });
};
//关闭报警
export const closeAlarm = data => {
  return request({
    url: `/alarm/close`,
    method: 'post',
    data,
  });
};

// 报警记录分页列表 2023-3-22报废，启用下面的
// export const alarmRecords = (data) => {
// 	return request({
// 		url: `/alarm/alramRecords`,
// 		method: 'get',
// 		params: data
// 	});
// };
export const alarmRecords = data => {
  return request({
    url: `/alarm/alarmRecords`,
    method: 'get',
    params: data,
  });
};

//诊断报告详情
export const diagnosticReport = id => {
  return request({
    url: `/diagnosisRecord/diagnosticReport/${id}`,
    method: 'get',
  });
};
// 编辑诊断报告
export const editDiagnosticReport = data => {
  return request({
    url: `/diagnosisRecord/submitReport`,
    method: 'post',
    data,
  });
};
