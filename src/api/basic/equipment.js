import request from '@/axios';

/**
 * 保存设备3d卡片位置
 * @param data
 * equipmentId：设备id
 * json
 */
export const equipmentSavePosition = data => {
  return request({
    url: '/equipment/savePosition',
    method: 'post',
    data,
  });
};
/**
 *
 * @param data
 * equipmentId：设备id
 * json
 */
export const equipmentSavePosition2D = data => {
  return request({
    url: '/equipment/savePlanarPosition',
    method: 'post',
    data,
  });
};
/**
 *
 * @param params
 * equipmentId：设备id
 */
export const getEquipmentPosition = params => {
  return request({
    url: '/equipment/positionDetail',
    method: 'get',
    params,
  });
};

/**
 * 获取设备2D卡片位置
 * @param params
 * equipmentId：设备id
 */
export const getEquipment2DPosition = params => {
  return request({
    url: '/equipment/planarPositionDetail',
    method: 'get',
    params,
  });
};

/**
 * 门户设备详情运行情况
 * @param params
 * equipmentId：设备id
 */
export const getEquipmentRunningStatistics = params => {
  return request({
    url: '/equipment/equipmentRunningStatistics',
    method: 'get',
    params,
  });
};
/**
 * 门户设备详情统计
 * @param params
 * equipmentId：设备id
 */
export const getEquipmentStatistics = params => {
  return request({
    url: '/equipment/equipmentStatistics',
    method: 'get',
    params,
  });
};

// 异常信息记录
export const getEquipmentExceptionRecord = params => {
  return request({
    url: '/dashboard/equipment-monitor-abnormal',
    method: 'get',
    params,
  });
};

// 设备台账故障列表
export const getEquipmentFaultList = params => {
  return request({
    url: '/szyk-sidas-fault/faultBiz/equipment-fault',
    method: 'get',
    params,
  });
};

// 设备台账累计异常统计
export const getEquipmentExceptionStatistics = params => {
  return request({
    url: '/abnormal/abnormalTotal',
    method: 'get',
    params,
  });
};
// 设备台账传感器信息
export const getEquipmentSensorList = params => {
  return request({
    url: '/sensorInstance/equipment-sensor',
    method: 'get',
    params,
  });
};

// 设备台账故障信息
export const getEquipmentFaultInfo = params => {
  return request({
    url: '/szyk-sidas-fault/dashboard/fault-total',
    method: 'get',
    params,
  });
};
