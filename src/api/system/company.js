import request from '@/router/axios';

// 获取职务列表
export const jobList = (orgId) => {
  return request({
    url: '/api/attila-system/company-job/list',
    method: 'get',
    params: { orgId }
  });
};
// 职务新增编辑
export const jobSubmit = (data) => {
  return request({
    url: '/api/attila-system/company-job/submit',
    method: 'post',
    data
  });
};
// 删除职务
export const jobRemove = (data) => {
  return request({
    url: '/api/attila-system/company-job/remove',
    method: 'post',
    data
  });
};
// 职务批量移除人员
export const removeUser = (positionId, userIdList) => {
  return request({
    url: '/api/attila-system/company-job/remove-user',
    method: 'post',
    data: { positionId, userIdList }
  });
};
// 职务排序
export const sortJob = (dragId, dropId, top) => {
  return request({
    url: '/api/attila-system/company-job/sort',
    method: 'post',
    data: {
      dragId,
      dropId,
      top
    }
  });
};
// 查询职务人员数量
export const userCount = (positionId) => {
  return request({
    url: '/api/attila-system/company-job/user-count',
    method: 'get',
    params: { positionId }
  });
};
// 批量添加人员
export const addUser = (positionId, userIdList) => {
  return request({
    url: '/api/attila-system/company-job/add-user',
    method: 'post',
    data: { positionId, userIdList }
  });
};
// 职务内成员
export const userList = (id, page) => {
  return request({
    url: '/api/attila-system/company-job/user-list',
    method: 'get',
    params: { id, ...page }
  });
};
// 根据职务获取成员
export const jobUserTree = (nodeId, processId) => {
  return request({
    url: '/api/attila-system/company-job/user-tree',
    method: 'get',
    params: { nodeId, processId }
  });
};
