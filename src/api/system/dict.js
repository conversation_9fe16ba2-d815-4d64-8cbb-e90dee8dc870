import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/szyk-system/dict/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getParentList = params => {
  return request({
    url: '/szyk-system/dict/parent-list',
    method: 'get',
    params,
  });
};

export const getChildList = params => {
  return request({
    url: '/szyk-system/dict/child-list',
    method: 'get',
    params,
  });
};

export const remove = ids => {
  return request({
    url: '/szyk-system/dict/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/szyk-system/dict/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/szyk-system/dict/submit',
    method: 'post',
    data: row,
  });
};

export const getDict = id => {
  return request({
    url: '/szyk-system/dict/detail',
    method: 'get',
    params: {
      id,
    },
  });
};
export const getDictTree = () => {
  return request({
    url: '/szyk-system/dict/tree?code=DICT',
    method: 'get',
  });
};

export const getDictionary = params => {
  return request({
    url: '/szyk-system/dict/dictionary',
    method: 'get',
    params,
  });
};
// 获取字典树 /szyk-system/dict-biz/dictionary-tree
export const getDictTree2 = (params) => {
  return request({
    url: '/api/szyk-system/dict-biz/dictionary-tree',
    method: 'get',
    params
  });
};