import request from '@/axios';

export const getAiModelList = (current, size, params) => {
  return request({
    url: '/api/aiModel/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};
export const addOrEditAiModel = (data) => {
  return request({
    url: '/api/aiModel/submit',
    method: 'post',
    data
  });
};

export const deleteAiModel = (ids) => {
  return request({
    url: '/api/aiModel/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const getAiModelDetail = (id) => {
  return request({
    url: `/api/aiModel/detail?id=${id}`,
    method: 'get'
  });
};

export const aiModelApplyApi = (data) => {
  return request({
    url: `/api/aiModel/apply`,
    method: 'post',
    data
  });
};

export const aiModelUpdateStatusApi = (data) => {
  return request({
    url: `/api/aiModel/updateStatus`,
    method: 'post',
    data
  });
};
