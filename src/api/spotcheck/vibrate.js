import request from '@/axios';

export const getList = params => {
  return request({
    url: '/equipmentSpotCheckConfig/page',
    method: 'get',
    params: {
      ...params,
    },
  });
};

export const update = row => {
  return request({
    url: '/equipmentSpotCheckConfig/update',
    method: 'post',
    data: row,
  });
};

export const getSpotCheckRecord = params => {
  return request({
    url: '/equipmentSpotCheckConfig/getSpotCheckRecord',
    method: 'post',
    data: params,
  });
};
