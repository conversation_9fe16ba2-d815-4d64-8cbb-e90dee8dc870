import website from '@/config/website';
import { ElNotification } from 'element-plus';
import store from '@/store';

let countReconnect = website.WS.maxReconnect;
let socket = null;
export function initWebSocket(e) {
  // const wsUri = wsUrl + e; http://*************:32067/
  // const wsUri = wsUrl + e; http://*************:32067/
  const wsUri = `ws://${window.location.host}/websocket/` + e;
  // const wsUri = `ws://*************:32067/websocket/` + e;
  socket = new WebSocket(wsUri); //这里面的this都指向vue
  socket.onerror = webSocketOnError;
  socket.onmessage = webSocketOnMessage;
  socket.onclose = closeWebsocket;
}
let notify = null;
function webSocketOnError(e) {
  notify && notify.close();
  notify = ElNotification({
    title: '',
    // message: 'WebSocket连接发生错误' + e,
    message: 'WebSocket连接失败，请检查网络连接并重试',
    type: 'error',
    duration: 0,
  });
}
function webSocketOnMessage(e) {
  const data = JSON.parse(e.data);
  console.log('websocket消息接收信息..............................：', data);
  if (data.type === 'TIP') {
    ElNotification({
      title: '',
      message: data.title,
      type: 'success',
      duration: 3000,
    });
  } else if (data.type === 'WORK_TODO') {
    // store.commit('SET_MESSAGE_RECEIVED', { bizId: data.bizId, bizType: data.bizType });

    store.commit('SET_MESSAGE_RECEIVED', { messageId: data.id, bizType: data.bizType });
  } else if (data.msgType === 'ERROR') {
    ElNotification({
      title: '',
      message: data.msg,
      type: 'error',
      duration: 0,
    });
  }
}
// 关闭websiocket
function closeWebsocket() {
  console.log('连接已关闭...');
  console.log('尝试重连');
  console.log('123456', store.getters.userInfo.user_id);

  if (this.lockReconnect || countReconnect <= 0) return;
  setTimeout(() => {
    countReconnect--; // 不做限制 连不上一直重连
    // this.initWebSocket();
    initWebSocket(store.getters.userInfo.user_id);
  }, 5 * 1000);
}
function close() {
  socket.close(); // 关闭 websocket
  socket.onclose = function (e) {
    console.log(e); //监听关闭事件
    console.log('关闭');
  };
}
// function webSocketSend(agentData) {
//   socket.send(agentData);
// }

export default {
  initWebSocket,
  close,
};
