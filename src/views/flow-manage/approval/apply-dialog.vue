<template>
  <el-dialog
    title="提示"
    :visible.sync="dialogVisible"
    width="586px"
    :before-close="handleClose"
    append-to-body
    custom-class="apply-confirm"
  >
    <div class="apply-confirm-context-box">
      <el-input
        v-model="formData.applyOpinion"
        type="textarea"
        :maxlength="200"
        show-word-limit
        placeholder="请输入审批意见(非必填)"
      >
      </el-input>
      <div class="upload-file-box" style="padding-top: 12px">
        <file-comp
          v-model="formData.hValue"
          btnsize="small"
          :accept="'.jpg, .jpeg, .png, .gif, .bmp, .doc, .docx, .pdf, .rar, .zip'"
        >
          <template slot="button">
            <span>选择附件</span>
          </template>
          <!-- <span slot="trigger" class="fileTip">
            附件支持word、pdf、图片、zip、rar，单个文件不能超过20MB
          </span> -->
        </file-comp>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button @click="submit" type="primary">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  import fileComp from '@/components/form-build-new/items/file-comp';

  export default {
    components: {
      fileComp
    },
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        formData: {
          applyOpinion: '',
          hValue: []
        }
      };
    },
    watch: {
      dialogVisible() {
        this.formData.applyOpinion = '';
        this.formData.hValue = [];
      }
    },
    methods: {
      updateFileList(arr) {
        this.formData.hValue = arr;
      },
      submit() {
        const file = this.formData.hValue.length ? this.formData.hValue : [];
        this.$emit('submit', { ...this.formData, file });
      },
      handleClose() {
        this.$emit('update:dialogVisible', false);
      }
    }
  };
</script>
