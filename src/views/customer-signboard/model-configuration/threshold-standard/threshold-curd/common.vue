<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <!--  门限管理 包含 列表 新增，编辑 删除-->
      <search ref="search" @search="onsubmit" @reset="reset"
    /></el-col>
    <el-col :span="2" class="add">
      <el-button
        v-hasPermi="['common-threshold-add']"
        icon="plus"
        type="primary"
        @click="add"
        >新增</el-button
      ></el-col
    >

    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      border
      height="calc(100vh - 400px)"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="alarmTypeName"
        label="报警类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="scope">
          {{ scope.row.alarmTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备分类"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="scope">
          {{ scope.row.deviceCategoryName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="quotaType"
        label="指标"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.quotaName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="powerUpper"
        label="功率上限(kW)"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="scope">
          {{ scope.row.powerUpper || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="powerLower"
        label="功率下限(kW)"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="scope">
          {{ scope.row.powerLower || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="firstThreshold"
        label="一级门限"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.thresholdFirstLabel }}
        </template>
      </el-table-column>
      <el-table-column
        prop="secondThreshold"
        label="二级门限"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.thresholdSecondLabel }}
        </template>
      </el-table-column>
      <el-table-column
        prop="thirdThreshold"
        label="三级门限"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.thresholdThirdLabel }}
        </template>
      </el-table-column>
      <el-table-column
        prop="fourthThreshold"
        label="四级门限"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.thresholdFourthLabel }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template v-slot="scope">
          <el-button
            link
            type="primary"
            v-hasPermi="['common-threshold-edit']"
            @click="edit(scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
          <template #reference>
            <el-button
              link
              type="danger"
              v-hasPermi="['common-threshold-del']"
              style="margin-left: 10px"
              >删除</el-button
            >
          </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex flex-justify-center mt-10" style="width: 100%">
        <yk-pagination
          ref="pagination"
          :total="total"
          v-model:page="searchParams.current"
          v-model:limit="searchParams.size"
          @pagination="getList"
          @sizeChange="getList"
        />
      </div>
    <add ref="add" :id="id" @success="addSuccess"></add>
    <!-- slh_查看详情组件 -->
    <threshold-detail ref="detailIndex"></threshold-detail>
  </el-row>
</template>

<script>
  import ThresholdDetail from './common/detail/index.vue';
  import Search from './common/search.vue';
  import Add from './common/add.vue';
  import { ListAll, isoListDele } from '@/api/common';
  import { formatThresholdLabel } from '@/views/customer-signboard/model-configuration/threshold-standard/threshold-curd/utils';
  import { mapGetters}  from 'vuex';
  export default {
    name: '',
    components: {
      Search,
      Add,
      ThresholdDetail, // 查看详情组件
    },
    props: {
      id: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10,
          type: 1,
          vibrationType: 1
        }
      };
    },
    computed: {
      ...mapGetters([
        'systemSelectTenant'
      ])
    },
    mounted() {
      this.getList();
    },
    methods: {
      addSuccess() {
        this.getList(true);
      },
      reset(param) {
        Object.assign(this.searchParams, param);
        this.getList();
      },
      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList(reload) {
        if (reload) {
          this.searchParams.current = 1;
        }
        this.loading = true;
        try {
          let res = await ListAll({
            ...this.searchParams,
            tenantId: this.systemSelectTenant.tenantId
          });
          res.data.data.records = res.data.data.records.map((item) => {
            Object.assign(item, formatThresholdLabel(item.alarmType, item));
            return item;
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      add() {
        this.$refs.add.show();
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      edit(row) {
        this.$refs.add.show(row.id);
      },
      async handleDelete(row) {
        try {
          await isoListDele({ ids: row.id });
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.getList(true);
          this.$emit('refreshTree');
          // 发信号 通知树更新
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>
<style scoped lang="scss">
.el-button--primary.is-link:focus,
.el-button--primary.is-link:hover {
  color:#59acff;
}
.el-button--danger.is-link:focus,
.el-button--danger.is-link:hover {
  color:#f98a8a;
}
.el-button--warning.is-link:focus,
.el-button--warning.is-link:hover {
  color:#f4bd6b;
}
.el-button--success.is-link:focus,
.el-button--success.is-link:hover {
  color:#7fca59;
}
  .add {
    margin-top: -8px;
    margin-bottom: 15px;
  }
  .top-info {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;
  }
</style>
