<template>
  <el-form
    :model="form"
    inline
    label-suffix=":"
    :rules="rules"
    ref="baseForm"
    label-width="130px"
    class="add-info"
  >
    <div>
      <p class="el-base-title">基本信息</p>
      <el-row class="add-info" :gutter="24">
        <el-col :span="12">
          <el-form-item label="应用门限名称" prop="name">
            <el-input
              v-model="form.name"
              maxlength="50"
              clearable
              placeholder="请输入应用门限名称"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用部位类型" prop="applyEquipment">
            <el-select
              v-model="form.applyEquipment"
              placeholder="请选择应用部位类型"
              popper-class="region-cascader"
              clearable
              :disabled="isEdit"
            >
              <el-option
                v-for="dict in serviceDicts.type['equipment_type']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info">
        <el-col :span="12">
          <el-form-item label="应用数据类型" prop="sampleDataType">
            <el-select
              v-model="form.sampleDataType"
              placeholder="请选择应用数据类型"
              popper-class="region-cascader"
              :disabled="isEdit"
            >
              <template v-if="vibrationType === '1'">
                <el-option
                  v-for="dict in vibrateList"
                  :key="dict.dictKey"
                  :label="dict.dictValue"
                  :value="dict.dictKey"
                ></el-option>
              </template>
              <template v-else-if="sampleDataType === 'STRESS'">
                <el-option
                  v-for="dict in stressList"
                  :key="dict.dictKey"
                  :label="dict.dictValue"
                  :value="dict.dictKey"
                ></el-option>
              </template>
              <template v-else>
                <el-option
                  v-for="dict in temperatureList"
                  :key="dict.dictKey"
                  :label="dict.dictValue"
                  :value="dict.dictKey"
                ></el-option>
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用报警类型" prop="alarmType">
            <el-select
              v-model="form.alarmType"
              placeholder="请选择应用报警类型"
              popper-class="region-cascader"
              @change="changeOption"
              :disabled="isEdit"
            >
              <el-option
                v-for="dict in serviceDicts.type['alarm_type']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用指标" prop="quotaType">
            <el-select
              v-model="form.quotaType"
              placeholder="请选择应用指标"
              popper-class="region-cascader"
              :disabled="isEdit"
            >
              <template v-if="vibrationType === '1'">
                <el-option
                  v-for="dict in serviceDicts.type['alarm_index'].filter(
                    (item) => item.value !== 'DTEMP'
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </template>
              <template v-else-if="sampleDataType === 'STRESS'">
                <el-option
                  v-for="dict in serviceDicts.type['alarm_index'].filter(
                    (item) => item.value !== 'DTEMP'
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </template>
              <template v-else>
                <el-option
                  v-for="dict in serviceDicts.type['alarm_index'].filter(
                    (item) => item.value === 'DTEMP'
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <section style="padding-bottom: 60px">
      <p class="el-base-title">应用门限配置</p>
      <el-row class="add-info">
        <el-col :span="12">
          <el-form-item label="一级门限">
            <div style="width: 100%">
              <span v-if="isChuangWai">&lt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowLower"
                v-model="form.firstThresholdLower"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
              <span v-if="isChuangNei">-</span>
              <span v-else-if="isChuangWai">&gt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowUpper"
                v-model="form.firstThresholdUpper"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二级门限">
            <div style="width: 100%">
              <span v-if="isChuangWai">&lt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowLower"
                v-model="form.secondThresholdLower"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
              <span v-if="isChuangNei">-</span>
              <span v-else-if="isChuangWai">&gt;</span>
              <template v-if="isShowUpper">
                <el-input-number
                  :controls="false"
                  :min="0"
                  :max="99999"
                  v-model="form.secondThresholdUpper"
                  placeholder="请输入门限"
                  clearable
                  style="width: 100%"
                ></el-input-number>
              </template>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="三级门限">
            <div style="width: 100%">
              <span v-if="isChuangWai">&lt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowLower"
                v-model="form.thirdThresholdLower"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
              <span v-if="isChuangNei">-</span>
              <span v-else-if="isChuangWai">&gt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowUpper"
                v-model="form.thirdThresholdUpper"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="四级门限">
            <div style="width: 100%">
              <span v-if="isChuangWai">&lt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowLower"
                v-model="form.fourthThresholdLower"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
              <span v-if="isChuangNei">-</span>
              <span v-else-if="isChuangWai">&gt;</span>
              <el-input-number
                :controls="false"
                :min="0"
                :max="99999"
                v-if="isShowUpper"
                v-model="form.fourthThresholdUpper"
                placeholder="请输入门限"
                style="width: 100%"
              ></el-input-number>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </section>
  </el-form>
</template>

<script>
  import { stress, temperature, vibrate } from '@/constant/sampledDataType';
  import { null2Undefined } from '@/utils/util';
  export default {
    serviceDicts: [
      'equipment_type',
      'sampled_data_type',
      'alarm_index',
      'alarm_type'
    ],
    name: 'threshold-vibrate-custom-operate',
    components: {},
    props: {
      baseInfo: {
        type: Object,
        default: () => {
          return { name: undefined, supplier: undefined, model: undefined };
        }
      },
      vibrationType: {
        type: String,
        default: '1'
      },
      sampleDataType: {
        type: String,
        default: ''
      },
      isEdit: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          name: undefined,
          applyEquipment: undefined,
          sampleDataType: undefined,
          alarmType: undefined,
          alarmTypeName: undefined,
          quotaType: undefined,
          firstThresholdUpper: undefined,
          firstThresholdLower: undefined,
          secondThresholdUpper: undefined,
          secondThresholdLower: undefined,
          thirdThresholdUpper: undefined,
          thirdThresholdLower: undefined,
          fourthThresholdUpper: undefined,
          fourthThresholdLower: undefined
        },
        rules: {
          name: [
            {
              required: true,
              message: '请输入应用门限名称',
              trigger: ['blur']
            }
          ],
          // applyEquipment: [
          //   {
          //     required: true,
          //     message: '请选择应用部位类型',
          //     trigger: ['blur']
          //   }
          // ],
          sampleDataType: [
            {
              required: true,
              message: '请选择应用数据类型',
              trigger: ['change']
            }
          ],
          alarmType: [
            {
              required: true,
              message: '请选择应用报警类型',
              trigger: ['change']
            }
          ],
          quotaType: [
            {
              required: true,
              message: '请选择应用指标',
              trigger: ['change']
            }
          ]
        }
      };
    },
    watch: {
      baseInfo(val) {
        Object.keys(val).forEach((key) => {
          this.form[key] = null2Undefined(val[key]);
        });
      }
    },
    computed: {
      isShowUpper() {
        return (
          this.form.alarmTypeName === '水平超限' ||
          this.form.alarmTypeName === '窗内' ||
          this.form.alarmTypeName === '窗外'
        );
      },
      isShowSpace() {
        return (
          this.form.alarmTypeName === '窗内' ||
          this.form.alarmTypeName === '窗外'
        );
      },
      isShowLower() {
        return (
          this.form.alarmTypeName === '水平低限' ||
          this.form.alarmTypeName === '窗内' ||
          this.form.alarmTypeName === '窗外'
        );
      },
      isChuangNei() {
        return this.form.alarmTypeName === '窗内';
      },
      isChuangWai() {
        return this.form.alarmTypeName === '窗外';
      },
      vibrateList() {
        return vibrate;
      },
      temperatureList() {
        return temperature;
      },
      stressList() {
        return stress;
      }
    },
    mounted() {},
    methods: {
      onDictReady(dicts) {
        if (!this.isEdit) {
          // 如果下拉框是一项 则自动选中
          if (
            dicts.type['equipment_type'] &&
            dicts.type['equipment_type'].length === 1
          ) {
            this.form.applyEquipment = dicts.type['equipment_type'][0].value;
          }
          // 应用数据类型
          if (this.vibrationType === '1') {
            if (this.vibrateList.length === 1) {
              this.form.sampleDataType = this.vibrateList[0].dictKey;
            }
          } else {
            if (this.sampleDataType === 'STRESS') {
              this.form.sampleDataType = this.stressList[0].dictKey;
            } else if (this.temperatureList.length === 1) {
              this.form.sampleDataType = this.temperatureList[0].dictKey;
            }
          }
          // 应用指标
          let alarmIndexArr;
          if (this.vibrationType === '1') {
            this.form.alarmIndex = dicts.type['alarm_index'][0].value;
            alarmIndexArr = dicts.type['alarm_index'].filter(
              (item) => item.value !== 'DTEMP'
            );
          } else if (this.sampleDataType === 'STRESS') {
            alarmIndexArr = dicts.type['alarm_index'].filter(
              (item) => item.label !== 'DTEMP'
            );
          } else {
            alarmIndexArr = dicts.type['alarm_index'].filter(
              (item) => item.value === 'DTEMP'
            );
          }
          // 应用报警类型
          if (alarmIndexArr.length === 1) {
            this.form.quotaType = alarmIndexArr[0].value;
          }
          if (
            dicts.type['alarm_type'] &&
            dicts.type['alarm_type'].length === 1
          ) {
            this.form.alarmType = dicts.type['alarm_type'][0].value;
            this.form.alarmTypeName = dicts.type['alarm_type'][0].label;
          }
        }
      },
      changeOption(value) {
        console.log('val', this.serviceDicts.label['alarm_type'][value]);
        this.form.alarmTypeName = this.serviceDicts.label['alarm_type'][value];
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();

        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
        this.form.name = undefined;
        this.form.applyEquipment = undefined;
        this.form.sampleDataType = undefined;
        this.form.alarmType = undefined;
        this.form.alarmTypeName = undefined;
        this.form.alarmIndex = undefined;
        this.form.firstThresholdUpper = undefined;
        this.form.firstThresholdLower = undefined;
        this.form.secondThresholdUpper = undefined;
        this.form.secondThresholdLower = undefined;
        this.form.thirdThresholdUpper = undefined;
        this.form.thirdThresholdLower = undefined;
        this.form.fourthThresholdUpper = undefined;
        this.form.fourthThresholdLower = undefined;
      }
    },

    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    margin-left: 0 !important;
    margin-right: 0 !important;
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }
    }
    .el-col {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
</style>
