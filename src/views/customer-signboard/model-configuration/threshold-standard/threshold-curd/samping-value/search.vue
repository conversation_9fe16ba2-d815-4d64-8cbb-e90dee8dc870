<template>
  <div class="search-content">
    <el-form
      :model="form"
      inline
      label-suffix=":"
      ref="baseForm"
      label-width="75px"
      :label-position="'right'"
      size="small"
    >
      <el-form-item label="指标名称" prop="quotaType">
        <el-select v-model="form.quotaType" placeholder="请选择指标">
          <el-option
            v-for="dict in list"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-button
        icon="el-icon-search"
        size="small"
        type="primary"
        @click="submit"
        >搜索</el-button
      >
      <el-button icon="el-icon-delete" size="small" @click="reset"
        >清空</el-button
      >
    </el-form>
  </div>
</template>

<script>
  import { modelList } from '@/api/common';
  export default {
    serviceDicts: ['discharge_pattern'], // 测量类型
    name: 'search',
    data() {
      return {
        list: undefined,
        form: {
          quotaType: [] // 设备分类
        }
      };
    },
    mounted() {
      this.getDictTree();
    },
    methods: {
      // 查询所有的设备分类
      async getDictTree() {
        try {
          let res = await modelList();
          this.list = res.data.data;
        } catch (e) {
          console.log(e);
        }
      },

      reset() {
        this.$refs['baseForm'].resetFields();
        this.$emit('search', this.form);
      },
      submit() {
        this.$emit('search', {
          ...this.form
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .search-content {
    ::v-deep {
      .el-form {
      }
    }
  }
</style>
