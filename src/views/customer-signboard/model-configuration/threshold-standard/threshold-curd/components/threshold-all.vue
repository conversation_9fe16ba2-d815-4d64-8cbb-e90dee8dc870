<template>
  <el-form
    :model="form"
    inline
    label-suffix=":"
    ref="baseForm"
    label-width="120px"
    :label-position="'right'"
    :key="key"
  >
    <el-row>
      <el-col :span="12">
        <el-form-item label="一级门限" prop="firstThreshold">
          <threshold-single
            v-if="isSingleShow"
            ref="first-single"
            v-model="form.firstThreshold"
            :setData="form.firstThreshold"
            @changeVal="(val) => getvalue(val, 'firstThreshold')"
          ></threshold-single>
          <threshold-interval
            v-if="isIntervalShow"
            ref="first-interval"
            :setData="form.firstThreshold"
            @changeVal="(val) => getvalue(val, 'firstThreshold')"
            v-model="form.firstThreshold"
          ></threshold-interval>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="二级门限" prop="secondThreshold">
          <threshold-single
            v-if="isSingleShow"
            ref="second-single"
            :setData="form.secondThreshold"
            v-model="form.secondThreshold"
            @changeVal="(val) => getvalue(val, 'secondThreshold')"
          ></threshold-single>
          <threshold-interval
            v-if="isIntervalShow"
            ref="second-interval"
            v-model="form.secondThreshold"
            :setData="form.secondThreshold"
            @changeVal="(val) => getvalue(val, 'secondThreshold')"
          ></threshold-interval>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="三级门限" prop="thirdThreshold">
          <threshold-single
            v-if="isSingleShow"
            :setData="form.thirdThreshold"
            ref="third-single"
            v-model="form.thirdThreshold"
            @changeVal="(val) => getvalue(val, 'thirdThreshold')"
          ></threshold-single>
          <threshold-interval
            v-if="isIntervalShow"
            :setData="form.thirdThreshold"
            ref="third-interval"
            v-model="form.thirdThreshold"
            @changeVal="(val) => getvalue(val, 'thirdThreshold')"
          ></threshold-interval>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="四级门限" prop="fourthThreshold">
          <threshold-single
            :setData="form.fourthThreshold"
            v-if="isSingleShow"
            ref="fourth-single"
            v-model="form.fourthThreshold"
            @changeVal="(val) => getvalue(val, 'fourthThreshold')"
          ></threshold-single>
          <threshold-interval
            v-if="isIntervalShow"
            ref="fourth-interval"
            :setData="form.fourthThreshold"
            v-model="form.fourthThreshold"
            @changeVal="(val) => getvalue(val, 'fourthThreshold')"
          ></threshold-interval>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import ThresholdInterval from './interval.vue';
  import ThresholdSingle from './threshold-single.vue';

  export default {
    name: 'AddDevice',
    components: { ThresholdInterval, ThresholdSingle },
    props: {
      alarmType: {
        type: String,
        default: () => {
          return '0';
        }
      },
      setThrshlod: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      alarmType: {
        immediate: true,
        handler(val) {
          if (val) {
            this.alarmTypeVal = val;
          }
        }
      },
      setThrshlod: {
        immediate: true,
        deep: true,
        handler(val) {
          this.form = {
            ...val
          };
        }
      }
    },

    data() {
      return {
        alarmTypeVal: '0',
        form: {
          firstThreshold: undefined,
          secondThreshold: undefined,
          thirdThreshold: undefined,
          fourthThreshold: undefined
        },
        key: 0
      };
    },
    computed: {
      isSingleShow() {
        return this.alarmTypeVal === '0' || this.alarmTypeVal === '1';
      },
      isIntervalShow() {
        return this.alarmTypeVal === '2' || this.alarmTypeVal === '3';
      }
    },
    methods: {
      getvalue(val, type) {
        this.form[type] = { ...val };
      },
      getValueObj() {
        return this.form;
      }
    }
  };
</script>

