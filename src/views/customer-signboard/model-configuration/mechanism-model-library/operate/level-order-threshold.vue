<template>
  <div class="num">
    <el-form
      :model="form"
      inline
      ref="baseForm"
      label-position="right"
      label-width="20px"
    >
      <el-row class="add-info" :gutter="24">
        <el-col :span="23" v-for="(item, idx) in form.list" :key="idx">
          <el-form-item
            class="nums"
            label=" "
            :prop="'list.' + idx + '.param'"
            :rules="{
              required: true,
              message: '请选择参数类型',
              trigger: ['change']
            }"
          >
            <el-select v-model="item.param" placeholder="请选择参数类型">
              <el-option
                v-for="dict in modelParam"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
                :disabled="form.list.some((it) => it.param === dict.value)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="nums"
            label=" "
            :prop="'list.' + idx + '.value'"
            :rules="{
              required: true,
              message: '请输入参数阈值',
              trigger: ['blur']
            }"
          >
            <el-input-number
              v-model="item.value"
              placeholder="请输入参数阈值"
              clearable
              :controls="false"
              :min="0"
              :max="99999"
              @blur="save4(item.value, idx)"
            ></el-input-number>
          </el-form-item>
          <i
            v-if="idx !== 0"
            class="el-icon-remove"
            link
            @click="del(idx)"
          >
          </i>
        </el-col>
      </el-row>
    </el-form>
    <div class="button">
      <el-button  type="primary" @click="add">
        + 继续添加参数配置
      </el-button>
    </div>
  </div>
</template>

<script>
  import { presion4 } from '@/utils/func';

  export default {
    name: 'bearingLibraryIndex',
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      },
      //  下拉字典的值
      modelParam: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },

    watch: {
      detail: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val.paramList) {
            this.form.list =
              this.detail.paramList ||
              [].map((item) => {
                return {
                  param: item.param,
                  value: item.value
                };
              });
          }
        }
      }
    },
    data() {
      return {
        rule: [
          {
            required: true,
            message: '请输入参数阈值',
            trigger: ['blur']
          }
        ],
        form: {
          list: [{ param: undefined, value: undefined }]
        },
        obj: {
          param: undefined,
          value: undefined
        }
      };
    },

    mounted() {},
    methods: {
      save4(val, idx) {
        this.$set(this.form.list[idx], 'value', presion4(val));
      },
      add() {
        this.form.list.push({ ...this.obj });
      },
      async validForm() {
        //  允许不存在
        if (this.form.list.length === 0) {
          return [];
        }
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form.list;
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
        // this.form.list = [{ param: undefined, value: undefined }];
      },
      del(idx) {
        this.form.list.splice(idx, 1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  @import 'oper';
  ::v-deep {
    .el-form-item__content {
      width: calc(100% - 20px);
    }
  }
</style>
