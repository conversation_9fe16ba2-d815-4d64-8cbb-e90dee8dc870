<template>
  <div class="table-content">
    <el-form :model="form" inline label-suffix=":" ref="listForm" size="small">
      <el-table
        ref="point-table"
        class="table"
        :data="form.monitorList"
        style="width: 100%"
        border
        height="calc(100% - 160px)"
        :header-cell-style="{ background: '#fafafa' }"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column prop="sensorCode" label="传感器编码" align="center">
          <template v-slot="scope">
            {{ scope.row.code }}
            <!--            <el-form-item-->
            <!--              :prop="'monitorList.' + scope.$index + '.sensorCode'"-->
            <!--              :rules="repeatRule"-->
            <!--            >-->
            <!--              <el-input v-model="scope.row.sensorCode"> </el-input>-->
            <!--            </el-form-item>-->
          </template>
        </el-table-column>
        <!--        <el-table-column prop="name" label="传感器二维码" align="center">-->
        <!--          <template v-slot="scope">-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column prop="code" label="传感器名称" align="center">
          <template v-slot="{ row }"> {{ row.name }} </template>
        </el-table-column>
        <el-table-column prop="code" label="传感器型号" align="center">
          <template v-slot="{ row }"> {{ row.model }} </template>
        </el-table-column>
        <el-table-column prop="code" label="传感器类型" align="center">
          <template v-slot="{ row }"> {{ row.categoryName }} </template>
        </el-table-column>
        <el-table-column prop="code" label="轴向数" align="center">
          <template v-slot="{ row }">
            {{ zhouXiangShu(row.axisCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="code" label="安装方向" align="center">
          <template v-slot="{ $index, row }">
            <el-form-item
              v-if="
                row.categoryName === '温振一体' || row.categoryName === '应力波'
              "
              :prop="'monitorList.' + $index + '.installDirection'"
              :rules="installDirectionRule"
            >
              <el-select
                v-model="row.installDirection"
                size="mini"
                :disabled="row.oldBind"
              >
                <el-option
                  v-for="dict in serviceDicts.type['sensor_measure_direction']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="编号(应力波)" align="center">
          <template v-slot="{ $index, row }">
            <el-form-item
              v-if="row.categoryName === '应力波'"
              :prop="'monitorList.' + $index + '.number'"
              :rules="numberRule"
            >
              <el-input
                v-model="row.number"
                maxlength="20"
                placeholder="编号"
                size="mini"
                :disabled="row.oldBind"
              ></el-input>
            </el-form-item>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="相位" align="center">
          <template v-slot="{ $index, row }">
            <el-form-item
              v-if="row.categoryName === '电流'"
              :prop="'monitorList.' + $index + '.phase'"
              :rules="phaseRule"
            >
              <el-select
                v-model="row.phase"
                size="mini"
                :disabled="row.oldBind"
              >
                <el-option label="A" value="A"></el-option>
                <el-option label="B" value="B"></el-option>
                <el-option label="C" value="C"></el-option>
                <!--                <el-option-->
                <!--                  v-for="dict in serviceDicts.type['sensor_measure_direction']"-->
                <!--                  :key="dict.value"-->
                <!--                  :label="dict.label"-->
                <!--                  :value="dict.value"-->
                <!--                ></el-option>-->
              </el-select>
            </el-form-item>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template v-slot="scope">
            <el-popover placement="top-start" width="200" trigger="hover">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                "
              >
                <canvas :id="'qrcode-' + scope.row.code"></canvas>
                <el-button @click="print(scope.row)" type="text" size="small"
                  >打印</el-button
                >
              </div>
              <template #reference>
              <el-button
                class="el-icon-receiving"
                type="text"
                size="small"
                >二维码</el-button
              >
              </template>
            </el-popover>
            <el-button
              v-if="!scope.row.oldBind"
              class="el-icon-delete el-button--danger"
              type="text"
              size="small"
              style="margin-left: 5px"
              @click="deleteItem(scope.$index)"
              >移除</el-button
            >
            <el-button
              v-else
              class="el-icon-delete el-button--danger"
              type="text"
              size="small"
              style="margin-left: 5px"
              @click="unbindSensor(scope.$index, scope.row)"
              >解绑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
  import { printQRCode, qrcode } from '@/utils/util';
  import { unbindSensor } from '@/api/device';
  function areInstallDirectionsUnique(array, key = 'installDirection') {
    // 过滤掉 installDirection 为空的对象
    const filteredArray = array.filter((obj) => obj[key]);

    // 提取非空的 installDirection 值
    const installDirections = filteredArray.map((obj) => obj[key]);

    // 利用 Set 来检查唯一性
    const uniqueInstallDirections = new Set(installDirections);

    return installDirections.length === uniqueInstallDirections.size;
  }
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      sensorList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    serviceDicts: ['sensor_measure_direction'],
    data() {
      function codeRepeat(all, val) {
        // 两种判重逻辑 1、本地判重 2、接口返回判重
        let codeArr = all.map((it) => it.code);
        let first = codeArr.indexOf(val);
        let last = codeArr.lastIndexOf(val);
        return first !== last;
      }
      return {
        form: {
          monitorList: []
        },
        installDirectionRule: [
          {
            required: true,
            message: '请选择安装方向',
            trigger: ['change']
          }
        ],
        numberRule: [
          {
            required: true,
            message: '请输入编号',
            trigger: ['change']
          }
        ],
        phaseRule: [
          {
            required: true,
            message: '请选择相位',
            trigger: ['change']
          }
        ],
        repeatRule: [
          {
            required: true,
            message: '请输入传感器编码',
            trigger: ['blur']
          },
          {
            max: 50,
            message: '请输入50位以内字符',
            trigger: ['blur']
          },
          {
            validator: (rule, value, callback) => {
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.monitorList, value)) {
                callback(new Error('编码存在重复!'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      };
    },
    watch: {
      sensorList: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val && val.length) {
            // 先找当前monitorList中有没有配置安装方向
            const monitorList = JSON.parse(JSON.stringify(val));
            monitorList.forEach((it) => {
              this.form.monitorList.forEach((item) => {
                if (item.code === it.code) {
                  it.installDirection = item.installDirection;
                }
              });
            });
            this.form.monitorList = monitorList;
            this.$nextTick(() => {
              qrcode(this.form.monitorList, 'qrcode');
            });
          }
        }
      }
    },
    methods: {
      zhouXiangShu(val) {
        if (val === '3') {
          return '三轴';
        } else if (val === '1') {
          return '单轴';
          // } else if (val === '0') {
          //   return '非振动';
        } else {
          return '-';
        }
      },
      deleteItem(index) {
        this.$confirm('确定要移除传感器吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.monitorList.splice(index, 1);
          this.$emit('deleteItem', this.form.monitorList);
        });
      },
      // 打印
      print(row) {
        printQRCode(row, 'qrcode-' + row.code);
      },
      async validForm() {
        if (!this.form.monitorList.length) {
          this.$message.warning('请选择传感器实例');
          return;
        }
        let v = await this.$refs['listForm'].validate();
        if (v) {
          if (
            !areInstallDirectionsUnique(
              this.form.monitorList.filter(
                (item) => item.categoryName === '温振一体'
              )
            )
          ) {
            this.$message.warning('温振传感器不允许有相同的安装方向');
          } else if (
            !areInstallDirectionsUnique(this.form.monitorList, 'number')
          ) {
            this.$message.warning('不允许有相同的编号');
          } else if (
            !areInstallDirectionsUnique(this.form.monitorList, 'phase')
          ) {
            this.$message.warning('不允许有相同的相位');
          } else {
            return this.form.monitorList;
          }
        } else {
          return false;
        }
      },

      resetForm() {
        this.form.monitorList = [];
      },
      // 点击删除傳感器
      unbindSensor(index, row) {
        if (row.id) {
          this.$confirm('确定要解绑传感器吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              unbindSensor({ code: row.code, id: row.id }).then((res) => {
                if (res.data.code === 200) {
                  // this.form.monitorList[index].ondBind = false;
                  this.form.monitorList.splice(index, 1);
                  this.$message.success('操作成功');
                  // this.$emit('bindSensorNumber', this.form.monitorList.length);
                  this.$emit('deleteItem', this.form.monitorList, true);
                }
              });
            })
            .catch(() => {
              // this.$message({
              //   type: 'info',
              //   message: '已取消删除'
              // });
            });
        } else {
          this.form.monitorList.splice(index, 1);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 15px;
  }

  // ::v-deep {
  //  .el-form,
  //  .el-table {
  //    height: 100%;
  //  }
  // }
</style>
