<template>
  <el-dialog
    title="选择轴承"
    :visible.sync="visible"
    width="65%"
    custom-class="_dialogStyle"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <bearing-library
      v-if="visible"
      ref="bearingLibrary"
      @select="select"
    ></bearing-library>
  </el-dialog>
</template>

<script>
  import BearingLibrary from './bearing-list/index.vue';
  export default {
    components: { BearingLibrary },
    props: {},
    data() {
      return {
        key: 0,
        queryParams: {
          size: 10,
          current: 1
        },
        visible: false,
        isShow: undefined
      };
    },
    watch: {},
    methods: {
      show() {
        this.visible = true;
      },
      // 选中的数据
      select(val) {
        this.$emit('selectVal', val);
        this.visible = false;
      },

      closed() {
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-dialog__body {
      padding: 0 20px 20px 20px;
      height: 65vh;
    }
  }
</style>
