<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="70%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info
        ref="info"
        :initData="detail"
        :id="id"
        @eqId="
          (ids) => {
            this.eqId = ids;
          }
        "
      ></info>
      <div class="oper_btn">
        <el-button
          class="el-icon-circle-plus-outline"
          size="small"
          type="primary"
          @click="submit(true)"
          :loading="loading"
        >
          保存并定位该设备</el-button
        >
        <btn type="save" @click="submit(false)" :loading="loading"></btn>
        <btn
          type="close"
          @click="
            () => {
              this.visible = false;
            }
          "
        ></btn>
      </div>
    </section>
  </dialog-drawer>
</template>

<script>
  import Info from './add-info.vue';
  import { addEditDevice, viewDevice } from '@/api/device';
  export default {
    name: 'AddDevice',
    components: {
      Info
    },
    props: {
      id: {
        type: String,
        default: ''
      },
      level: {
        type: Number,
        required: true
      }
    },
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        sceneId: ''
      };
    },
    watch: {},
    methods: {
      async getDetail(equipmentId) {
        this.loading = true;
        try {
          const res = await viewDevice(equipmentId);
          let data = res.data.data;
          this.setData(data);
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(equipmentId) {
        this.visible = true;
        this.$nextTick(() => {
          this.$refs.info.getTree();
        });
        if (equipmentId) {
          this.edit = true;
          this.getDetail(equipmentId);
        }
      },

      setData(data) {
        this.detail = { ...data };
        this.list = data.monitorList;
        this.eqId = data.id;
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.$refs['info'].resetForm();
        this.detail = this.$options.data().detail;
        this.visible = false;
      },
      async submit(isPath) {
        let params = await this.$refs['info'].validForm();
        Promise.all([params]).then((res) => {
          if (res.some((it) => it === false)) {
            return;
          }
          this.devicePath = { ...res[0] }.devicePath;
          delete res[0].devicePath;
          this.save(
            {
              equipmentId: this.id,
              level: this.level + 1,
              flag: this.edit ? 1 : 0,
              ...res[0]
            },
            isPath
          );
        });
      },

      // 提交设备
      async save(params, isPath) {
        this.loading = true;
        try {
          const res = await addEditDevice(params);
          if (res.data.msg === '操作成功') {
            const devId = res.data.data;
            this.$message.success('操作成功');
            const path = (this.devicePath + ',' + devId).split(',');
            const uniquePath = Array.from(new Set(path)).join(',');
            this.$emit('success', isPath ? uniquePath : undefined);
            this.visible = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
          let index = e.message.indexOf('重复的传感器编码:');
          if (index === 0) {
            this.repeatHandle(e.message);
          }
        }
      },
      repeatHandle(msg) {
        msg = msg.substring(9);
        this.$refs.list.repeatHandle(msg);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
