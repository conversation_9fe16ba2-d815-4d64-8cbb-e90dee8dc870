//波形图chart样式
.top {
  display: grid;
  grid-template-columns: auto 30px;
  width: 100%;
  height: 33px;
  //background: #2166E5;
  //background: #2caffe;
  background: #4782fa;
  .zoom {
    display: flex;
    justify-content: flex-end;
  }

  & > span {
    color: #fff;
    font-size: 13px;
    line-height: 33px;
  }

  & > span:nth-child(1) {
    display: inline-block;
    text-align: center;
  }

  & > span:nth-child(2) {
    display: flex;
    display: inline-block;
    justify-content: flex-end;
    width: 25px;
    height: 25px;
    margin-top: 4px;
    margin-right: 5px;
    text-align: right;
    background-size: contain;
    cursor: pointer;
  }

  .fullscreen {
    background: url('/img/charts/fullScreen.png');
  }

  .exitScreen {
    background: url('/img/charts/outScreen.png');
  }
}

.bottom {
  display: grid;
  grid-template-columns: 33px auto;
  height: calc(100% - 33px);

  //border-bottom: 4px solid #484849;

  .left {
    width: 33px;
    height: 100%;
    background: #eeeff3;
    .single {
      background: url('/img/charts/charts.png') no-repeat;
      background-size: contain !important;
    }

    .double {
      background: url('/img/charts/doublevernier.png') no-repeat;
      background-size: contain;
    }

    .multiplication {
      background: url('/img/charts/multifrequencycursor.png') no-repeat;
      background-size: contain;
    }

    .side {
      background: url('/img/charts/sidefrequencycursor.png') no-repeat;
      background-size: contain;
    }
    .marked {
      background: url('/img/charts/bz.png') no-repeat;
      background-size: contain;
    }
    .identifying {
      background: url('/img/charts/sb.png') no-repeat;
      background-size: contain;
    }
    .icon7 {
      cursor: pointer;
      display: inline-block;
      width: 30px;
      height: 30px;
      background: url('/img/charts/zoom.png') no-repeat;
      background-size: contain;
    }
  }

  //三个图形分别的高度
  #effective_value {
    height: 100%;
    background: #fff;
  }

  #time_domain_plot {
    height: 100%;
    background: #fff;
  }

  #frequency_domain {
    height: 100%;
    background: #fff;
  }
}

::v-deep.highcharts-container {
  height: 100% !important;
}
// 隐藏 恢复缩放按钮
::v-deep {
  .highcharts-reset-zoom {
    //display: none !important;
  }
}
.chartComponent {
  height: 100%;
}
