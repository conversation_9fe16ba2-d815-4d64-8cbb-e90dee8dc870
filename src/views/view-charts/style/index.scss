._container {
  height: 100%;
  background: url('/img/bg/baseContainter.png') no-repeat;
  background-size: 100% 100%;

  .content {
    padding: 10px 25px 0;
  }
}

._containerPadding {
  display: grid;
  height: 100%;
}

$height3: calc(100% / 3);
$height2: calc(100% / 2);

.smallStatus3 {
  grid-template-rows: $height3 $height3 $height3 !important;
}

.smallStatus2 {
  grid-template-rows: $height2 $height2 !important;
}

.bigStatus {
  grid-template-rows: calc(100%) !important;
}
.bigStatusWall {
  grid-template-rows: calc(100%) !important;
  grid-template-columns: calc(100% - 245px) 245px !important;
}
._empty {
  padding: 20px;
  color: #fff;
}

.charts {
  height: 100%;
}

.btnView {
  padding-left: 20px;
}

.fixedClassName {
  position: fixed !important;
  right: 0px !important;
  bottom: 0px !important;
  //left: calc(20.8% + 25px) !important;
  left: 0;
  box-sizing: border-box !important;
  z-index: 1000;
  //width: calc(100% - 20.8% - 25px) !important;
  //height: calc(100% - 160px) !important;
  width: 100%;
  height: 100%;
  margin: 0px !important;
}

// 波形图分页样式
::v-deep {
  .el-pagination {
    width: 100% !important;
  }
  .el-pagination .el-select .el-input {
    width: 80px;
    .el-input__inner {
      padding-right: 0 !important;
      text-align: left;
    }
  }
  thead tr th .el-checkbox {
    display: none;
  }

  .el-pagination__sizes {
    margin: 0 !important;
  }
  .btn-prev,
  .btn-next {
    min-width: 0px !important;
  }
  ._pagination-container {
    bottom: 0px !important;
  }
  .el-pagination span:not([class*='suffix']) {
    min-width: 18px !important;
  }
}
.condition {
  position: relative;
}
.condition-com {
  position: absolute;
  top: 0;
  z-index: 100;
  right: 45px;
}
