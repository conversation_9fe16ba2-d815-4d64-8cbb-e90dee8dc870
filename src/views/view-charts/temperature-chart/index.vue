<template>
  <!--  有效值，时域，频域波形图-->
  <div class="charts" v-loading="loading">
    <div :class="'_containerPadding ' + screenStatus" ref="_containerPadding">
      <div class="container" @click="clickMap(chartsName[0])">
        <charts
          :ref="chartsName[0]"
          :chartsData="characterDataList"
          :chartsName="chartsName[0]"
          :flag_time="true"
          :main_xy_point="headerInfo"
          :btnArr="[{ value: 'single', label: '单游标' }]"
          :tickShowData="tickIntervalNum"
          :isFullScreen="false"
          @clickMap="clickMap(chartsName[0])"
        ></charts>
      </div>
    </div>
  </div>
</template>

<script>
import Charts from '../component/oscillograph.vue';
import { eigenvalueWaveForm } from '@/api/device-info-api';
import CustomEmpty from '@/components/custom-empty.vue';
import { mapGetters } from 'vuex';
export default {
  components: { Charts, CustomEmpty },
  props: {
    timeObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    currentTabName: {
      type: String,
      default: () => {
        return '';
      },
    },
  },
  data() {
    return {
      chartItself: 'temperature',
      chartsName: ['temperature'],
      loading: false,
      screenStatus: 'bigStatus', // 大屏小屏展示状态
      characterDataList: [], // 特征值
      tickIntervalNum: {},
      headerInfo: {},
      waveConfigId: undefined,
      monitorId: undefined,
      res: [],
      flag: false,
    };
  },
  computed: {
    ...mapGetters(['getWaveFormObj']),
  },
  watch: {
    'getWaveFormObj.id': {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.waveConfigId = this.getWaveFormObj.id; // 节点id
            this.monitorId = this.getWaveFormObj.monitorId; // 测点id
            this.sampleDataType = this.getWaveFormObj.sampleDataType; // 采样数据类型
            this.$nextTick(async () => {
              await this.getWaveData();
            });
          });
        } else {
          // 移除键盘事件
          this.chartItself = undefined;
          this['characterDataList'] = [
            {
              x_point: [],
              y_point: [],
              waveform: [],
            },
          ]; // 主图谱
        }
      },
    },
    timeObj: {
      immediate: true,
      handler(val) {
        this.$nextTick(async () => {
          if (val.isChange && this.getWaveFormObj.currentTabName === 'TemperatureChart') {
            await this.getWaveData();
          }
        });
      },
    },
  },
  mounted() {},
  methods: {
    keyDowns(e) {
      this.$refs[this.chartItself].keyDown(e);
    },
    clickMap(type) {
      this.chartItself = type;
      this.$emit('chartItself', type);
    },
    async getWaveData() {
      this.loading = true;
      try {
        let params = {
          waveId: this.waveConfigId,
          monitorId: this.monitorId,
          ...this.timeObj,
        };
        let data = await eigenvalueWaveForm(params);
        this.res = data.data.data;
        if (data.data.data) {
          this.transData(this.res['dataList']);
        }

        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },

    // 处理数据
    transData(val) {
      if (val && val.length > 0) {
        let timeArray = [];
        let data = val.map(item => {
          timeArray.push(new Date(item.originTime).getTime());
          return [new Date(item.originTime).getTime(), Number(item.value)];
        });
        this.tickIntervalNum = {
          min: timeArray[0],
          max: timeArray[timeArray.length - 1],
          count: 5,
        }; // 计算出来的是没一个间隔
        this['characterDataList'] = [
          {
            x_point: data[data.length - 1][0],
            y_point: data[data.length - 1][1],
            waveform: data,
          },
        ]; // 主图谱
        // 头部信息
        this.headerInfo = {
          x: data[data.length - 1][0],
          y: data[data.length - 1][1],
        };
        this.flag = true;
      } else {
        this['characterDataList'] = [
          {
            x_point: 0,
            y_point: 0,
            waveform: [],
          },
        ]; // 主图谱
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import '../style/index';
</style>
