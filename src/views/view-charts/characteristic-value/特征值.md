## 特征值模型
+ 峰值（code：MMN01）
+ 峰峰值（code:MMN02）
+ 裕度(code:MMN03)
+ 歪度(code:MMN04)
+ 峭度(code:MMN05)
x轴时间 为 originTime


	keyUp(e) {
				let el =
					e || event || window.event || arguments.callee.caller.arguments[0];
				if ((el && el.keyCode === 37) || (el && el.keyCode === 39)) {
					if (
						this.chartsName === 'effective_value' ||
						this.chartsName === '峰值' ||
						this.chartsName === '峰峰值' ||
						this.chartsName === '裕度' ||
						this.chartsName === '歪度' ||
						this.chartsName === '峭度'
					)
						this.emitSingle();
				}
			}

#以上方法 峰值，峰峰值.. 判断是写死的，页面上其实是存在接口的的，键盘抬起事件，如果指标更新，记得更改这个地方
