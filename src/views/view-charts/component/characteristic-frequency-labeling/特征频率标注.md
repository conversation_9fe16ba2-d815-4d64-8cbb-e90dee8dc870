## 此文件夹内涵特征频率标注弹窗 说明
2.5.0版本
齿轮啮合频率   转速/60 * 齿数

轴承倍频计算过程
 选择轴承之后，拿到轴承的缺陷频率， 然后选择转速， 四个缺陷频率的 1X = 转速/60 * 当前选择的轴承的缺陷频率




2.5.0版本之前---------------------------
* 1.特征频率标注计算过程
* 点击 *60 字样，用【转速】输入框的值 *60 
### 计算啮合频率过程：
+ 点击计算的时候，提示先选择齿数
+ 选择齿数之后计算啮合频率   啮合频率 = 齿数 * 转频    转频 = 转速/60   ==== 啮合频率 = 齿数*转速/60   
+ 而我们的啮合频率 是 求当前齿数的啮合频率  转频  =   （第一个齿数/最后一个齿数） *当前选择的齿数 * 转速  
+ 当前啮合频率 = （第一个齿数/最后一个齿数） *当前选择的齿数 * 转速  /60 

### 特征频率怎么计算
+ 首选选择右边的轴承 得到轴承数据
+ 然后查看是不是选择了齿数，如果没选择，提示，选择齿数计算
+ 计算：特征频率 = 转速*第一个齿数值/当前选择的这个齿数值 /60 * 当前选择的轴承特征频率 对应系数

## 2023-3-30 验收完成补充纠正---------------------
#### 齿轮啮合频率 = 转速*齿数/60
#### 轴承缺陷频率 = 转速/60*系数

+ 计算出来的轴承缺陷频率 添加轴承的厂商和型号
+ 去掉2,3,4,5x 的频率
+ 弹窗在有限空间内 变窄变瘦
+ 齿数多选 ，计算多重啮合频率，同时计算不同齿数情况下的轴承缺陷频率  但是，转速不能变
+ 整理波形图 单位，约定哪些有单位，哪些不存在单位（eg:裕度歪度，翘度，没有单位 波峰系数 ）
+ 识别范围的计算，范围频率  =  识别范围/2 - 原始值 + 识别范围/2
+ 增加 计算齿轮啮合频率 频率值，幅值，原始值 = 当前轴承齿轮啮合频率的值  （位置：放在轴承的最上面）
+ 把识别和标注按钮添加到包络里面
+ 

