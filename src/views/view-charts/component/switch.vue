<template>
  <div>
    <el-checkbox-group v-model="checked" @change="handleChange">
      <el-checkbox
        v-for="(o, index) in options"
        :key="o.id"
        :label="o.id"
        :style="`color:${color[index]}`"
        :class="`box${index}`"
        >{{ o.label
        }}{{
          o.alarmType === 0 || o.alarmType === 1
            ? `y=[${o.value || '-'}]`
            : `y=[${o.value[1] || '-'},${o.value[0] || '-'}  ]`
        }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },

  data() {
    return {
      color: ['#3896f9', '#ecae40', '#ff5c75', '#f72c30'],
      checked: [],
    };
  },
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.checked = val.map(i => {
            return i.id;
          });
        }
      },
    },
  },
  created() {},
  methods: {
    handleChange(val) {
      let cur = this.options.filter(i => val.includes(i.id));
      this.$emit('changeBox', cur);
    },
  },
};
</script>

<style scoped lang="scss">
.box0 {
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #3896f9 !important;
  }
}
.box1 {
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #ecae40 !important;
  }
}
.box2 {
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #ff5c75 !important;
  }
}
.box3 {
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #f72c30 !important;
  }
}
</style>
