<template>
  <!--  有效值，时域，频域波形图-->
  <div class="charts" v-loading="loading">
    <div
      class="_containerPadding smallStatus2"
      ref="_containerPadding"
      v-if="modelCodeList.length !== 0"
    >
      <div
        class="container"
        v-for="item in modelCodeList"
        :key="item.name"
        @click="clickMap(item.name)"
      >
        <charts
          :ref="item.name"
          :chartsName="item.name"
          :chartsData="[item]"
          :btnArr="[{ value: 'single', label: '单游标' }]"
          :y_unit="''"
          :flag_time="true"
          :tickShowData="item.tickIntervalNum"
          :wavaName="item.label"
          :main_xy_point="{ x: item.x_point, y: item.y_point }"
          @clickMap="clickMap(item.name)"
        ></charts>
      </div>
    </div>
    <custom-empty v-else></custom-empty>
  </div>
</template>

<script>
import CustomEmpty from '@/components/custom-empty.vue';
import Charts from '../component/oscillograph.vue';
import { trendPlotForm } from '@/api/device-info-api';
import { crestCoefficient, trendCode, fullScreen } from '../util/util';
import { mapGetters } from 'vuex';
export default {
  components: { Charts, CustomEmpty },
  props: {
    timeObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    currentTabName: {
      type: String,
      default: () => {
        return '';
      },
    },
  },

  data() {
    return {
      chartItself: undefined,
      crestCoefficient,
      trendCode,
      modelCodeList: [],
      dataList: [],
      f_cutoff: undefined, // 包络图的截止频率
      flag: false, // w判断第一个文件返回的是不是null
      waveConfigId: undefined, // 查找波形图参数
      monitorId: undefined,
      iotCode: undefined, // 波形图参数
      sampleFrequency: undefined, // 采样频率
      cutoffFreq: undefined, // 截止频率
      typeOfVibration: undefined, // 振动类型
      loading: false,
      freqDomainWaveForm: [], // 频域
      envelopeWaveForm: [], // 包络
      timeChange: false, // 监听的时间是不是变化了
    };
  },
  watch: {
    currentTabName: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val === 'TendencyChart') {
          this.waveConfigId = this.getWaveFormObj.id; // 节点id
          this.monitorId = this.getWaveFormObj.monitorId;
          this.modelCodeList = [];
          this.getWaveData();
        }
      },
    },
  },
  mounted() {},
  computed: {
    ...mapGetters(['getWaveFormObj']),
  },
  methods: {
    keyDowns(e) {
      console.log('波峰系数 + 趋势图', this.chartItself);
      this.$refs[this.chartItself][0].keyDown(e);
    },
    clickMap(type) {
      this.chartItself = type;
      this.$emit('chartItself', type);
    },
    // changeData(val) {
    //   this.waveConfigId = this.$route.params.id; // 节点id
    //   this.monitorId = this.$route.params.monitorId; // 测点id
    //   this.modelCodeList = [];
    //   if (val === 'CrestCoefficient') {
    //     // 【注意】这个是波峰系数，代码未做修改，如果后期将波形系数增加回来，记得修改代码逻辑。。。。
    //     // 波峰系数
    //     this.$nextTick(() => {
    //       this.crestCoefficient.forEach(code => {
    //         this.getWaveData(code);
    //       });
    //     });
    //   } else if (val === 'TendencyChart') {
    //     this.$nextTick(() => {
    //       // this.trendCode.forEach(code => {
    //       //   this.getWaveData(code);
    //       // });
    //       this.getWaveData();
    //     });
    //   } else {
    //     // 移除键盘事件
    //     this.chartItself = undefined;
    //   }
    // },

    // 波形图展示，1.如果第一次返回多个波形图，则第一个组件展示数组第一个波形数据  2.将删除头部的数组付给第二个波形图
    async getWaveData() {
      this.loading = true;
      try {
        let params = {
          waveId: this.waveConfigId,
          monitorId: this.monitorId,
          ...this.timeObj,
        };
        let res = await trendPlotForm(params);
        let dts = res.data.data;
        let str = ['increaseValue', 'increaseRatio'];
        this.modelCodeList = str.map((it, idx) => {
          let data = (dts['time'] || []).map((item, index) => {
            return [new Date(item).getTime(), Number(dts[it][index])];
          });
          return {
            ...this.trendCode[idx],
            x_point: data.length !== 0 ? data[data.length - 1][0] : 0,
            y_point: data.length !== 0 ? data[data.length - 1][1] : 0,
            waveform: data,
            tickIntervalNum: {
              min: data[0][0],
              max: data[data.length - 1][0],
              count: 5,
            },
          };
        });
        console.log('数据展示', this.modelCodeList);
        // let list = res.data.data.modelDataList;
        // let increaseRatio = res.data.data.increaseRatio;
        // let increaseValue = res.data.data.increaseValue;
        // let time = res.data.data.time;
        // let data = (time || []).map((item, index) => {
        //   return [new Date(item).getTime(), Number(increaseValue[index])];
        // });
        //
        // code = {
        //   ...code,
        //   x_point: data.length !== 0 ? data[data.length - 1][0] : 0,
        //   y_point: data.length !== 0 ? data[data.length - 1][1] : 0,
        //   waveform: data,
        //   tickIntervalNum: {
        //     min: data[0][0],
        //     max: data[data.length - 1][0],
        //     count: 5,
        //   },
        // };

        // this.modelCodeList.push(code);
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import '../style/index';
</style>
