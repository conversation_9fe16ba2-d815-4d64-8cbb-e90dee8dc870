 

频域振幅（Magnitude）数组：freqDomainWaveform = [0.12, 0.21, ...]
频域相位（Phase）数组：freqPhaseWaveform = [82, 29, ...]
开始频率：start_freq = 0.1，log_start_freq = Math.log10(start_freq)
截止频率：end_freq = 1000，log_end_freq = Math.log10(end_freq)
数组长度：length = 频域振幅、相位数组的长度。

二 绘制流程

构造波特图中振幅、相位图的横坐标数组：frequency = [ ]

for (int i = 0; i < length; i++) {   double logFreq = log_start_freq + (log_end_freq - log_start_freq) * i / (length - 1);   frequency[i] = logFreq;

画波特图的振幅频率图（上半部分）：
对振幅数组进行如下处理：
for (int i = 0; i < length; i++) {   //振幅波特图的纵坐标 & 横坐标   freqDomainWaveform[i] = 20 * Math.log10(freqDomainWaveform[i]); }



然后分别将freqDomainWaveform和frequency作为纵坐标和横坐标的数据绘图。

画波特图的相位频率图（下半部分）：

将freqPhaseWaveform和frequency作为纵坐标和横坐标的数据绘图。

 
