<template>
  <!--  有效值，时域，频域波形图-->
  <div class="charts" v-loading="loading">
    <div :class="'_containerPadding ' + screenStatus" v-if="flag" ref="_containerPadding">
      <div class="container" @click="clickMap(chartsName[0])">
        <charts
          @clickMap="clickMap(chartsName[0])"
          :ref="chartsName[0]"
          :chartsData="characterDataList"
          :chartsName="chartsName[0]"
          :flag_time="true"
          :tickShowData="tickIntervalNum"
          :btnArr="[{ value: 'single', label: '单游标' }]"
          @changePointLine="changePointLine"
          :main_xy_point="{ x: main_x_timestamp, y: main_y_data }"
          @main_xyPoint="main_xyPoint"
          @GLTJSJ="GLTJSJ"
          @QXGLTJSJ="QXGLTJSJ"
          @addMark="addMark"
          @cancelMark="cancelMark"
          :y_max="y_maxMin.max"
          :y_min="y_maxMin.min"
        ></charts>
        <!-- 筛选条件 振动波形才展示，非振动不展示  全部/波形-->
        <condition
          v-if="getWaveFormObj.vibrationType === '1'"
          class="condition-com"
          ref="condition"
          @handleChange="getChange"
          :change_showWave="showWave"
        ></condition>
      </div>
      <div class="container" v-loading="auxiliaryLoading" @click="clickMap(chartsName[1])">
        <charts
          @clickMap="clickMap(chartsName[1])"
          :ref="chartsName[1]"
          :chartsData="timeDomainWaveForm"
          :chartsName="chartsName[1]"
          :btnArr="[
            { value: 'single', label: '单游标' },
            { value: 'double', label: '双游标' },
          ]"
          :main_xy_point="{ x: main_x_timestamp, y: main_y_data }"
          :x_unit="'ms'"
          :x_max="x_max_timeDomain"
        ></charts>
        <!--        :main_xPoint="timeDomainWaveForm[0].x_point"-->
      </div>

      <div class="container" v-loading="auxiliaryLoading" @click="clickMap(chartsName[2])">
        <charts
          @clickMap="clickMap(chartsName[2])"
          :ref="chartsName[2]"
          :chartsData="freqDomainWaveForm"
          :chartsName="chartsName[2]"
          :btnArr="[
            { value: 'single', label: '单游标' },
            { value: 'multiplication', label: '倍频游标' },
            { value: 'side', label: '边频游标' },
            { value: 'marked', label: '特征频率标注' },
            { value: 'identifying', label: '特征频率标识' },
          ]"
          :main_xy_point="{ x: main_x_timestamp, y: main_y_data }"
          :x_unit="'Hz'"
          :x_max="x_max_freqDomain"
        ></charts>
      </div>
    </div>

    <custom-empty v-else></custom-empty>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex';
import Charts from '../component/oscillograph.vue';
import {
  eigenvalueWaveForm,
  timeAndFreqWave,
  addMark,
  cancelMark,
  getStopLineValue,
} from '@/api/device-info-api';
import CustomEmpty from '@/components/custom-empty.vue';
import { getAlarmColor } from '@/views/view-charts/util/util';
import Condition from '../component/condition.vue';
import { convertFileUrl, fetchTextFile } from '@/utils/util';
import dayjs from 'dayjs';
export default {
  components: { Charts, CustomEmpty, Condition },
  props: {
    currentTabName: {
      type: String,
      default: () => {
        return '';
      },
    },
    timeObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    'getWaveFormObj.id': {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val && this.getWaveFormObj.isSameId) {
          return;
        }
        // 如果是从属性取参数 就不走路由
        this.SET_CHARACTER_X(undefined); // 先将时间清空
        this.SET_CHARACTER_Y(undefined); // 将时间存起来
        if (val) {
          this.waveConfigId = val; // 节点id
          this.monitorId = this.getWaveFormObj.monitorId;
          this.samplingFreq = Number(this.getWaveFormObj.samplingFreq); // 采样频率
          this.samplingPoints = Number(this.getWaveFormObj.samplingPoints); // 采样点数
          if (this.getWaveFormObj.currentTabName === 'RmsFrequencyDomains') {
            this.showWave = '1';
            this.$nextTick(() => {
              if (this.waveConfigId) {
                this.getWaveData();
              } else {
                this.flag = false;
                this.getJumpToAddWave();
              }
            });
          }
        } else {
          this.flag = false;
          this.getJumpToAddWave();
        }
      },
    },

    timeObj: {
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          if (val.isChange && this.currentTabName === 'RmsFrequencyDomains') {
            this.getWaveData();
          }
        });
      },
    },
  },
  data() {
    return {
      lastRequestedId: undefined,
      markedPoints: [], // 存放已经标注的点的对象 isMarked = 0 未标注 1 = 标注
      x_categories: [],
      rightMenuShow: true,
      x: 0, // 记录鼠标点击位置的x坐标
      y: 0, // 记录鼠标点击位置的y坐标
      showWave: undefined, // 默认是波形
      chartItself: 'effective_value',
      f_jg: 0, // 频域图的间隔
      alarmDataList: [], // 报警数据点
      x_max_timeDomain: 0, // X轴展示最大值
      x_max_freqDomain: 0,
      flag: false, // w判断第一个文件返回的是不是null
      tickIntervalNum: {}, // 存放特征值展示的时间间隔
      waveConfigId: undefined, //
      monitorId: undefined, // 测点id
      samplingFreq: undefined, // 采样频率
      samplingPoints: undefined, //采样点数
      loading: false,
      screenStatus: 'smallStatus3', // 大屏小屏展示状态
      characterDataList: [], // 有效值
      characterOrigin: [], // 存放有效值原始值
      timeDomainWaveForm: [{ x_point: 0 }], // 时域
      freqDomainWaveForm: [], // 频域
      auxiliaryLoading: false, // 切换数据时域图和频域图的loading
      main_x_timestamp: undefined, //用于保存第一个图的x轴时间 ，方便时域图使用
      main_y_data: undefined, //用于保存第一个图的y轴时间 ，方便时域图使用
      chartsName: ['effective_value', 'time_domain_plot', 'frequency_domain'], //
      click_xPoint: undefined, // 当前点击的是那一个点，默认的是有效值最后一个点
      isGLFlag: false, // 是否过滤停机数据
      y_maxMin: { min: 0, max: 0 }, // x轴最大值和最小值
      markedListTime: [],
    };
  },
  computed: {
    ...mapGetters(['getWaveFormObj', 'getLastRequestedId']),
  },

  methods: {
    ...mapMutations(['SET_CHARACTER_X', 'SET_CHARACTER_Y', 'SET_LAST_REQUESTED_ID']),
    keyDowns(e) {
      this.$refs[this.chartItself].keyDown(e);
    },
    keyUps(e) {
      this.$refs[this.chartItself].keyUp(e);
    },
    // 添加标注接口
    async addMarksInterface(params, xy) {
      try {
        await addMark(params);
        this.markedPoints = [...this.markedPoints, ...[xy]];
        this.markedListTime = [...this.markedListTime, ...[xy.objId]];
        this.$message.success('标注成功');
        console.log(this.markedListTime);
      } catch (e) {
        console.log(e);
        this.$message.error('标注失败');
      }
    },
    async cancelMarksInterface(params) {
      try {
        await cancelMark(params);
        this.markedPoints = [];
        this.markedListTime = [];
        this.$message.success('取消全部标注成功');
      } catch (e) {
        console.log(e);
        this.$message.error('取消全部标注失败');
      }
    },
    async getStopLineValue() {
      try {
        let res = await getStopLineValue({ waveId: this.waveConfigId });
        return res.data.data;
      } catch (e) {
        this.$message.error('获取停机线失败');
        return false;
      }
    },
    addMark(xy) {
      //    将x和元数据进行对比
      let obj = this.characterOrigin.find(it => {
        return new Date(it.originTime).getTime() === xy.x;
      });
      this.addMarksInterface(
        { originTime: obj.originTime, waveId: this.waveConfigId, showWave: this.showWave },
        { ...xy, objId: obj.originTime }
      );
    },
    //  取消全部标注
    cancelMark() {
      if (this.markedPoints.length === 0) {
        this.$message.warning('请先添加标注');
        return;
      }
      let params = {
        // dataIds: this.markedPoints.map(it => it.objId).join(','),
        // monitorId: this.monitorId,
        waveId: this.waveConfigId,
        ...this.timeObj,
        originTimes: this.markedListTime,
      };
      this.cancelMarksInterface(params);
    },
    async QXGLTJSJ() {
      if (this.isGLFlag) {
        this.transData(this.characterOrigin, 'characterDataList');
      } else {
        this.$message.warning('请先过滤停机数据');
      }
    },
    // 过滤停机线,找到元数据，将小于停机线的值过滤掉，然后重绘图表
    async GLTJSJ() {
      let res = await this.getStopLineValue();
      // if (res.enableRunningByVibration) {
      // let stopVal = Number(res.runningStateMin);
      let stopVal = Number(res);

      //  过滤掉原始数据
      let filterData = this.characterOrigin.filter(it => {
        return Number(it.value) >= stopVal;
      });
      // 过滤掉报警点数据
      this.alarmDataList = this.alarmDataList.filter(it => {
        return Number(it.value) >= stopVal;
      });
      if (filterData.length !== 0 && filterData.length < this.characterOrigin.length) {
        let def = new Date(filterData[filterData.length - 1].originTime).getTime();
        this.transData(filterData, 'characterDataList');
        // 重新请求时域和频域图
        if (this.click_xPoint && def !== this.click_xPoint) {
          await this.changePointLine(def);
        }
        this.$message.success('过滤停机数据成功');
        this.isGLFlag = true;
      } else if (filterData.length === 0 && this.characterOrigin.length > 0) {
        // 全部小于0
        this.flag = false;
      } else {
        this.$message.warning('暂无可过滤数据');
        this.isGLFlag = false;
      }
      // } else {
      //   this.$message.warning('未开启停机线');
      //   this.isGLFlag = false;
      // }
    },
    // 点击图谱，确定鼠标按键是移动哪个
    clickMap(type) {
      this.rightMenuShow = false;
      this.chartItself = type;
      this.$emit('chartItself', type);
    },

    // 将有效值域图的x轴传递回来
    main_xyPoint(x_time, y_data) {
      this.main_x_timestamp = x_time;
      this.main_y_data = y_data;
      this.SET_CHARACTER_X(x_time); // 将时间存起来
      this.SET_CHARACTER_Y(y_data); // 将时间存起来
      this.changePointLine(x_time); // 如果渲染完成之后，获取对应时间的url，开始渲染时域图 和频域图
    },

    // 点击上面的点，切换波形
    async changePointLine(x, y) {
      this.main_x_timestamp = x;
      this.main_y_data = y;
      this.SET_CHARACTER_X(x);
      this.click_xPoint = x;
      // 判断是不是键盘弹起，键盘弹起，加载数据，左右键盘按住不动不加载数据；点击效果传参数up 直接展示数据
      this.auxiliaryLoading = true;
      // 开始根据点击的点，查找相应的时域波形
      let obj = this.characterOrigin.find(it => {
        if (new Date(it.originTime).getTime() === x) {
          this.samplingFreq = Number(it.samplingFreq);
          this.samplingPoints = Number(it.samplingPoints);
        }
        return new Date(it.originTime).getTime() === x;
      });
      if (obj.waveformUrl) {
        let urlFile = convertFileUrl(obj.waveformUrl);
        fetchTextFile(urlFile).then(data => {
          // 在这里处理文件内容
          this.transData(data, 'timeDomainWaveForm');
        });
        //  获取频域图
        try {
          let params = {
            waveId: this.waveConfigId,
            monitorId: this.monitorId,
            originTime: dayjs(x).format('YYYY-MM-DD HH:mm:ss'),
          };

          let res = await timeAndFreqWave(params);
          let forData = res.data.data;
          this.transData(forData['freqDomainWaveForm'], 'freqDomainWaveForm');
          this.auxiliaryLoading = false;
        } catch (e) {
          this.auxiliaryLoading = false;
          console.log(e);
        }
      } else {
        this.freqDomainWaveForm = [];
        this.timeDomainWaveForm = [];
        this.auxiliaryLoading = false;
      }
    },
    // 波形图展示，1.如果第一次返回多个波形图，则第一个组件展示数组第一个波形数据  2.将删除头部的数组付给第二个波形图
    async getWaveData() {
      this.$emit('indexTreeLoading', true);
      if (this.waveConfigId) {
        try {
          let params = {
            waveId: this.waveConfigId, // 查找波形图参数
            monitorId: this.monitorId,
            ...this.timeObj,
            showWave: this.showWave,
          };
          let data = await eigenvalueWaveForm(params);
          this.res = data.data.data;
          this.$emit('indexTreeLoading', false);
          this.flag = (this.res.dataList && this.res.dataList.length) > 0 ? true : false;
          this.alarmDataList = this.res.alarmDataList || []; // 报警数据
          this.characterOrigin = this.res.dataList;
          if (this.flag) {
            this.transData(this.res.dataList, 'characterDataList');
          } else {
            this.SET_CHARACTER_X(undefined); // 将时间存起来
            this.SET_CHARACTER_Y(undefined); // 将时间存起来
          }
          this.$refs['effective_value'].switchOption = [];
        } catch (e) {
          this.$emit('indexTreeLoading', false);
        }
        //  执行完成趋势波形图谱之后，判断会不会存在要打开的tab
        this.getJumpToAddWave();
      } else {
        this.$emit('indexTreeLoading', false);
        this.flag = false;
      }
    },
    //  获取要打开的对象
    getJumpToAddWave() {
      let strObj = localStorage.getItem('alarmJumpToAddWave');
      const obj = strObj && strObj !== 'undefined' ? JSON.parse(strObj) : undefined;
      if (obj) {
        this.$emit('addTab', obj);
      }
    },
    getChange(val) {
      this.showWave = val.showWave;
      this.getWaveData();
      // 切换图表的时候，将switchOption 清空
      this.$refs['effective_value'].switchOption = [];
    },
    // 处理数据
    transData(val, type) {
      if (val && val.length) {
        // 振动s
        if (type === 'characterDataList') {
          let maxMinArr = []; // 存放最大值最小值
          // 有效值 需要特殊处理
          let timeArray = [];
          let markList = [];
          let data = val.map(item => {
            maxMinArr.push(Number(item.value));
            if (item.isMarked === 1) {
              this.markedListTime.push(item.originTime);
              markList.push(item);
            } // 将已经标注的存放进入数组
            timeArray.push(new Date(item.originTime).getTime());
            if (item.alarmLevel && item.alarmLevel > 0) {
              return {
                x: new Date(item.originTime).getTime(),
                y: Number(item.value),
                id: item.id,
                marker: {
                  symbol: 'diamond', // 可以选择其他形状，如 'square', 'diamond', 等
                  radius: 6, // 设置marker大小
                  fillColor: getAlarmColor(item.alarmLevel), // 设置填充颜色
                  lineWidth: 2, // 边框线宽
                  lineColor: getAlarmColor(item.alarmLevel), // 边框线颜色
                },
              };
            } else {
              return {
                x: new Date(item.originTime).getTime(),
                y: Number(item.value),
              };
            }
          });
          this.tickIntervalNum = {
            min: timeArray[0],
            max: timeArray[timeArray.length - 1],
            count: 5,
          }; // 计算出来的是每一个间隔

          this.markedPoints = markList.map(item => {
            return {
              x: new Date(item.originTime).getTime(),
              y: Number(item.value),
              objId: item.originTime,
            };
          });
          // 时间倒序返回
          this.y_maxMin = { min: Math.min(...maxMinArr), max: Math.max(...maxMinArr) };
          this[type] = [
            {
              isAlarm: this.alarmDataList.length > 0 ? true : false,
              x_point: data[data.length - 1].x,
              y_point: data[data.length - 1].y,
              waveform: data,
              markedList: this.markedPoints,
            },
          ]; // 主图谱
        } else {
          let dataArr = type === 'timeDomainWaveForm' ? JSON.parse(val) : val;

          // 时域图和频域图
          // 時域圖最大值 采样频率/2.56*1000（1000是将m 转换成ms）
          let len = dataArr.length;
          //修改成计算的x轴最大值 时域图：x = 采样点数/采样频率  频域图: x = 采样频率*1000/2.56  = 截止频率
          let x_max =
            type === 'timeDomainWaveForm'
              ? (this.samplingPoints / this.samplingFreq) * 1000
              : this.samplingFreq / 2.56;
          type === 'timeDomainWaveForm'
            ? (this.x_max_timeDomain = Number(x_max))
            : (this.x_max_freqDomain = this.samplingFreq / 2.56);
          let jg = x_max / len;
          let int = -jg;
          let data = dataArr.map(item => {
            int = int + jg;
            return [int, Number(item)];
          });

          this[type] = [
            {
              x_point: 0,
              y_point: Number(data[0][1]),
              waveform: data,
            },
          ];
        }
      } else {
        this[type] = [
          {
            x_point: 0,
            y_point: 0,
            waveform: [],
          },
        ];
        this.SET_CHARACTER_X(undefined); // 先将时间清空
        this.SET_CHARACTER_Y(undefined); // 将时间存起来
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import '../style/index';
</style>
