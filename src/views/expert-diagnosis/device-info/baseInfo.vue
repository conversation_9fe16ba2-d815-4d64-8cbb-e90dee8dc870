<template>
  <el-descriptions
    border
    :labelStyle="{ width: '110px' }"
    :contentStyle="{ wordBreak: 'break-all', wordWrap: 'break-word' }"
    contentClassName="contentClassName"
  >
    <el-descriptions-item label="设备名称">{{ details.name || '-' }}</el-descriptions-item>
    <el-descriptions-item label="制造厂商">{{ details.manufacturer || '-' }}</el-descriptions-item>
    <el-descriptions-item label="设备类型">{{ details.typesName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="设备编码">{{ details.code || '-' }}</el-descriptions-item>
    <el-descriptions-item label="规格型号">{{ details.model || '-' }}</el-descriptions-item>
    <el-descriptions-item label="设备分类">{{ details.categoryName || '-' }}</el-descriptions-item>

    <el-descriptions-item :label="'设备频率'"
      >{{ details.frequency || '-' }} {{ details.frequency ? ' Hz' : '' }}</el-descriptions-item
    >
    <el-descriptions-item label="设备转速"
      >{{ details.rev || '-' }} {{ details.rev ? ' RPM' : '' }}</el-descriptions-item
    >
    <el-descriptions-item label="设备路径">{{ details.pathName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="设备功率">
      {{ details.power || '-' }}{{ details.power ? ' kW' : '' }}</el-descriptions-item
    >
    <el-descriptions-item label="预期寿命">
      {{ details.lifeExpectancy || '-' }}天</el-descriptions-item
    >
    <el-descriptions-item label="开始使用日期">
      {{ details.startDateOfUse || '-' }}
    </el-descriptions-item>
    <el-descriptions-item label="3D场景">
      {{ details.sceneName || '-' }}
    </el-descriptions-item>
    <el-descriptions-item label="RFID卡号">
      {{ details.rfid || '-' }}
    </el-descriptions-item>
    <el-descriptions-item
      label="设备模型图"
      v-if="details.imageList && details.imageList.length > 0"
    >
      <el-image
        style="width: 100px; height: 100px"
        :src="convertFileUrl(details.imageList[0].domain)"
        fit="cover"
        :preview-src-list="[convertFileUrl(details.imageList[0].domain)]"
      ></el-image
    ></el-descriptions-item>
  </el-descriptions>
</template>

<script>
import { convertFileUrl } from '@/utils/util';
export default {
  props: {
    details: {
      type: Object,
      default: () => {
        return {};
      },
    },
    imageListLink: {
      type: String,
      default: () => {
        return '';
      },
    },
  },
  watch: {},
  components: {},
  data() {
    return { convertFileUrl };
  },

  methods: {},
  mounted() {},
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-descriptions {
    padding: 0 15px 0 10px;
  }

  .labelClassName {
    width: 90px;
    border: 1px solid #314162 !important;
  }

  .contentClassName {
    width: 200px;
    color: #fff;
    background: #00254f !important;
    border: 1px solid #314162 !important;
  }
}
</style>
