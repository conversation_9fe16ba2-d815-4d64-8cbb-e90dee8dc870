<template>
  <el-table :data="points" border :span-method="objectSpanMethod" row-key="sensorCode">
    <el-table-column type="index" label="#" align="center"></el-table-column>
    <el-table-column prop="sensorCode" label="部位名称" align="center">
      <template #default="{ row }">{{ row.name || '-' }} </template></el-table-column
    >
    <el-table-column prop="equipmentTypeName" label="部位类型" align="center">
      <template #default="{ row }">{{ row.equipmentTypeName || '-' }} </template></el-table-column
    >
    <el-table-column prop="code" label="部位编号" align="center">
      <template #default="{ row }">{{ row.code || '-' }} </template></el-table-column
    >
    <el-table-column prop="sensorNumber" label="绑定传感器数量" align="center">
      <template #default="{ row }">{{ row.sensorNumber }} </template></el-table-column
    >
    <el-table-column prop="sensorName" label="部位图片" align="center">
      <template #default="{ row }">
        <el-image
          v-if="row.imageList"
          style="width: 50px; height: 50px"
          :src="convertFileUrl(row.imageList[0].domain)"
          fit="fill"
          :preview-src-list="convertFileUrl([row.imageList[0].domain])"
        ></el-image>
        <span v-else>-</span>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import { convertFileUrl } from '@/utils/util';

export default {
  serviceDicts: ['sensor_category'], //  传感器类型
  name: 'DeviceDetailPoints',
  props: {
    // 测点信息列表
    points: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {},
  data() {
    return { convertFileUrl };
  },
  watch: {},
  mounted() {},
  methods: {
    convertFileUrl,
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const _row = this.filterData(this.points, columnIndex).one[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        row.spanCount = _row; // 记录下合并的行数量
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    filterData(arr, colIndex) {
      let spanOneArr = [];
      let concatOne = 0;
      arr.forEach((item, index) => {
        if (index === 0) {
          spanOneArr.push(1);
        } else {
          //name表格数据第二列和第六列的字段参数,根据实际参数修改
          if (colIndex === 1) {
            if (item.name && item.name === arr[index - 1].name) {
              spanOneArr[concatOne] += 1;
              spanOneArr.push(0);
            } else {
              spanOneArr.push(1);
              concatOne = index;
            }
          }
        }
      });
      // console.log('spanOneArr ', spanOneArr);
      return {
        one: spanOneArr,
      };
    },
  },
};
</script>

<style scoped lang="scss"></style>
