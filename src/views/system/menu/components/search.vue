<template>
  <el-form
    v-show="showSearch"
    ref="queryForm"
    :model="queryParams"
    :inline="true"
    label-position="left"
    label-width="20"
  >
    <el-form-item label=" " prop="name">
      <el-input
        maxlength="50"
        v-model="queryParams.name"
        placeholder="请输入菜单名称"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <el-form-item label=" " prop="code">
      <el-input
        maxlength="50"
        v-model="queryParams.code"
        placeholder="请输入菜单编号"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <!--    <el-form-item label=" " prop="alias">-->
    <!--      <el-input v-model="queryParams.alias" placeholder="请输入菜单别名" clearable />-->
    <!--    </el-form-item>-->
    <el-form-item label=" ">
      <el-button type="primary" @click="handleQuery">查询</el-button>
    </el-form-item>
    <el-form-item label=" ">
      <el-button @click="resetQuery">重置 </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    showSearch: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      queryParams: {
        name: undefined,
        code: undefined,
        alias: undefined,
      },
    };
  },
  mounted() {
    this.handleQuery();
  },
  methods: {
    handleQuery() {
      this.$emit('search', Object.assign({}, this.queryParams));
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-right: 0;
}
</style>
