<template>
  <div class="basic-container">
    <el-card>
      <div class="flex flex-justify-between flex-content-center">
        <div>
          <el-button type="primary" @click="operateAdd()">添加</el-button>
          <el-button type="danger" @click="batchDelete">删除</el-button>
        </div>
        <search @query="search"></search>
      </div>

      <el-table
        :data="list"
        border
        ref="table"
        v-loading="loading"
        :height="tableHeight"
        :max-height="tableHeight"
        @selection-change="handleSelectionChange"
      >
        <template #empty> <custom-empty></custom-empty></template>
        <el-table-column type="index" label="#"></el-table-column>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="字典编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dictValue" label="字典名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sort" label="字典排序" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="isSealed" label="封存" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag type="primary"> {{ row.isSealed === 1 ? '是' : '否' }}</el-tag>
          </template></el-table-column
        >

        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button type="primary" link @click="operateAdd(scope.row.id)">编辑</el-button>
            <el-button link type="primary" @click="del(scope.row.id)">删除</el-button>
            <el-button link type="primary" @click="config(scope.row)">字典配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex flex-justify-center mt-10">
        <yk-pagination
          ref="pagination"
          :total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
          @sizeChange="sizeChange"
        />
      </div>
      <!--    新增的时候-->
      <add-dialog ref="AddDialog" @refresh="refresh"></add-dialog>
      <!--       字典配置信息-->
      <config-dialog ref="configDialog"></config-dialog>
    </el-card>
  </div>
</template>

<script>
import AddDialog from './operate/index.vue';
import ConfigDialog from './config/index.vue';
import Search from './search.vue';
import { getTableHeight } from '@/utils/util';
import CustomEmpty from '@/components/custom-empty.vue';
import { getParentList, remove } from '@/api/system/dict';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  components: { Search, AddDialog, CustomEmpty, ConfigDialog },
  data() {
    return {
      percentage: 50,
      queryParams: {
        size: 20,
        current: 1,
      },
      list: [],
      total: 10,
      deviceId: [],
      loading: false,
      res: undefined,
      tableHeight: 0,
      mul: [],
    };
  },

  mounted() {
    this.$nextTick(() => {
      this.setTableHeight();
      this.getList();
    });
  },
  unmounted() {
    window.removeEventListener('resize', this.tableDoLayout);
  },
  methods: {
    //  配置字典
    config(row) {
      this.$refs['configDialog'].show(row);
    },
    // 批量 删除
    batchDelete() {
      if (this.mul.length > 0) {
        console.log(this.map);
        let ids = this.mul
          .map(item => {
            return item.id;
          })
          .join(',');
        this.del(ids);
      } else {
        ElMessage.warning('请选择至少一条数据');
      }
    },
    del(ids) {
      ElMessageBox.confirm('确定删除选择的数据吗?', '提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          remove(ids).then(() => {
            this.getList();
            ElMessage({
              type: 'success',
              message: '删除成功',
            });
          });
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消',
          });
        });
    },

    handleSelectionChange(val) {
      this.mul = val;
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = getTableHeight({
          tableRef: this.$refs.table,
          underTableRefs: [this.$refs.pagination],
          otherBottomHeight: 41,
        });
      });
    },
    // 窗口改变的时候，重新渲染表格
    tableDoLayout() {
      if (this.$refs.table && this.$refs.table.doLayout) {
        this.$refs.table.doLayout();
      }
    },
    sizeChange() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 刷新
    refresh() {
      this.getList();
    },

    // 点击新增的时候，展示新增页面
    operateAdd(id) {
      this.$refs['AddDialog'].show(id);
    },

    // 点击搜索
    search(search) {
      this.queryParams.current = 1;
      this.queryParams = {
        ...this.queryParams,
        ...search,
      };
      this.getList();
    },

    async getList() {
      this.loading = true;
      try {
        let res = await getParentList({
          ...this.queryParams,
        });
        this.list = res.data.data.records || [];
        this.total = res.data.data.total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
