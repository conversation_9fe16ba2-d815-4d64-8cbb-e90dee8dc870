<template>
  <el-form class="_form" ref="form" :model="form" label-width="20" inline>
    <el-form-item label="" prop="code">
      <el-input maxlength="50" v-model="form.code" placeholder="请输入字典编号"></el-input>
    </el-form-item>
    <el-form-item label="" prop="dictValue">
      <el-input maxlength="50" v-model="form.dictValue" placeholder="请输入字典名称"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">查询</el-button>
    </el-form-item>
    <el-form-item class="reset">
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      form: {
        code: undefined, // 设备
        dictValue: undefined,
      },
    };
  },
  watch: {},
  methods: {
    search() {
      this.$emit('query', this.form);
    },
    reset() {
      this.$refs['form'].resetFields();
      this.search();
    },
  },
};
</script>

<style scoped lang="scss"></style>
