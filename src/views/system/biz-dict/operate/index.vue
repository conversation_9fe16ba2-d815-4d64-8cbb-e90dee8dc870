<template>
  <el-dialog
    :title="edit ? '编辑' : '新增'"
    v-model="visible"
    width="80%"
    ref="_dialog"
    top="5vh"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <form-info ref="formInfo" :details="details"></form-info>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closed" :loading="loading">取 消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">提 交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import FormInfo from './form-info/index.vue';
import CustomEmpty from '@/components/custom-empty.vue';
import { add, getDict } from '@/api/system/dictbiz';

export default {
  components: {
    FormInfo,
    CustomEmpty,
  },
  data() {
    return {
      visible: false,
      loading: false,
      edit: false,
      details: {},
    };
  },
  methods: {
    // 父节点点击的时候，展示弹窗
    show(id) {
      this.edit = !!id;
      this.visible = true;
      if (id) {
        this.getDetail(id);
      }
    },
    async getDetail(id) {
      this.loading = true;
      try {
        let { data } = await getDict(id);
        this.details = data.data;

        this.loading = false;
      } catch ({ message }) {
        this.loading = false;
      }
    },
    // 点击提交
    async submit() {
      let val = await this.$refs['formInfo'].validForm(); //案例信息
      if (val) {
        await this.submitApi({
          id: this.edit ? this.details.id : undefined,
          ...val,
          isSealed: val.isSealed ? 1 : 0,
        });
      }
    },
    // 弹窗关闭之后，重置表单
    closed() {
      this.$refs['formInfo'].resetForm(); //案例信息
      this.visible = false;
    },

    // 提交
    async submitApi(params) {
      this.loading = true;
      try {
        await add(params);
        this.$message.success('操作成功');
        this.$emit('refresh', true);
        this.visible = false;
        this.loading = false;
      } catch ({ message }) {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.el-dialog__body {
  height: 55vh !important;
}

.knowledge {
  //border: 1px solid #626c90;
  height: 50vh;
  overflow-y: scroll;
  //padding: 10px 1px;
  box-shadow: 0 0 30px rgba(98, 108, 144, 0.3);
}
</style>
