<template>
  <el-dialog :modelValue="visible" title="删除结果" width="500px">
    <div v-if="deleteResult.failureNumber > 0">
      <p>删除失败数量: {{ deleteResult.failureNumber }}</p>
      <p>失败原因: {{ deleteResult.detailVOList[0]?.message }}</p>
    </div>
    <div v-else>
      <p>删除成功!</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="$emit('update:visible', false)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
const props = defineProps({
  visible: Boolean,
  deleteResult: Object
})
defineEmits(['update:visible'])
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 