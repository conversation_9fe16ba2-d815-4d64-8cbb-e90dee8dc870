<template>
  <el-dialog :modelValue="visible" title="租户数据源配置" width="450px">
    <el-form ref="formRef" :model="datasourceForm" label-width="100px">
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="datasourceForm.datasourceId"
          placeholder="请选择数据源"
          style="width: 100%"
        >
          <el-option
            v-for="item in datasourceOptions"
            :key="item.id"
            :label="item.packageName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="$emit('submit')">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  datasourceForm: Object,
  datasourceOptions: Array
})
defineEmits(['update:visible', 'submit'])
const formRef = ref(null)
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 