<template>
  <div>
    <p class="el-base-title">消息说明</p>
    <p class="desc">{{ desc }}</p>
    <p class="el-base-title">消息设置</p>
    <el-form class="form" ref="form" :model="form" label-width="150" label-suffix=":">
      <el-row :gutter="0">
        <el-col :span="12"
          ><el-form-item
            label="消息发送时间"
            prop="time"
            v-if="active !== 'sensor'"
            :key="form.time"
          >
            <el-select v-model="form.time" placeholder="请选择报警等级" v-if="active === 'daily'">
              <el-option
                v-for="dict in getHalfDateArr"
                :key="dict.label"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
            <span v-if="active === 'abnormal' || active === 'report'">及时发送</span>
          </el-form-item>
          <el-form-item label="传感器异常排查间隔" prop="jg" v-else :key="form.jg">
            <el-select v-model="form.jg" placeholder="请选择传感器异常排查间隔">
              <el-option
                v-for="dict in getSensorInterval()"
                :key="dict.label"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
            <span v-if="active === 'abnormal' || active === 'report'">及时发送</span>
          </el-form-item>
        </el-col>
        <el-col :span="12"
          ><el-form-item label="消息推送对象" prop="role">
            <el-select v-model="form.role" placeholder="请选择消息推送对象">
              <el-option
                v-for="dict in []"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="12">
          <el-form-item label="推送方式" prop="">
            <span>{{ active === 'sensor' ? 'PC' : 'APP' }}，暂不支持其它模式</span>
          </el-form-item></el-col
        >
        <el-col :span="12">
          <el-form-item label="消息模板" prop="">
            <span> 暂不支持模板编辑</span>
          </el-form-item></el-col
        >
        <el-col :span="12" v-if="active === 'abnormal'"
          ><el-form-item label="是否自动推送" prop="auto">
            <el-select v-model="form.auto" placeholder="请选择是否自动推送">
              <el-option
                v-for="dict in systemDicts.type['yes_no']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
      </el-row>
    </el-form>
    <div class="footer">
      <el-button @click="reset" type="info">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>
<script>
import { getHalfDate, getSensorInterval } from '@/utils/date';

export default {
  systemDicts: ['yes_no'],
  props: {
    desc: {
      type: String,
      default: '',
    },
    active: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      getSensorInterval,
      form: {
        time: undefined,
        role: undefined,
        auto: '1',
      },
      getHalfDateArr: [],
    };
  },
  mounted() {
    this.getHalfDateArr = getHalfDate();
  },
  methods: {
    onSubmit() {
      console.log(this.form);
    },
    reset() {
      this.$refs['form'].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.desc {
  padding: 20px 0;
  text-indent: 15px;
}
.footer {
  position: fixed;
  bottom: 30px;
  right: 20px;
}
</style>
