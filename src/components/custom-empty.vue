<template>
  <div
    style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center"
  >
    <el-empty :image-size="size" :image="Empty">
      <template #description>
        <span class="description">{{ tip }}</span>
      </template>
    </el-empty>
  </div>
</template>

<script>
import Empty from '@/assets/images/empty.png';
export default {
  name: 'customEmpty',
  props: {
    size: {
      type: Number,
      default: 50,
    },
    tip: {
      type: String,
      default: '暂无数据',
    },
  },
  data() {
    return {
      Empty,
    };
  },
};
</script>

<style lang="scss" scoped>
.description {
  color: #cdcdcd !important;
  font-size: 12px;
}
</style>
