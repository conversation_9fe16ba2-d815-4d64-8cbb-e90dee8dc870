<template>
  <div>
    <el-dialog width="555px" append-to-body v-model="open" :title="title">
      <el-form v-model="form" :rules="rules" label-width="90px" label-suffix=":">
        <el-form-item label="文件上传">
          <el-upload
            ref="upload"
            :action="`/api${action}?isCovered=${form.isCovered}`"
            accept=".xls, .xlsx"
            :file-list="form.fileList"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :headers="uploadHeaders"
          >
            <el-button type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="数据覆盖" prop="isCovered">
          <el-switch
            v-model="form.isCovered"
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="模板下载">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </el-form-item>
      </el-form>

      <el-dialog
        title="导入结果"
        width="555px"
        append-to-body
        v-model="resOpen"
        @close="handleResClose"
      >
        <div class="content">
          <template v-if="importRes.failCount > 0">
            <div class="res-text">
              结果: 成功{{ importRes.successCount }}条, 失败{{ importRes.failCount }}条
            </div>
            <el-button type="text" @click="handleDownloadFailData"
              >点击下载导入失败的数据</el-button
            >
          </template>
          <div v-else class="res-text">结果: 导入成功, 成功{{ importRes.successCount }}条</div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="handleResClose">关闭</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import { exportBlob } from '@/api/common';
import website from '@/config/website';
import { getToken } from '@/utils/auth';
import { downloadXls } from '@/utils/util';
import { Base64 } from 'js-base64';
import { ElMessage } from 'element-plus';

function downloadFile({ headers, data }) {
  // 从响应头获取文件名称
  let encodeName = headers['content-disposition'].split('filename=')[1];
  downloadXls(data, decodeURIComponent(encodeName));
}

export default {
  name: 'Import',
  props: {
    // 导入弹框标题
    title: {
      type: String,
      default: '',
    },
    // 弹框是否可见
    importVisible: {
      type: Boolean,
      default: false,
    },
    // 文件上传地址
    action: {
      type: String,
      require: true,
    },
    // 模版下载路径
    templateUrl: {
      type: String,
      require: true,
    },
    // 导入失败原因下载路径
    failReasonUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      website,
      form: {
        fileList: [],
        isCovered: 0,
      },
      rules: {
        isCovered: [{ required: true, message: '请选择是否覆盖', trigger: 'blur' }],
      },
      resOpen: false,
      importRes: {
        failCount: 0,
        successCount: 0,
      },
    };
  },
  methods: {
    // 下载模版
    handleTemplate() {
      exportBlob(`/api${this.templateUrl}?${this.website.tokenHeader}=${getToken()}`).then(
        downloadFile
      );
    },
    // 导入成功
    handleUploadSuccess(res) {
      if (res.success) {
        // 如果配置了failReasonUrl, 则展示导入结果, 否则直接关闭导入弹窗
        if (this.failReasonUrl) {
          this.importRes = res.data;
          this.resOpen = true;
        } else {
          this.$message.success('导入成功');
          this.open = false;
          this.$emit('refresh');
        }
      }
    },
    // 导入失败
    handleUploadError(err) {
      // console.log('err', err, JSON.parse(err));
      // this.$message.error(err);
      ElMessage({
        message: '上传失败',
        type: 'error',
      });
    },
    // 结果弹窗关闭
    handleResClose() {
      this.resOpen = false;
      this.open = false;
      this.$emit('refresh');
    },
    // 下载导入失败数据
    handleDownloadFailData() {
      if (this.importRes.failReasonDownloadKey) {
        exportBlob(
          `/api${this.failReasonUrl}?downloadKey=${this.importRes.failReasonDownloadKey}`
        ).then(downloadFile);
      } else {
        this.$message.error('失败信息key丢失, 请重新导入');
      }
    },
  },
  computed: {
    open: {
      get() {
        return this.importVisible;
      },
      set() {
        this.form.isCovered = 0;
        this.$refs.upload.clearFiles();
        this.$emit('close');
      },
    },
    uploadHeaders() {
      return {
        Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
        [website.tokenHeader]: 'bearer ' + getToken(),
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  .res-text {
    margin-bottom: 10px;
    font-weight: bold;
  }
}
</style>
