<template>
  <el-row id="root-tree" ref="root-tree">
    <el-input
      ref="search"
      placeholder="输入名称进行过滤"
      v-model="filterText"
      maxlength="50"
    >
    </el-input>
    <el-scrollbar style="flex: 1">
      <el-tree
        id="tree"
        ref="tree"
        :current-node-key="currentNodeKey"
        v-loading="treeLoading"
        class="equipment-category-tree"
        :data="treeData"
        :show-checkbox="showCheckbox"
        node-key="id"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        :props="treeDefaultProps"
        @node-click="nodeClick"
        @check="checkChange"
        :default-expanded-keys="expandKeys"
      >
        <template v-slot="{ node }">
          <el-row type="flex" align="middle">
            <span class="_text">
              <span>{{ node.label }} </span></span
            >
          </el-row>
        </template>
      </el-tree>
    </el-scrollbar>
    <!--    @check-change="checkChange"-->
    <!--      @check="check"-->
  </el-row>
</template>

<script>
  import { queryTree, waveConfigTreeList } from '@/api/device';
  import { mapGetters } from 'vuex';
  import { bizFilterTreeApi } from '@/api/basic/basicTree';
  export default {
    name: 'all-tree',
    props: {
      params: {
        type: Object,
        default: () => {
          return {};
        }
      },
      showCheckbox: {
        type: Boolean,
        default: false
      },
      hiddenButtons: {
        type: Boolean,
        default: false
      },
      // 是否展示搜索框
      isSearch: {
        type: Boolean,
        default: true
      },
      //是否展示测点
      isPoint_show: {
        type: Boolean,
        default: false
      },
      // 点击图表的测点，
      currentPointObj: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
        // if (val !== '') {
        //   // this.expandKeys.push('1579278591113003010');
        //   // 查询
        //   this.queryAllTree(val);
        // } else {
        //   this.filterData = [];
        // }
      },
      popVisible(val) {
        this.popoverVisible = val;
      },
      // 当载设备基本信息图片点击测点的时候，定位相应的选中节点
      currentPointObj: {
        immediate: true,
        deep: true,
        handler(val) {
          // 先设置测点 ，然后 获取选中当前节点，如果对应，展开相关设备测点，不匹配，掉接口，展开，然后再设置
          this.$nextTick(() => {
            if (val.pointId) {
              this.$refs.tree && this.$refs.tree.setCurrentKey(val.pointId);
              let getNodePoint = this.$refs.tree.getCurrentKey();
              if (getNodePoint === val.pointId) {
                // 展开过，直接展开
                this.expandKeys.push(val.deviceId);
              } else {
                this.$refs.tree.getCurrentNode();
                this.expandKeys.push(val.deviceId);
                this.willSelectId = val.pointId;
              }
            }
          });
        }
      }
    },
    data() {
      return {
        checkedNode: [], // 选中的数据
        filterText: '',
        filterTree: [],
        treeFilterLoading: false,
        treeLoading: false,
        treeData: [],
        treeDefaultProps: {
          children: 'children',
          label: 'nodeName',
          isLeaf: 'isLeaf'
        },
        filterTreeDefaultProps: {
          children: 'children',
          label: 'name',
          isLeaf: 'isLeaf'
        },
        expandKeys: [],
        currentNodeKey: '',
        filterData: [],
        popoverVisible: false,
        willSelectId: '',
        oldCurrentKey: undefined,
        popoverWidth: 0,
        pathId: undefined,
        key: 0,
        checkFlag: false, // 标志操作的当前点是勾选还是取消勾选
        mouseOnPop: false, // 标识当前鼠标是否在pop搜索框上
        inputFocus: false // 标识当前搜索框是否在focus状态
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'topOrgId']),
      // 用于决定 搜索结果弹窗是否显示
      popVisible() {
        return (
          (this.mouseOnPop || this.inputFocus) && this.filterText.trim() !== ''
        );
      }
    },

    created() {
      window.addEventListener('resize', this.getTreeWidth);
    },
    mounted() {
      this.popoverWidth = document.getElementById('root-tree').clientWidth;
      this.init();
    },
    destroyed() {
      window.removeEventListener('resize', this.getTreeWidth);
    },
    methods: {
      // 取消勾选的方法
      setChecked(val) {
        if (Object.keys(val).length === 0) return;
        this.$refs['tree'].setChecked(val, false);
      },
      getTreeWidth() {
        let tree = document.getElementById('root-tree');
        this.popoverWidth = tree && tree.clientWidth;
      },
      searchInputFocus() {
        this.inputFocus = true;
      },
      popOver() {
        this.mouseOnPop = true;
      },
      popOut() {
        this.mouseOnPop = false;
      },
      // 查询所有树结构
      async queryAllTree(val) {
        try {
          this.treeFilterLoading = true;
          const res = await queryTree({
            nodeName: val
          });
          this.filterData = res.data.data;
          this.treeFilterLoading = false;
        } catch (e) {
          this.$message.warning(e.data.msg);
          this.treeFilterLoading = false;
        }
      },
      async loadNode(node, resolve) {
        let category = 0;
        if (node.level === 0) {
          this.getCheckedNode(node, this.data);
          return resolve(this.data || []);
        } else if (node.data.category === 3) {
          // 测点 查询
        } else if (node.data.category === 2) {
          const res = await waveConfigTreeList({
            monitorId: node.data.id
          });
          const data = res.data.data.map((it) => {
            return {
              ...it,
              category: it.nodeCategory,
              categoryName: it.nodeCategoryName,
              code: it.nodeCode,
              level: it.nodeLevel,
              name: it.nodeName,
              pathName: node.data.pathName + '',
              isLeaf: true
            };
          });
          this.getCheckedNode(node, data);
          return resolve(data);
        } else {
          if (node.data.hasDevice) {
            category = 1;
          } else if (node.data.hasMonitor) {
            category = 2;
          }
          const res = await bizFilterTreeApi({
            ...this.params
          });
          const data = res.data.data.map((it) => {
            let isLeaf = !(it.hasChildren || it.hasDevice || it.hasMonitor);
            return {
              ...it,
              category: it.nodeCategory,
              categoryName: it.nodeCategoryName,
              code: it.nodeCode,
              level: it.nodeLevel,
              name: it.nodeName,
              pathName: node.data.pathName + '/' + it.name,
              isLeaf: isLeaf
            };
          });
          if (data.length > 0) {
            if (data[0].category === 0) {
              node.data.hasChildren = true;
            } else if (data[0].category === 1) {
              node.data.hasDevice = true;
            }
          } else {
            node.data.hasChildren = false;
            node.data.hasDevice = false;
          }
          const findI = data.findIndex((it) => {
            return it.id === this.willSelectId;
          });

          if (node.data.id === this.willSelectId || findI !== -1) {
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(this.willSelectId);
              const node = this.$refs.tree.getNode(this.willSelectId);
              this.$emit('category-change', node);
              this.willSelectId = '';
            });
          }
          this.getCheckedNode(node, data);
          return resolve(data);
        }
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.nodeName.indexOf(value) !== -1;
      },
      init() {
        this.getTree('0', 0);
      },
      async getTree() {
        try {
          this.treeLoading = true;
          const res = await bizFilterTreeApi({ ...this.params });
          this.treeData = res.data.data.map((it) => {
            let isLeaf = !(it.hasChildren || it.hasDevice || it.hasMonitor);

            // 只展示到设备那一层
            return {
              ...it,
              category: it.nodeCategory,
              categoryName: it.nodeCategoryName,
              code: it.nodeCode,
              level: it.nodeLevel,
              name: it.nodeName,
              pathName: it.name,
              isLeaf: isLeaf
            };
          });
          this.setDefaultSelect();
          this.treeLoading = false;
        } catch (e) {
          this.treeLoading = false;
          this.treeData = [];
        }
      },
      async reload() {
        // 记录重新加载前的选中值
        this.oldCurrentKey = this.$refs.tree.getCurrentKey();
        try {
          this.treeLoading = true;
          const res = await bizFilterTreeApi({ ...this.params });
          this.treeData = res.data.data.map((it) => {
            let isLeaf = !(it.hasChildren || it.hasDevice || it.hasMonitor);
            return {
              ...it,
              category: it.nodeCategory,
              categoryName: it.nodeCategoryName,
              code: it.nodeCode,
              level: it.nodeLevel,
              name: it.nodeName,
              pathName: it.name,
              isLeaf: isLeaf
            };
          });

          const findI = this.treeData.findIndex((it) => {
            return it.id === (this.willSelectId || this.oldCurrentKey);
          });

          // 重新加载后 设置选中值
          if (findI !== -1) {
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(
                  this.willSelectId || this.oldCurrentKey
                );
              const node = this.$refs.tree.getNode(
                this.willSelectId || this.oldCurrentKey
              );

              this.$emit('category-change', node);
              this.willSelectId = '';
              this.oldCurrentKey = undefined;
            });
          } else {
            this.$emit('category-change', null);
          }

          this.treeLoading = false;
        } catch (e) {
          this.treeLoading = false;
          this.treeData = [];
        }
      },

      // 点击全部展示全部设备
      resetAll() {
        this.$emit('category-reset', null);
      },
      nodeClick(data, node) {
        this.$emit('category-change', node);
      },
      // 用它找到当前勾选的节点 后执行
      check(node, data) {
        console.log('check', node, data);
        this.$emit('getCheckedNodes', node, data, this.checkFlag);
      },
      // 用它找到当前节点是取消勾选还是确认勾选 先执行
      checkChange(node, ischeck) {
        this.checkFlag = ischeck;
        // 另一種方式注釋
        this.checkedNode = [];
        let checkeds = this.$refs['tree'].getCheckedNodes();
        this.checkedNode = [...this.checkedNode, ...checkeds];
        this.$emit('getCheckedNodes', this.checkedNode, node, ischeck);
      },
      getCheckedNode(node, data) {
        this.checkedNode = node.checked
          ? [...this.checkedNode, ...data]
          : [...this.checkedNode];
        this.$emit('getCheckedNodes', this.checkedNode, node);
      },
      filterNodeClick(data) {
        let paths = data.path.split(',');
        paths.forEach((it, index) => {
          const findIn = this.expandKeys.findIndex((m) => m === it);
          if (findIn === -1) {
            this.expandKeys.push(it);
          }

          if (index === paths.length - 1 && findIn !== -1) {
            this.$refs.tree && this.$refs.tree.setCurrentKey(it);

            this.$nextTick(() => {
              const node = this.$refs.tree.getNode(this.willSelectId);
              this.$emit('category-change', node);
            });
          }
        });
        this.popoverVisible = false;
        this.willSelectId = paths[paths.length - 1];
      },
      clear() {
        this.$refs.tree && this.$refs.tree.setCurrentKey(null);
        this.$emit('category-change', null);
      },
      clearFilter() {
        this.filterText = '';
      },
      // 设置第一个数据默认选中
      setDefaultSelect() {
        if (this.treeData.length === 0) {
          return;
        }
        if (this.pathId) {
          let pathArr = this.pathId.split(',');
          let path = [...pathArr];
          path.map((it) => {
            this.expandKeys.push(it);
          });
          this.willSelectId = path[path.length - 1];
        } else {
          // 请求第一个节点的子节点
          this.expandKeys = [this.treeData[0].id];
          this.$nextTick(() => {
            const first = this.$refs.tree.getNode(this.treeData[0].id);
            this.willSelectId = this.treeData[0].id;
            this.$emit('category-change', first);
          });
        }
      },
      // 重新懒加载某个节点
      refreshTree(node) {
        if (node === null) {
          this.reload();
          return;
        }

        //  设置未进行懒加载状态
        node.loaded = false;
        // 重新展开节点就会间接重新触发load达到刷新效果
        node.expand();
        this.willSelectId = node.data.id;
      },
      // 刷新某个节点 并选中其某个子节点
      refreshParent(parent, chilId) {
        this.willSelectId = chilId;
        if (parent === null) {
          this.reload();
        } else {
          // 设置未进行懒加载状态
          parent.loaded = false;
          // 重新展开节点就会间接重新触发load达到刷新效果
          parent.expand();
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  #root-tree {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }

  :deep(.el-scrollbar__bar) {
    display: none;
  }

  .filter-tree {
    height: 300px;
    overflow: auto;

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }
</style>
<style lang="scss">
  .filter-popover {
    padding-right: 5px;
  }
</style>
