export default {
  consoleTitleName: '知识图谱',
  LeftMenu: {
    service_title: '知识图谱',
    menu_graph: '我的图谱',
    menu_assets: '我的图谱资产库',
    menu_ontology: '我的本体',
    menu_model: '我的模型',
    menu_helpCenter: '帮助中心',
    graph_apply: '申请公测',
    graph_enabled: '您尚未开通知识图谱服务',
    graph_introduce:
      '知识图谱服务是面向各类企业的一站式知识计算平台，提供源数据管理、本体管理、图谱管理等功能，帮助用户从0到1快速构建知识图谱，管理知识图谱流水线任务。'
  },
  Common: {
    common_ok: '确定',
    common_ok_save: '保存',
    common_cancel: '取消',
    common_yes: '确定',
    common_no: '取消',
    common_next: '下一步',
    data: '数据源配置',
    ontology: '本体选择',
    info: '信息抽取',
    map: '知识映射',
    fuse: '知识融合',
    data_title: '数据源配置',
    ontology_title: '本体选择',
    info_title: '信息抽取',
    map_title: '知识映射',
    fuse_title: '知识融合',
    no_data: '暂无数据',
    reLoad: '重新加载',
    load_failed: '加载失败',
    no_search_data: '暂无符合条件的记录。',
    import_obs_bucket: 'OBS桶',
    import_obs_file: '文件',
    common_term_kgp_colon: ':',
    import_obs_import: 'OBS导入',
    import_obs_file_select: '存储路径',
    import_back_previous: '返回上一级',
    export_name_bucket: '导出文件名',
    name_invalid2: '不能以下划线开头',
    obs_auth: 'OBS桶授权',
    obs_auth_title: '确认授权',
    obs_auth_prompt: '请为OBS桶授权',
    graph_name_existed: '图谱名称已存在',
    model_name_existed: '模型名称已存在',
    lab_get_file_in_obs_bucket_failed: '获取OBS桶指定路径内文件列表失败。',
    common_term_ges_colon: '：',
    ontology_table_concepts: '实体个数',
    ontology_table_relations: '关系个数',
    ontology_table_description: '描述',
    ontology_table_operation: '操作',
    import_create_obs: '请先创建OBS桶',
    ges_access_gremlin_result_title: '搜索',
    ges_access_command_complete: '运行完成',
    ges_access_search_result_empty: '查询结果为空',
    kgp_name_field_search: '搜索实体 name 属性',
    graph_gremlin_command_placeholder: '请在此输入Gremlin命令，按回车键执行',
    ges_algorithm_run_time: ' 总耗时',
    update_entity: '更新实体',
    update_relation: '更新关系',
    headEntity: '头实体',
    headEntityTip: '请选择头实体',
    please_enter_keyWords: '请输入关键字',
    releta_name: '关系名',
    releta_name_tip: '请选择关系名',
    releta_no_options_tip: '该头实体不存在相应关系',
    tailEntity: '尾实体',
    tailEntity_tip: '请选择尾实体',
    tailEntity_tip2: '请先选择关系',
    entity_unique: '实体唯一标识值',
    entity_unique_tip: '将根据该值生成实体ID',
    create_success_entity: '创建成功',
    create_success_relation: '创建成功',
    modify_graph_name: '修改图谱名称',
    update_success: '更新成功',
    update_failed: '更新失败',
    delete_time: '删除时间',
    undo: '撤销',
    undo_tip: '确定要撤销？',
    undo_sucess: '撤销成功',
    delete_entity: '确定删除实体吗？',
    delete_entity_tip: '删除实体后可在删除记录中恢复。',
    delete_success: '删除成功',
    delete_relete: '确定删除关系吗？',
    delete_relete_tip: '删除关系后可在删除记录中恢复。',
    short_text: '短文本',
    verify_version: '验证版本',
    randomly_verify: '随机验证',
    randomly_back: '返回',
    verification_results: '验证结果',
    verification_publish: '发布版本',
    accuracies: '准确数',
    inaccuracies: '不准确数',
    uncertainties: '不确定数',
    property: '属性名称',
    Entity: '实体',
    undo_title: '撤销',
    ontology_node_attr_validation_null: '属性名称不能为空',
    boolean_validation: '请输入Boolean类型值',
    double_validation: '请输入Double类型值',
    int_validation: '请输入int类型值',
    model_file: '文件',
    kg_config_not_empty: '配置不为空，是否删除该流水线？',
    graphs_confirm_exit: '确认离开界面',
    graphs_confirm_exit_detail:
      '当前页面的配置还没保存，您确定要放弃保存，离开该页面吗？',
    graphs_confirm_exit_tip: '放弃保存将不对当前配置做任何修改。',
    graphs_confirm_exit_ok: '保存并离开',
    graphs_confirm_exit_cancel: '放弃保存',
    graphs_confirm_close: '关闭界面',
    graphs_confirm_close_ok: '保存并关闭',
    graphs_confirm_close_cancel: '放弃保存并关闭',
    save_success: '保存成功',
    parenthesesLeft: '（',
    parenthesesRight: '）',
    colon: '：',
    comma: '，',
    semicolon: '；',
    saveAndLeave: '保存并离开',
    doNotSave: '放弃保存'
  },
  Graph: {
    choose_map: '选择映射方式',
    ui_mode: '可视化模式',
    table_mode: '表格配置模式',
    code_mode: '代码配置模式',
    concept_mapping: '概念映射',
    data_category: '数据类别',
    map_to_concept: '映射为概念',
    extraction_item: '抽取项',
    graphs_table_error_tip: '最多添加5个数据字段。',
    type_empty_error: '数据类别为空，请配置。',
    new: '新建',
    newEntity: '新建实体',
    newRelete: '新建关系',
    select_all_entity: '选择所有实体',
    service_title: '知识图谱',
    menu_graph: '我的图谱',
    graph_list_title: '图谱管理',
    create_graph_tip:
      '您还没有知识图谱，点击<span style="color: #39f; cursor: pointer; margin: 0 5px">创建知识图谱</span>',
    smart_create_graph_tip:
      " 或<span style='color: #39f; cursor: pointer; margin: 0 5px'>智能构建知识图谱</span>",
    create_graph: '普通配置构建',
    graphs_name: '图谱名称',
    graphs_name_placeholder: '请输入图谱名称',
    graphs_entity_type: '实体唯一标识字段',
    data_source_type: '选择数据格式',
    data_source_required: '请选择数据源',
    graphs_label_type: '实体类型字段',
    graphs_label_illegal:
      '实体类型名称"{0}"不合法,只能由大小写字母、数字、下划线组合',
    graphs_spec: '图谱规格',
    menu_upgrade: '规格升级',
    menu_not_supported_upgrade: '当前图谱规格已是顶配版本',
    graphs_entities: '实体个数',
    graphs_relations: '关系个数',
    graphs_entities_relations: '实体/关系',
    graphs_status: '运行状态',
    graphs_created: '创建时间',
    graphs_trial_info: '试用信息',
    graphs_status_initializing: '初始化',
    graphs_status_creating: '创建中',
    graphs_status_upgrading: '升级中',
    graphs_status_frozen_can_release: '已冻结',
    graphs_status_frozen_can_not_release: '冻结不可用',
    graphs_status_running: '运行中',
    graphs_status_available: '可用',
    graphs_status_error: '故障',
    graphs_ID: '图谱ID',
    graphs_last_created: '最后更新时间',
    graphs_full_update: '全量更新',
    graphs_increment_update: '增量更新',
    graphs_generate: '生成图谱',
    graphs_publish: '图谱发布',
    graphs_version_publish: '版本开始发布',
    graphs_version_update_publish: '版本更新成功',
    graphs_reach_limit: '图谱数量已达上限',
    graphs_versions_reach_limit: '图谱版本数量已达上限',
    graphs_versions_cant_modify_ontology:
      '图谱版本数量已达上限(修改本体会新建一个图谱版本)',
    graphs_copy: '复制图谱',
    graphs_delete: '删除图谱',
    graphs_delete_tip: '删除图谱之后相关的数据将不能恢复。',
    graphs_statistics: '图谱统计数据',
    graphs_version_statistics: '版本统计',
    graphs_version_statistics_detail: '上线/未上线',
    graphs_version_published: '已上线',
    graphs_version_unpublished: '未上线',
    graphs_job: 'Job统计数据',
    graphs_preview: '预览图谱',
    graphs_view: '知识图谱预览查询',
    graphs_publish_count: '发布次数',
    graphs_full_count: '全量更新次数',
    graphs_increment_count: '增量更新次数',
    graphs_pipeline_preview: '流水线预览',
    graphs_log: '更新日志',
    graph_version: '图谱版本',
    graph_online_version: '在线版本图谱',
    graph_ability: '图谱应用',
    graphs_extract_mode: '抽取方式',
    graphs_edit_mode: '编辑方式',
    graphs_general_mode: '交互界面',
    graphs_json_mode: '格式化编辑',
    graphs_json_mode2: '代码编辑',
    graphs_json_mode_error: 'JSON 格式错误',
    graphs_extract_rule_error: '规则设置有误',
    graphs_info_general: '普通抽取',
    graphs_info_model: '模型抽取',
    graphs_info_structured: '结构化抽取',
    graphs_info_unstructured: '非结构化抽取',
    graphs_built_model: '预置模型',
    graphs_assets_model: '公有库模型',
    graphs_data_extraction_model: '抽取模型',
    graphs_built_placeholder: '请选择预置模型',
    graphs_custom_model: '用户自定义模型',
    graphs_custom_placeholder: '请选择用户自定义模型',
    graphs_info_retain_field: '默认抽取',
    graphs_info_retain_tip:
      '在每个数据类别中，为每个数据字段建立同名抽取项，抽取函数为${数据字段名}',
    graphs_info_add: '增加',
    graphs_info_copy: '复制',
    graphs_subject_type: 'Subject_type',
    graphs_predicate: 'Predicate',
    graphs_object_type: 'Object_type',
    graphs_data_type: '选择数据源文件',
    graphs_data_type_folder: '选择数据源文件夹',
    graphs_data_type_placeholder: '请选择数据源文件',
    graphs_data_type_title: '选择数据源文件',
    graphs_data_type_csv_tips: '请选择包含 .csv 文件所在的文件夹',
    graphs_table_tips:
      '每个需要抽取的数据类别有且仅有一个唯一标识符__identifier__（可以抽取id/编号等可以唯一标识一条数据的内容）',
    graphs_table_tips_warn:
      '每个需要抽取的数据类别有且仅有一个唯一标识符__identifier__',
    graphs_table_dup_key_warn: '每个实体类型下面的字段名都不能重复',
    graphs_table_tips_warn2: '抽取规则不能为空，请配置',
    graphs_extract_rule_key_error: '抽取规则缺少"key"字段',
    graphs_extract_rule_value_function_error:
      '抽取规则缺少"value_function"字段',
    graphs_extract_rule_key_value_function_error:
      '抽取规则仅支持设置"key"和"value_function"字段',
    graphs_table_type: '实体类型',
    graphs_table_type2: '数据类别',
    graphs_table_field: '数据字段',
    graphs_table_field2: '抽取项名称',
    graphs_table_function: '字段函数',
    graphs_table_function2: '抽取函数',
    graphs_table_operation: '操作',
    graphs_table_delete: '删除',
    graphs_entity_tip: '能够唯一标识实体的主键字段',
    graphs_label_tip: '数据中标识该实体属于何种类型的字段',
    graphs_map_type: '类型映射',
    graphs_map_before: '映射前',
    graphs_map_after: '映射后',
    graphs_map_before_tip: '源数据中该实体类型表示',
    graphs_map_property: '属性映射',
    graphs_table_property: '属性',
    graphs_table_property_tip: '该类型在本体中定义的属性',
    graphs_table_field_tip: '源数据中对应数据字段(最多添加5个)',
    graphs_map_relation: '关系映射',
    graphs_table_relation: '关系名称',
    graphs_table_tail: '尾实体类型',
    graphs_table_tail_tip: '有向关系中以该类型为头实体的尾实体类型',
    graphs_fuse_is: '是否融合',
    graphs_fuse_is_tip: '是否将相同的实体合并',
    graphs_fuse_tips:
      '知识融合为非必选流程，不配置仍可运行图谱构建。配置融合后，需开启融合开关方可生效。',
    graphs_fuse_block: '融合标识符',
    graphs_fuse_block_tip: '对融合标识符相似的实体计算相似度',
    graphs_fuse_property: '属性配置',
    graphs_fuse_property_tip: '判断融合的属性配置信息',
    graphs_fuse_condition: '融合条件',
    graphs_fuse_add: '增加属性配置项',
    graphs_fuse_group: '添加分组',
    graphs_fuse_items:
      "您还可以添加<span style='color: #FF4C4C'>{0}</span>行。",
    graphs_table_new_property: '新实体属性',
    graphs_table_old_property: ' 待融合属性',
    graphs_table_similarity_function: '相似度函数',
    graphs_table_similarity_threshold: '相似度阈值',
    graphs_edges: '图谱规格（边数）',
    graphs_edges_10000: '一万',
    graphs_edges_1000000: '一百万',
    graphs_edges_10000000: '一千万',
    graphs_edges_100000000: '一亿',
    graphs_edges_detail:
      '图谱边数超过图谱规格可能造成数据丢失，您可以升级现有图谱规格或创建更大规格图谱',
    graphs_run: '运行',
    graphs_run_tip: '当前流水线配置不完整',
    graph_version_name_placeholder: '请输入版本名称',
    graph_version_name: '版本名称',
    graph_update_ID: '运行编号',
    graph_update_event: '事件类型',
    graph_update_ontology: '本体',
    graph_all_status: '全部状态',
    graph_version_publishing: '发布中',
    graph_version_published: '已上线',
    graph_version_unpublished: '未上线',
    graph_logs_error: '异常',
    graph_logs_running: '运行中',
    graph_logs_completed: '已完成',
    graph_logs_name_placeholder: '请输入 ID',
    graph_version_verify: '验证',
    graph_version_publish: '发布',
    graph_version_publish_vs: '发布版本',
    graph_version_view_ontology: '查看本体',
    graph_version_delete: '删除',
    graph_export: '导出',
    graph_export_title: '图谱导出',
    graph_import: '导入',
    graph_import_title: '图谱导入',
    graph_export_action: '查看导出结果',
    graph_export_disabled_radio_tips: '请选择保存的文件夹路径',
    graph_import_disabled_radio_tips: '请选择导入的文件夹路径',
    graph_export_success_tips: '图谱导出任务已提交，导出进度请查看更新日志',
    graph_version_delete_vs: '删除版本',
    graph_version_modify_ontology: '修改本体',
    pipeline_common_assets: '公有库',
    pipeline_private_assets: '我的库',
    graph_publish_version_tip: '发布后此版本将替换线上版本。',
    graph_delete_version_ok: '确定要删除{0}版本吗？',
    graph_delete_version_tip: '删除版本将不可恢复，请谨慎操作。',
    kg_onekey_ontology_generating: '本体生成中，请稍等几分钟',
    kg_onekey_pipeline_config_generating: '流水线配置生成中，请稍等几分钟',
    graph_intelligent_construct: '智能一键构建',
    graph_intelligent_data_format: '选择数据格式',
    graph_look_pipeline: '查看流水线',
    graph_view_report: '查看质检报告',
    graph_look_ontology: '查看本体',
    graph_intelligent_hold_failed: '流水线配置生成失败。',
    graph_intelligent_exit_title: '确认退出智能构建',
    graph_intelligent_exit_tip: '退出后您所做的修改将不会被保存',
    graph_build_way_pipeline: '普通配置构建',
    graph_build_way_pipeline2: '图谱构建WorkSpace',
    graph_start_creating: '{0}图谱已经开始创建',
    order_product_type: '产品类型',
    order_product_size: '产品规格',
    order_product_number: '数量',
    order_product_sum: '小计',
    graphs_spec_beta: '体验版 一万边',
    graphs_spec_beta_tip:
      '每个用户仅能创建一个体验版图谱，体验有效期为30天，您已创建过哦~',
    graphs_spec_stard_million: '标准版 百万边',
    graphs_spec_stard_thousandsGrand: '标准版 千万边',
    graphs_spec_advance: '高级版 千万边',
    graphs_spec_no_package: '体验版图谱不能开通套餐包',
    graphs_spec_choose_first: '请先选择图谱规格',
    graphs_spec_size_not_match: '图谱版本和图谱规格不匹配',
    creat_task_committed: '您的图谱已开始创建',
    upgrade_task_committed: '您的图谱已经开始升级',
    upgrade_status_not: '当前状态不能进行升级',
    kg_purchase_choose_service: '服务选型',
    kg_purchase_version_confirm: '版本确认',
    kg_purchase_finished: '完成',
    kg_purchase_package: '购买套餐包',
    kg_purchase_package_or_not: '是否购买套餐包',
    kg_purchase_package_type: '套餐类型',
    kg_purchase_package_tip: '知识图谱套餐包',
    kg_purchase_pay: '支付',
    kg_purchase_time_unit_hour: '小时',
    kg_purchase_period: '购买时长',
    kg_purchase_one_month: '一个月',
    kg_purchase_nine_month: '9个月',
    kg_purchase_one_year: '1年',
    kg_purchase_packge_timeout_tip:
      '超出当前套餐包额度或使用时段，将自动转为按需付费',
    kg_purchase_previous_step: '上一步',
    kg_purchase_next_step: '下一步',
    kg_purchase_direct_to_pay: '去支付',
    kg_purchase_create_confirm: '确认创建',
    kg_purchase_package_valid: '套餐包有效期',
    kg_purchase_task_committing: '正在创建知识图谱...',
    kg_purchase_task_committed: '知识图谱创建任务提交成功',
    kg_purchase_task_committed_failed: '知识图谱创建任务提交失败',
    kg_purchase_upgrade_task_committed: '知识图谱升级任务提交成功',
    kg_charge: '充值',
    kg_activity_recommended: '推荐活动',
    kg_package_months: '个月',
    kg_package_year: '1年',
    kg_purchae_tip: '温馨提示',
    kg_purchase_tip1:
      '1.套餐包是按月或按年预先支付费用的实例，相比按需计费提供大额折扣，适合计划长期使用的客户。',
    kg_purchase_tip2:
      '2.套餐实例从购买之日起生效，到期自动结束。到期后也不会延期，且无法退还费用。为了节省您的费用，建议您购买套餐包后立即使用。',
    kg_cost_example: '计费示例：',
    kg_cost_example_detail:
      '例如在2020年1月1日购买了套餐包（标准版“百万边”，购买时长“1年”，图谱数为“1”），则在2021年1月1日之前，1个百万边图谱按预付费套餐包价格收费，超出的按需计费。',
    graphs_to_create: '创建图谱',
    kg_professional_version_confirm: '版本确认',
    kg_professional_charge_mode: '计费模式',
    kg_professional_charge_one_time: '一次性付费',
    kg_professional_service_edition: '服务版本',
    kg_professional_professor_service: '知识图谱专家服务',
    kg_professional_deploy_service: '知识图谱部署实施服务',
    kg_professional_service_spec: '服务规格',
    kg_professional_spec_1_title: '知识图谱高级专家',
    kg_professional_spec_1_content:
      '针对项目实际情况，提供业务数据分析、数据梳理、本体设计、图谱流水线构建等专业服务。',
    kg_professional_spec_2_title: '知识图谱资深顾问',
    kg_professional_spec_2_content:
      '针对项目实际情况，提供业务数据分析、数据梳理、本体设计、图谱流水线构建等专业服务。',
    kg_professional_spec_3_title: '知识图谱高级咨询专家',
    kg_professional_spec_3_content:
      '针对项目实际情况，提供业务数据分析、数据梳理、本体设计、图谱流水线构建等专业服务。',
    kg_professional_spec_4_title: '知识图谱资深咨询专家',
    kg_professional_spec_4_content:
      '针对项目实际情况，提供业务数据分析、数据梳理、本体设计、图谱流水线构建等专业服务。',
    kg_professional_spec_5_title: '知识图谱首席咨询专家',
    kg_professional_spec_5_content:
      '针对项目实际情况，提供业务数据分析、数据梳理、本体设计、图谱流水线构建等专业服务。',
    kg_professional_deploy_title: '知识图谱部署实施服务',
    kg_professional_deploy_content: '提供知识图谱平台的部署服务',
    kg_professional_purchase_amount: '购买数量',
    kg_professional_unit_person_day: '人天',
    kg_professional_unit_suit: '套',
    kg_professional_order_confirm: '订单确认',
    service_spec_choose_first: '请先选择服务规格',
    kg_professional_order_committed_loading: '正在生成订单...',
    kg_professional_order_committed: '订单生成成功',
    kg_professional_order_committed_failed: '订单生成失败',
    kg_professional_order_inquiry_failed: '询价失败',
    kg_professional_order_inquiry_failed_retry: '重试',
    kg_professional_order_inquirying: '计算中...',
    graph_ability_service: '图谱应用',
    graph_ability_no: '您暂未开通图谱应用。',
    graph_ability_enable: 'Enable it.',
    graph_ability_open_failed: '图谱应用开通失败。',
    graph_ability_open_success: '图谱应用开通成功。',
    graph_ability_kbqa_title: '知识图谱问答KBQA',
    graph_ability_kbqa_detail:
      '利用NLP能力理解用户输入的不同类型问题、找到并返回知识图谱中相关的答案。',
    graph_ability_er_title: '实体链接',
    graph_ability_er_detail:
      '识别句子中出现的知识图谱中的实体，并返回实体相关信息。',
    graph_ability_kbqa_experience: '问答体验',
    graph_ability_more: '了解更多',
    graph_ability_api_info: '接口信息',
    graph_ability_status: '状态',
    graph_ability_info: '简介',
    graph_ability_status_running: '启动中',
    graph_ability_status_ready: '已开通',
    graph_ability_status_not_start: '未开通',
    graph_ability_status_failed: '开通失败',
    graph_ability_status_failed_tips: '请重新启动 KBQA 应用',
    graph_ability_open_request_success: '图谱应用开通请求已提交。',
    graph_ability_stop_request_success: '图谱应用关闭请求已提交。',
    graph_ability_kbqa_hello:
      '您好，我是为您服务的客服，请问有什么问题可以帮助您的？',
    graph_ability_kbqa_hello_tip: '比如您可以这样提问：',
    graph_ability_kbqa_placeholder: '请输入内容',
    graph_ability_kbqa_send: '发送',
    graph_ability_kbqa_sorry: '该问题我还没有学会。',
    graph_ability_kbqa_entity: '实体：',
    graph_ability_kbqa_view_json: '查看json',
    graph_ability_kbqa_invalid_tip1: '不允许输入非法字符，比如&lt;a&gt',
    kbqa_random_dialog_sentence1:
      '正在努力理解您的意思，您也可以再具体描述一下问题。',
    kbqa_random_dialog_sentence2: '刚才走神了，您能再把问题具体描述一下么？',
    kbqa_random_dialog_sentence3:
      '不好意思，前两天偷了个懒，有些知识还没来的及学习，这个问题我暂时还答不上来。',
    kbqa_dialog_start: '您好，我是为您服务的客服，请问有什么问题可以帮助您的？',
    kbqa_config: '问答配置',
    kbqa_experience: '问答体验',
    kbqa_lookat_json: '查看json',
    Entity: '实体',
    kg_create_workflow: '图谱构建工作流',
    kg_based_kbqa: '基于图谱的问答',
    kg_visual_edit: '可视化编辑',
    kg_visual_search: '可视化查询和搜索',
    kg_edition_backup: '版本备份',
    kg_query_per_second: 'QPS',
    kg_query_unlimited: '无限制',
    kg_query_limited: '500 次/天',
    kg_backup_adv_limited: '5 个',
    kg_backup_standard_limited: '3 个',
    kg_submit_failed: '提交失败',
    graph_operate_ontology_tip: '本体的值为空，请配置。',
    graph_operate_info_tip: '信息抽取项配置为空请配置，或打开"默认抽取"选项',
    graph_operate_info_max_count: '实体类型最大限度1000条，已超出。',
    graph_operate_info_body_type:
      '实体类型的值为空请配置，或打开"默认抽取"选项',
    graph_operate_info_data_field: '数据字段的值为空，请配置。',
    graph_operate_info_value_fun: '字段函数的值为空，请配置。',
    graph_operate_info_model: '模型抽取模型为空，请配置。',
    graph_operate_known_map_data_filed: '数据字段的值为空，请配置。',
    graph_operate_known_map_source_key: '关系映射的数据字段不能超过{0}。',
    graph_operate_known_map_after: '{0}映射前为空，请配置。',
    graph_operate_known_map_name: '{0}的配置属性name数据字段为空，请配置。',
    graph_operate_known_conflate_max: '属性配置最大限度10条，已超出。',
    graph_operate_known_conflate_attr: '属性配置项的值为空，请配置。',
    graph_operate_known_conflate_block_key: '融合标识符不能超过{0}。',
    graph_operate_known_conflate_empty_id_tip: '融合标识符为空，请配置。',
    graph_operate_known_conflate_empty_condition_tip: '融合条件为空，请配置。',
    graph_operate_known_conflate_group_count: '实体分组不能超过{0}。',
    graph_operate_known_conflate_group_attr_count: '分组内属性不能超过{0}。',
    graph_operate_known_conflate_merge: '{0}的融合标识符为空，请配置。',
    graph_operate_known_conflate_merge_attr: '{0}的融合属性配置为空，请配置。',
    graph_operate_known_conflate_empty: '融合配置为空，请配置。',
    graph_operate_known_conflate_attr_null:
      '{0}的融合属性配置包含空值，请配置。',
    graph_operate_known_conflate_max_row:
      "您还可以添加<span style='color: #FF4C4C'>{0}</span> 行",
    model_version_max_count: '您的版本数量已达上限，无法创建。',
    ontology_delete_tip: '请选择删除的本体。',
    ontology_create_null_tip: '本体内容不能为空，请编辑后保存。',
    ontology_node_attr_tip: '节点的配置属性重名。',
    ontology_node_relation_tip: '请为节点之间的关系设置名称。',
    graph_operate_copy_tip: '只有当图谱处于可用状态时才能复制。',
    graph_operate_delete_tip: '当前图谱状态不能删除',
    cant_not_delete_trial_graph: '试用版图谱不能删除',
    graph_operate_full_update_tip: '只有当图谱处于可用状态时才能全量更新。',
    graph_operate_increment_update_tip:
      '只有当图谱处于可用状态时才能增量更新。',
    graph_operate_preview_pipeline_tip:
      '只有当图谱处于可用、运行中、发布中状态时才能预览流水线。',
    graph_operate_preview_graph_tip:
      '只有当图谱处于可用、运行中状态时才能预览。',
    graphs_status_not_ready: '图谱当前状态不支持执行该操作',
    graph_gremlin_title: 'Gremlin',
    preview_search_rule_1: '模糊匹配',
    preview_search_rule_2: '完全匹配',
    preview_search_placeholder: '搜索实体 name 属性',
    kg_preview_result_not_found: '查询结果为空',
    preview_query_empty: '图谱为空',
    preview_gremlin_title: 'Gremlin',
    preview_filter_multi_hop: '多跳过滤',
    preview_filter_condition: '条件过滤',
    preview_filter_property: '属性',
    preview_multi_hop_start: '实体ID',
    preview_multi_hop_floor: '层数:  ',
    preview_multi_hop_node: '实体过滤',
    preview_multi_hop_edge: '关系过滤',
    preview_multi_hop_attr: '属性',
    preview_multi_hop_type: '类型',
    preview_multi_hop_delete: '删除',
    preview_multi_hop_all_type: '所有类型',
    preview_multi_hop_all_placeholder: '请选择类型',
    preview_multi_hop_attr_condition: '添加过滤条件',
    preview_multi_hop_next_floor: '添加下一层',
    preview_multi_hop_filter: '过滤',
    preview_condition_math: '匹配',
    preview_condition_spot: '实体',
    preview_condition_edge: '关系',
    preview_filter_eq: '等于',
    preview_filter_neq: '不等于',
    preview_filter_has: '存在',
    preview_filter_has_not: '不存在',
    preview_filter_contain: '包含',
    preview_filter_contain_not: '不包含',
    preview_filter_lt: '小于',
    preview_filter_gt: '大于',
    preview_filter_range: '在范围',
    preview_filter_lte: '小于或等于',
    preview_filter_gte: '大于或等于',
    preview_title_back: '返回上一级',
    multihop_start_vertex_id: '请输入起始实体 ID',
    multihop_start_vertex_id_not_null: '起始实体id不能为空',
    kg_preview_menu_view_attributes: '查看属性',
    kg_preview_menu_vertex_hide: '隐藏',
    kg_preview_menu_query_node: '扩线查询',
    kg_preview_menu_query_node_all: 'ALL：双向',
    kg_preview_menu_query_node_in: 'IN ：沿入边',
    kg_preview_menu_query_node_out: 'OUT：沿出边',
    kg_preview_menu_single_hop: '单跳过滤',
    kg_preview_controls_layout: '布局',
    kg_preview_controls_forceLayout: '力引导布局',
    kg_preview_controls_circleLayout: '圆形布局',
    kg_preview_controls_gridLayout: '网格布局',
    kg_preview_controls_radialTreeLayout: '核心单节点布局',
    kg_preview_controls_hierarchicalLayout: '分层布局',
    kg_preview_controls_coseLayout: '自动分群布局',
    kg_preview_singlehop_modal_title: '单跳过滤',
    kg_preview_lengendbox: '图例',
    kg_preview_controls_history: '历史',
    kg_preview_controls_zoom_out: '缩小',
    kg_preview_controls_zoom_in: '放大',
    kg_preview_controls_fit: '适配',
    kg_preview_controls_subGraph: '子图',
    kg_preview_controls_export_graph: '导出图片',
    kg_preview_controls_color: '颜色',
    kg_preview_controls_size: '大小',
    kg_preview_current_node_num: '当前画布区域存在的实体数目',
    kg_preview_current_edge_num: '当前画布区域存在的关系数目',
    kg_preview_all_node_num: '图谱存在的实体数目',
    kg_preview_all_edge_num: '图谱存在的关系数目',
    kg_preview_entity: '实体',
    kg_preview_relation: '关系',
    kg_preview_run_record: '运行记录',
    kg_preview_query_result: '查询结果',
    kg_preview_gremlin_command: 'gremlin命令',
    kg_preview_command_run: '运行',
    kg_preview_command_run_time: '运行时间',
    kg_preview_command_run_failed: '运行失败',
    ontology_edit: '编辑',
    ontology_conflate: '融合',
    ontology_delete: '删除',
    pipeline_workflow_suit: '图谱流水线套件',
    pipeline_workflow_suit_all: '全部',
    pipeline_workflow_suit_shortname: '流水线',
    pipeline_workflow_suit_tip:
      '拖拽套件至右侧画布，将自动填充整条流水线，仅需按要求配置数据源，即可得到图谱。套件内本体、模型、映射、融合不可修改。',
    pipeline_ontology_workflow_suit: '图谱本体组件',
    pipeline_ontology_workflow_suit_shortname: '本体',
    pipeline_ontology_workflow_suit_tip:
      '拖拽本体组件至右侧工作区，将自动填充流水线中的图谱本体。一个图谱有且仅有一个本体，本体组件支持编辑修改，修改后的本体将保存至“我的库”。',
    pipeline_model_workflow_component: '模型组件',
    pipeline_model_workflow_component_shortname: '模型',
    pipeline_model_workflow_component_tip:
      '可直接使用已有模型，或使用算法模板训练你自己的模型。',
    pipeline_work_order_analysis_graph_workflow: '工单分析图谱workflow',
    pipeline_work_order_return_analysis_graph_workflow:
      '工单退单分析图谱workflow',
    pipeline_policy_graph_workflow: '政策图谱workflow',
    pipeline_automobile_maintenance_graph_workflow: '汽车维修图谱workflow',
    pipeline_virus_graph_workflow: '病毒图谱workflow',
    pipeline_virus_ontology: '病毒本体',
    pipeline_automobile_ontology: '汽车本体',
    pipeline_work_order_analysis_ontology: '工单分析本体',
    pipeline_work_order_return_analysis_ontology: '退单分析本体',
    pipeline_work_order_topic_extraction_model: '工单话题抽取模型',
    pipeline_bert_extraction_template: 'Bert抽取算法模板',
    government_affairs_tag: '政务',
    automobile_tag: '汽车',
    medical_treatment_tag: '医疗',
    general_tag: '通用',
    create_model_from_template: '从模板创建模型',
    data_source: '数据源',
    add_data_source: '添加数据源',
    graph_ontology: '图谱本体',
    data_extraction: '信息抽取',
    data_extraction_tips:
      '从数据中抽取待创建图谱的实体、属性信息及实体间的相互关系。',
    knowledge_mapping: '知识映射',
    knowledge_mapping_tips:
      '知识映射是建立从数据抽取出的实体信息与知识图谱本体的映射关系。',
    knowledge_fusion: '知识融合',
    knowledge_fusion_tips:
      '知识融合是指融合来自多个数据源的关于同一个实体或概念的描述信息，对来自不同数据源的知识在统一规范下进行异构数据整合、消歧。',
    graph_report: '图谱质检',
    pipeline2_tips:
      '点击画布中流水线的组件即可自定义编辑组件；拖拽资产库组件至画布，将自动填充配置流水线对应组件。',
    pipeline2_ontology_tips:
      '一个图谱有且仅有一个本体，若需要将2个本体合并成一个使用，请将2个本体拖拽进来，点击“融合”按钮，进行本体合并。',
    pipeline2_ontology_add_tips: '您可以从资产库中将本体拖拽进来',
    pipeline2_ontology_should_conflate_tips:
      '图谱仅支持1个本体文件，请编辑将2个本体进行融合，或删除1个本体',
    pipeline2_ontology_empty_ontology: '本体未配置，请配置。',
    pipeline2_increment_update_lock_tips: '增量更新无法编辑原流水线',
    pipeline2_config_save_success: '流水线配置保存成功',
    pipeline2_run_success: '流水线运行成功',
    pipeline2_apply_assets_pipeline_confirm_desc:
      '应用图谱流水线配置会覆盖现有配置，是否继续？',
    pipeline2_apply_assets_pipeline_success: '图谱流水线套件应用成功',
    pipeline2_apply_assets_pipeline_failed: '图谱流水线套件应用失败',
    pipeline2_pipeline_delete_pipeline_config: '配置不为空，是否删除该流水线？',
    pipeline2_pipeline_clean_component_text: '清空',
    pipeline2_pipeline_clean_ontology_component:
      '清空本体将清空<span class="text-red">所有</span><span class="text-red">知识映射、知识融合</span>的组件内容，是否继续清空？',
    pipeline2_pipeline_delete_subpipe_tips: '删除单条数据源对应的工作流',
    return_console: '返回图谱管理'
  },
  Pipeline: {
    service_title: '知识图谱',
    menu_graph: '我的图谱',
    menu_assets: '我的图谱资产库',
    menu_ontology: '我的本体',
    menu_model: '我的模型',
    menu_helpCenter: '帮助中心',
    graph_apply: '申请公测',
    graph_enabled: '您尚未开通知识图谱服务',
    graph_introduce:
      '知识图谱服务是面向各类企业的一站式知识计算平台，提供源数据管理、本体管理、图谱管理等功能，帮助用户从0到1快速构建知识图谱，管理知识图谱流水线任务。',
    back: '返回',
    pipeline2_delete_ontology: '确定删除本体？',
    pipeline2_change_ontology:
      '本体修改后，知识映射和知识融合需要重新配置，确定修改本体？',
    fuse_select_concept: '请选择一个概念',
    data_source_modified_tips:
      '确定修改数据源？数据源修改后，可能会影响信息抽取和知识映射的配置，修改后请检查信息抽取和知识映射的配置。',
    ontology_modified_tips:
      '确定修改本体？本体修改后，可能会影响知识映射和知识融合的配置，修改后请检查知识映射和知识融合的配置。',
    data_extraction_modified_tips:
      '确定修改信息抽取规则？信息抽取规则修改后，可能会影响知识映射的配置，修改后请检查知识映射的配置。',
    model_not_released: '模型未发布',
    graph_build_report_tips:
      '图谱质检为非必选流程，关闭和开启状态均不影响图谱构建。开启质检任务后，后台会对当前版本的知识图谱的知识质量和流水线构建进行评估，并生成质检报告。',
    CSV: 'CSV',
    XLSX: 'XLSX',
    JSON: 'JSON',
    dataSourceOrigin: '数据源类型',
    builtInDataSource: '预置数据源',
    userDataSource: '用户数据源',
    autoPublish: '自动发布',
    autoPublishTips: '运行成功后自动发布版本'
  },
  Ontology: {
    ontology_title: '本体管理',
    ontology_name: '本体名称',
    ontology_create: '创建本体',
    ontology_modify: '修改本体',
    ontology_delete: '删除',
    ontology_batch_delete: '批量删除',
    ontology_preview: '预览',
    ontology_edit: '编辑',
    ontology_copy: '复制',
    ontology_export: '导出',
    ontology_table_name: '名称',
    ontology_table_updated: '更新时间',
    ontology_table_concepts: '概念个数',
    ontology_table_relations: '关系个数',
    ontology_table_description: '描述',
    ontology_table_operation: '操作',
    ontology_table_search_placeholde: '请输入本体名称',
    ontology_visual_search_placeholder: '请输入概念或关系名称',
    ontology_ontology_concept_name_search_placeholder: '请输入概念名称',
    ontology_visual_search_no_result: '无搜索结果',
    ontology_reach_limit: '本体数量已达上限',
    ontology_create_title: '创建本体',
    ontology_conflate: '本体融合',
    ontology_edit_title: '编辑本体',
    ontology_delete_title: '删除本体',
    ontology_unsaved: '已修改',
    ontology_saving: '正在保存中',
    ontology_saved: '已保存',
    ontology_autosaved: '已自动保存',
    ontology_not_modify: '本体未修改',
    ontology_confirm_save: '确认保存',
    ontology_from_graph_save: '本体修改后会新建一个图谱版本, 确定保存？',
    ontology_from_graph_modify_suffix: '修改本体',
    ontology_delete_title_tip: '删除本体将不可恢复，是否确认删除？',
    ontology_create_name: '本体名称',
    ontology_create_name_tips: '请输入本体名称',
    ontology_name_placeholder: '请输入名称',
    ontology_attr_placeholder: '请输入属性名称',
    ontology_create_description: '本体描述',
    ontology_description_placeholder: '请输入描述',
    ontology_export_title: '导出本体',
    ontology_import_title: '导入本体',
    ontology_preview_title: '预览本体',
    ontology_draw_mode_concept: '概念编辑模式',
    ontology_draw_mode_relation: '关系编辑模式',
    ontology_draw_concept: '概念',
    ontology_draw_concept_name: '概念名称',
    ontology_draw_concept_name_modify: '修改概念名称',
    ontology_draw_concept_name_valid: '概念名不可重复',
    ontology_draw_relation: '关系',
    ontology_draw_relation_name: '关系名称',
    ontology_draw_relation_name_valid: '关系名不可重复',
    ontology_draw_property: '属性',
    ontology_draw_property_name: '名称',
    ontology_draw_property_name_valid: '属性名不可重复',
    ontology_draw_property_value_type: '单值/多值',
    ontology_draw_property_value_type_single: '单值',
    ontology_draw_property_value_type_multi: '多值',
    ontology_draw_property_type: '类型',
    ontology_draw_add: '新建属性',
    ontology_draw_add2: '添加属性',
    ontology_draw_more: '更多',
    ontology_draw_coolapse: '收起',
    ontology_draw_shortcut_title: '快捷方式',
    ontology_draw_save: '保存',
    ontology_draw_save_tip: '请先保存',
    ontology_draw_clear: '清空画布',
    ontology_draw_import: '导入',
    ontology_draw_import_warn_tip:
      '导入新本体将覆盖当前编辑的本体，继续导入吗？',
    ontology_draw_import_confirm: '继续导入',
    ontology_draw_export: '导出',
    ontology_draw_shortcut: '快捷键',
    ontology_draw_zoomin: '放大',
    ontology_draw_zoomout: '缩小',
    ontology_draw_fit: '适配',
    ontology_draw_undo: '撤销',
    ontology_draw_redo: '恢复',
    ontology_draw_select_click: '单击多选',
    ontology_draw_select_all: '全选',
    ontology_draw_select_all_node: '选择所有概念',
    ontology_draw_select_all_edge: '选择所有关系',
    ontology_draw_select_ctrl_drag: '框选',
    ontology_draw_select_delete: '删除画布选中的概念或关系',
    ontology_draw_unselect_all: '取消选择',
    ontology_draw_add_node: '新建概念',
    ontology_draw_copy_node: '复制概念',
    ontology_draw_copy_node_old: '原概念名',
    ontology_draw_copy_node_new: '新概念名',
    ontology_draw_add_edge: '新建关系',
    ontology_draw_guide: '操作指南',
    ontology_draw_guide_node: '添加节点',
    ontology_draw_guide_node_detail:
      '拖拽节点图标，鼠标悬放在画布上的目标位置,创建好的节点可以随意移动位置，鼠标右击可以删除节点。',
    ontology_draw_guide_attribute: '节点属性配置',
    ontology_draw_guide_attribute_detail:
      '点击节点会出现属性配置面板，可以配置节点名称，在属性栏可以配置节点各项属性（特别注意，节点属性必须在概念编辑模式下编辑）。',
    ontology_draw_guide_relation: '添加关系',
    ontology_draw_guide_relation_detail:
      '双击节点，创建单节点的关系曲线。两个节点之间的关系创建：点击一个节点拖拽出一条虚线，拖动到目标节点，点击目标节点，就创建好了节点关系。鼠标右击可以删除关系。',
    ontology_draw_guide_name: '关系名称配置',
    ontology_draw_guide_name_detail:
      '点击关系线会出现关系名称配置面板，可以配置关系名称（特别注意，关系名称必须在关系编辑模式下编辑）。',
    ontology_draw_guide_text:
      '鼠标双击画布空白处可新建概念。按住 a 键的同时，鼠标点击一个概念并拖拽出一条线，移动到目标概念可新建关系。',
    ontology_management_import_success: '本体导入成功',
    ontology_management_export_success: '本体导出成功',
    ontology_concepts_property_reach_limit: '概念属性数量必须在 1~255 之间'
  },
  Model: {
    menu_model: '模型管理',
    model_create: '创建模型',
    model_table_name: '模型名称',
    model_table_obs: 'OBS路径',
    model_table_description: '描述',
    model_input_description_tip: '请输入描述内容',
    model_table_operation: '操作',
    model_version_name: '版本',
    model_all_status: '全部状态',
    model_status_using: '使用中',
    model_status_available: '可用',
    model_status_training: '训练中',
    model_status_unavailable: '不可用',
    model_remain_count:
      "您还可以创建<span style='padding: 0 5px'>{0}</span>个模型。",
    model_create_title: '创建模型',
    model_name: '模型名称',
    model_name_placeholder: '请输入模型名称',
    model_name_tip: '模型名称创建后暂不支持修改。',
    model_template: '模型模板',
    model_template_placeholder: '请选择模型模板',
    model_data_type: '数据类型',
    model_data_placeholder: '请选择数据类型',
    model_obs: 'OBS桶',
    model_folder: '文件夹',
    model_file: '文件',
    model_published_version: '已发布版本',
    model_evaluation: '模型评估',
    model_status: '状态',
    model_precision: '准确率',
    model_recall: '召回率',
    model_path: '训练数据路径',
    model_update: '更新时间',
    model_create_time: '开始时间',
    model_finish_time: '结束时间',
    model_schema: '查看schema',
    model_version: '版本管理',
    model_create_version: '创建新版本',
    model_create_new_version: '创建新版本',
    model_modify_desc_success: '修改版本描述成功',
    model_evaluation_tip:
      '准确率(Precision)是在被所有预测为正的样本中实际为正样本的概率，\n召回率(Recall)是在实际为正的样本中被预测为正样本的概率，\nF1值综合考虑准确率和召回率的影响，由两者计算而来，越接近1代表模型越好',
    model_vs_published: '已发布',
    model_vs_trained: '训练完成',
    model_vs_training: '训练中',
    model_vs_failed: '训练失败',
    model_vs_create_failed: '版本创建失败',
    model_vs_stop: '停止',
    model_stop_vs: '停止训练',
    model_vs_publish: '发布',
    model_publish_vs: '发布版本',
    model_vs_delete: '删除',
    model_delete_vs: '删除版本',
    model_vs_modify: '修改',
    model_modify_vs: '修改版本',
    model_vs_view: '查看',
    model_vs_offline: '停用',
    model_vs_take_offline: '停用',
    model_take_offline_vs: '停用版本',
    model_take_offline_ok: '确定要停用{0}版本吗？',
    model_take_offline_tip:
      '停用已发布的版本会导致模型无法使用，同时会影响已经配置的图谱，请谨慎操作。',
    model_publish_version_ok: '确定要发布{0}版本吗？',
    model_publish_version_tip:
      '发布此版本将会停用已发布的版本，同时会影响已经配置的图谱，请谨慎操作。',
    model_delete_version_ok: '确定要删除版本吗？',
    model_delete_version_tip: '删除版本后将无法恢复，请谨慎操作。',
    model_stop_version_ok: '确定要停止训练该版本吗？',
    model_model_parameter: '模型参数',
    model_parameter: '参数',
    model_parameter_info: '参数信息',
    model_parameter_value: '参数值',
    model_parameter_max_length:
      '待训练数据中句子的最大截断长度，需要根据模型大小、数据句子长度衡量决定，推荐范围：[64,1024]。',
    model_parameter_batch_siz:
      '模型训练时批数据大小，需要根据GPU显存大小、模型、max_len等因素指定，最好能正好占满显存，推荐范围：[8,1024]',
    model_parameter_learning_rate:
      '模型的训练速率，不要太大。推荐范围：[0.00001, 0.0001]',
    model_parameter_epochs: '模型训练迭代次数，推荐范围：[5,100]',
    model_max_count: '您的模型数量已达上限，无法创建。',
    model_min_count: '您还未创建模型。',
    model_query_empty: '查询结果为空。',
    model_create_success: '模型{0}创建成功。',
    model_delete_md: '删除模型',
    model_delete_ok: '确定要删除{0}模型？',
    model_delete_tip: '删除模型后将无法恢复，请谨慎操作。',
    model_delete_success: '模型{0}删除成功。',
    model_delete_failed: '删除模型{0}失败。',
    model_edit_success: '模型{0}编辑成功。',
    model_edit_failed: '模型{0}编辑失败。',
    import_back_previous: '返回上一级',
    ontology_description_placeholder: '请输入描述'
  },
  HelpCenter: {
    menu_helpCenter: '帮助中心',
    guidance_help_docs: '帮助文档',
    help_center_introduction: '产品介绍',
    help_center_introduction_detail: '知识图谱服务产品介绍',
    help_center_guide: '用户指南',
    help_center_guide_detail: '引导新用户一步步完成知识图谱的建立',
    help_center_glossary: '常见问题',
    help_center_glossary_detail: '知识图谱常见问题',
    help_center_api: 'API参考',
    help_center_api_detail: '知识图谱服务API参考',
    create_graph: '普通配置构建',
    guidance_use_steps: '使用步骤',
    data_title: '数据源配置',
    ontology_title: '本体选择',
    info_title: '信息抽取',
    map_title: '知识映射',
    fuse_title: '知识融合',
    help_center_data_detail: '创建图谱名称和从OBS导入数据',
    help_center_ontology_detail:
      '创建本体，1.可自定义本体，2.选择提前创建好的本体，3.从OBS直接导入本体',
    help_center_info_detail: '从源数据中提取需要用于构建图谱的信息',
    help_center_map_detail: '将信息抽取结果映射到本体上',
    help_center_fuse_detail: '完成实体对齐、融合模型配置'
  },
  GraphReport: {
    result: '质检结果',
    versionName: '版本名称',
    versionDescription: '版本描述',
    createTime: '创建时间',
    updateTime: '最后更新时间',
    resultOverviewTab: '质检结果总览',
    qualityAssessTab: '知识质量评估',
    pipelineAssessTab: '流水线配置质检',
    knowledgeQualityAssess: '知识质量评估',
    pipelineBuiltAssess: '流水线构建质检',
    entityTypeName: '数据类型名',
    entityTypeNameTips:
      'XLSX文件中每一个工作簿为一类数据，工作簿名为数据类型名。CSV文件的每一个文件为一类数据，文件名(不包含.csv后缀)为数据类型名。多行JSON文件中，"entity_type"的值相同的JSON字符串为一类数据，"entity_type"的值为数据类型名。',
    entityTypeCount: '数据量',
    entityTypeColumns: '数据字段',
    dataStageSearchPlaceholder: '请输入数据类型名',
    dataStageResultNormal: '数据配置无异常',
    dataStageResultEmptyInput: '未读取到有效数据内容',
    dataStageResultEmptyLabel: '有数据类别为空',
    dataStageResultNoLabelData: '有数据未匹配上',
    dataStageViewError: '查看详情',
    dataStageNoMatchDataDetailTitle: '异常数据',
    dataStageNoMatchDataDetailTitleTips:
      '格式错误的JSON字符串无法被解析成图谱数据',
    dataStageNoMatchDataDetailTips:
      '请检查每条JSON字符串是否包含数据类别(entity_type)字段',
    ontologyStageConceptName: '概念名称',
    ontologyStageRelationCount: '关系数量',
    ontologyStageProperty: '属性',
    ontologyStageSearchPlaceholder: '请输入概念名称',
    ontologyStagePipelineEmptyDescription: '暂无质检结果',
    ieStageExtractionName: '抽取项名称',
    ieStageValueFunction: '抽取函数',
    ieStageExtractionKeyCount: '字段数量',
    ieStageExtractionCount: '抽取结果数量',
    ieStageExtractionErrorCount: '抽取异常数量',
    ieStageExtractionOption: '操作',
    ieStageExtractionViewError: '查看异常',
    ieStageExtractionViewErrorTitle: '字段函数-查看异常',
    ieStageExtractionViewErrorEntityType: '实体类型',
    ieStageExtractionViewErrorEmptyData: '未抽取到字段的原数据',
    ieStageAlgorithmExtractionTitle: '模型三元组抽取结果列表',
    ieStageAlgorithmTableColumnSubjectType: 'subject_type',
    ieStageAlgorithmTableColumnPredicate: 'predicate',
    ieStageAlgorithmTableColumnObjectType: 'object_type',
    ieStageAlgorithmTableColumnCount: '抽取数量',
    ieStagePipelineResultEmptyItem: '字段抽取出现异常',
    ieStagePipelineResultEmptyIe: '字段抽取全部异常',
    ieStagePipelineSuccessDescription: '结果正常',
    mappingStageMapType: '类型映射',
    mappingStageMapTypeBefore: '映射前',
    mappingStageMapTypeAfter: '映射后',
    mappingStageMapTypeCount: '映射后数量',
    mappingStagePropertyMap: '属性映射',
    mappingStageRelationMap: '关系映射',
    mappingStageProperty: '属性',
    mappingStageRelation: '关系',
    mappingStageSourceKey: '数据字段',
    mappingStagePropertyTips: '该类型在本体中定义的属性',
    mappingStageSourceKeyTips: '源数据中对应数据字段',
    mappingStageSourceKeyPropertyCount: '映射后的属性值数量',
    mappingStageSourceKeyRelationCount: '映射后的关系值数量',
    mappingStageWarnDescriptionConcept: '类型映射',
    mappingStageWarnDescriptionProperty: '属性映射',
    mappingStageWarnDescriptionRelation: '关系映射',
    mappingStageSuccessDescription: '结果正常',
    mappingStageErrorDescription: '结果异常',
    conflationStageBeforeConflatedEntityCount: '被融合的实体个数',
    conflationStageAfterConflatedEntityCount: '融合后的实体个数',
    conflationStageConflatedEntityCount: '融合标识符相同的个数',
    conflationStageSimilarityMean: '融合相似度均值',
    conflationStageNoSamples: '无融合实体',
    conflationStageSamples: '融合结果示例',
    conflationStageSamplesNotVerified: '未校验',
    conflationStageSamplesHasVerified: '已校验',
    conflationStageSamplesGroup: '组别',
    conflationStageSamplesProperty: '融合实体属性',
    conflationStageSamplesPropertyTips:
      '判断两个实体相似度所依据的实体属性，每组可选择多个属性判断相似度。',
    conflationStageSamplesSimilarityFunction: '相似度函数',
    conflationStageSamplesSimilarity: '相似度',
    conflationStageSamplesEntity1: '实体1',
    conflationStageSamplesEntity2: '实体2',
    conflationStageSamplesConfirmRight: '正确',
    conflationStageSamplesConfirmWrong: '错误',
    conflationStageSamplesVerificationResult: '验证结果',
    conflationStageSamplesAccuracy: '准确率',
    conflationStageSamplesAccurateCount: '准确数',
    conflationStageSamplesInaccurateCount: '不准确数',
    conflationStageWarnDescription: '融合结果待验证',
    conflationStageSuccessDescription: '融合结果已验证',
    conflationStageEmptyDescription: '暂无之间结果',
    conflationStagePipelineEmptyDescription: '查看融合示例'
  },
  KbqaConfig: {
    tabs: {
      elementLinkConfig: '元素链接配置',
      qaTemplateConfig: '问答模板配置',
      qaOnlineTest: '问答体验'
    },
    qaOntologyConfig: {
      list: {
        buttons: {
          create: '创建',
          debug: '测试',
          import: '导入',
          export: '导出',
          publish: '发布',
          batchDelete: '批量删除',
          searchPlaceholder: '请输入名称'
        },
        tableHeader: {
          name: '概念名称',
          description: '描述',
          updatedTime: '更新时间',
          workTime: '生效时间',
          operation: '操作'
        },
        table: {
          notPublished: '未生效'
        },
        operation: {
          view: '查看',
          edit: '编辑',
          delete: '删除'
        },
        tips: {
          deleteTemplateTitle: '删除链接配置',
          deleteTemplateContent: '删除链接配置将不可恢复，是否确认删除？',
          deleteTemplateContent2: '注：删除配置后，需发布后才能生效。',
          deleteTemplateSuccess: '删除成功'
        },
        viewConfig: {
          title: '查看元素链接配置'
        }
      },
      config: {
        buttons: {
          back: '返回配置列表',
          confirm: '确定',
          save: '保存',
          cancel: '取消'
        },
        basic: {
          title: '配置基本信息',
          conceptName: '概念名称',
          description: '配置描述',
          descriptionPlaceholder: '请输入描述信息'
        },
        steps: {
          title: '元素链接配置',
          editMode: '配置模式',
          interactiveMode: '交互',
          codeMode: '代码编辑',
          concept: '概念 CONCEPT',
          conceptProperty: '概念属性 CONCEPT_PROPERTY',
          relation: '概念间关系 CONCEPT_RELATION'
        },
        concept: {
          name: '元素名称',
          synonym: '同义词',
          synonymPlaceholder: '添加同义词',
          ontologyWeight: '权重',
          entityWeight: '实体实例权重'
        },
        conceptProperty: {
          name: '元素名称',
          synonym: '同义词',
          synonymPlaceholder: '添加同义词',
          ontologyWeight: '权重',
          entityWeight: '属性值权重'
        },
        relation: {
          name: '元素名称',
          synonym: '同义词',
          synonymPlaceholder: '添加同义词',
          ontologyWeight: '权重'
        },
        tips: {
          createOntologyConfigSuccess: '创建元素链接成功',
          updateOntologyConfigSuccess: '更新元素链接成功',
          conceptNameErrorMsg: '请选择概念名称',
          jsonFormatErrorMsg: 'JSON 格式错误',
          configErrorMsg: '配置对象格式错误',
          weightConfigErrorMsg: '权重范围为0~1，最多保留2位小数',
          synonymConfigErrorMsg: {
            concept: '概念同义词配置错误',
            conceptProperty: '概念属性同义词配置错误',
            conceptRelation: '概念间关系同义词配置错误',
            tiTagsMaxLengthRule: '同义词数量不能超过50个',
            tiTagsInputTextLengthRule: '文本长度不能超过10个字符'
          },
          ontologyWeightConfigErrorMsg: {
            concept: '概念权重配置错误',
            conceptProperty: '概念属性权重配置错误',
            conceptRelation: '概念间关系权重配置错误'
          },
          entityWeightConfigErrorMsg: {
            concept: '概念实体实例权重配置错误',
            conceptProperty: '概念属性属性值权重配置错误'
          },
          routerChangedTitle: '确定要离开界面吗？',
          routerChangedDesc:
            '当前编辑的内容还没有保存，您是否要保存当前编辑的内容？'
        }
      },
      debug: {
        title: '元素链接测试',
        question: '问题',
        questionPlaceholder: '请输入问题',
        result: '返回结果',
        buttons: {
          test: '测试'
        },
        tableHeader: {
          token: '链接词',
          position: '位置',
          type: '链接类型',
          score: '链接分数',
          result: '链接结果',
          operation: '操作'
        },
        operation: {
          viewDetail: '查看详情'
        }
      },
      import: {
        title: '导入元素配置',
        name: '配置名称',
        failedTips: '导入任务失败'
      },
      export: {
        title: '导出元素配置',
        disabledRadioTips: '请选择文件夹',
        failedTips: '导出任务失败'
      },
      batchDelete: {
        title: '批量删除',
        content: '删除配置后将不可恢复，是否继续？',
        content2: '注：删除配置后，需发布后才能生效。',
        successTips: '批量删除成功',
        failedTips: '批量删除失败'
      },
      publish: {
        title: '发布',
        content: '所有元素配置将生效，是否确认发布？',
        successTips: '发布成功',
        failedTips: '发布失败'
      }
    },
    qaTemplate: {
      list: {
        buttons: {
          create: '创建',
          debug: '测试',
          import: '导入',
          export: '导出',
          publish: '发布',
          batchDelete: '批量删除',
          searchPlaceholder: '请输入名称'
        },
        tableHeader: {
          name: '模板名称',
          updatedTime: '更新时间',
          workTime: '生效时间',
          operation: '操作'
        },
        table: {
          notPublished: '未生效'
        },
        operation: {
          view: '查看',
          edit: '编辑',
          delete: '删除'
        },
        tips: {
          deleteTemplateTitle: '删除模板',
          deleteTemplateContent: '删除模板将不可恢复，是否确认删除？',
          deleteTemplateContent2: '注：删除模板后，需发布后才能生效。',
          deleteTemplateSuccess: '删除成功'
        },
        viewConfig: {
          title: '查看模板配置'
        }
      },
      config: {
        buttons: {
          back: '返回配置列表',
          confirm: '确定',
          save: '保存',
          test: '测试',
          cancel: '取消',
          addElement: '添加元素',
          addLink: '添加条件',
          addLinkTipsRequire: '请先添加模板元素序列',
          edit: '编辑',
          delete: '删除'
        },
        basic: {
          title: '配置基本信息',
          name: '模板名称'
        },
        steps: {
          title: '模板配置',
          editMode: '配置模式',
          interactiveMode: '交互',
          codeMode: '代码编辑',
          element: '模板元素序列',
          link: '元素的关联条件',
          gremlinPattern: '图谱查询模板',
          askPattern: '追问模板'
        },
        tableHeader: {
          name: '元素编号',
          code: '元素代码',
          value: '元素限定取值',
          operation: '操作'
        },
        operation: {
          edit: '编辑',
          delete: '删除'
        },
        element: {
          configTitle: '标准问答元素配置',
          type: '元素类型',
          concept: '概念',
          conceptLabel: '概念名',
          conceptProperty: '概念属性',
          conceptPropertyName: '属性名',
          conceptPropertyLabel: '所属概念名',
          conceptRelation: '概念间关系',
          conceptRelationName: '关系名',
          sourceEntityConcept: '头实体概念',
          targetEntityConcept: '尾实体概念',
          entity: '实体',
          entityName: '实体名',
          entityLabel: '所属概念名',
          propertyString: '实体属性值',
          propertyStringName: '属性名',
          propertyStringValue: '属性值',
          propertyStringLabel: '所属概念名',
          keyword: '关键词',
          propertyTime: '时间',
          propertyDate: '日期',
          propertyNumber: '数字',
          compareWord: '比较词'
        },
        link: {
          element1: '元素1',
          element2: '元素2',
          relation: '元素间关系',
          equal: '相等'
        },
        gremlin: {
          placeholder: '请输入 Gremlin'
        },
        ask: {
          placeholder: '请输入追问模板'
        },
        tips: {
          createTemplateSuccess: '创建模板成功',
          updateTemplateSuccess: '更新模板成功',
          templateNameErrorMsg: '模板名称输入有误',
          askPatternErrorMsg: '追问模板输入有误',
          addContentErrorMsg: '请添加模板元素序列',
          jsonFormatErrorMsg: 'JSON 格式错误',
          configErrorMsg: '配置对象格式错误',
          routerChangedTitle: '确定要离开界面吗？',
          routerChangedDesc:
            '当前编辑的内容还没有保存，您是否要保存当前编辑的内容？',
          tiTagsMaxLengthRule: '配置数量不能超过50个',
          tiTagsInputTextLengthRule: '文本长度不能超过30个字符'
        }
      },
      debug: {
        title: '问答模板测试',
        singleTitle: '当前问答模板测试',
        question: '问题',
        questionPlaceholder: '请输入问题',
        result: '返回结果',
        buttons: {
          test: '测试'
        },
        tableHeader: {
          matchTemplateName: '匹配模板',
          matchElement: '匹配元素',
          matchScore: '匹配分数',
          conditionScore: '条件得分',
          elementScore: '元素得分',
          templateScore: '模板得分',
          operation: '操作'
        },
        operation: {
          viewDetail: '查看详情'
        }
      },
      import: {
        title: '导入模板',
        failedTips: '导入任务失败'
      },
      export: {
        title: '导出模板',
        name: '导出文件名',
        disabledRadioTips: '请选择文件夹',
        failedTips: '导出任务失败'
      },
      batchDelete: {
        title: '批量删除',
        content: '删除模板后将不可恢复，是否继续？',
        content2: '注：删除模板后，需发布后才能生效。',
        successTips: '删除模板成功',
        failedTips: '删除模板失败'
      },
      publish: {
        title: '发布',
        content: '所有模板将生效，是否确认发布？',
        successTips: '发布成功',
        failedTips: '发布失败'
      }
    },
    onlineTest: {}
  }
};
