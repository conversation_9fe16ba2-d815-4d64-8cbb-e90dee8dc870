var register = function ($) {
  function K(q, Q) {
    void 0 === q.scratch('_undoRedo') && q.scratch('_undoRedo', {});
    var ae = q.scratch('_undoRedo');
    return void 0 === Q ? ae : ae[Q];
  }
  function V(q, Q, ae) {
    var he = K(q);
    (he[Q] = ae), q.scratch('_undoRedo', he);
  }
  $ &&
    $('core', 'undoRedo', function (q, Q) {
      var ae = this,
        he =
          K(ae, 'instance') ||
          (function B(q) {
            var Q = {
              options: {
                isDebug: !1,
                actions: {},
                undoableDrag: !0,
                stackSizeLimit: void 0,
                beforeUndo: function () {},
                afterUndo: function () {},
                beforeRedo: function () {},
                afterRedo: function () {},
                ready: function () {}
              },
              actions: {},
              undoStack: [],
              redoStack: [],
              reset: function (ae, he) {
                (this.undoStack = ae || []), (this.redoStack = he || []);
              },
              undo: function () {
                if (!this.isUndoStackEmpty()) {
                  var ae = this.undoStack.pop();
                  q.trigger('beforeUndo', [ae.name, ae.args]);
                  var he = this.actions[ae.name]._undo(ae.args);
                  return (
                    this.redoStack.push({
                      name: ae.name,
                      args: he
                    }),
                    q.trigger('afterUndo', [ae.name, ae.args, he]),
                    he
                  );
                }
                this.options.isDebug &&
                  console.log(
                    'Undoing cannot be done because undo stack is empty!'
                  );
              },
              redo: function () {
                if (!this.isRedoStackEmpty()) {
                  var ae = this.redoStack.pop();
                  q.trigger(ae.firstTime ? 'beforeDo' : 'beforeRedo', [
                    ae.name,
                    ae.args
                  ]),
                    ae.args || (ae.args = {}),
                    (ae.args.firstTime = !!ae.firstTime);
                  var he = this.actions[ae.name]._do(ae.args);
                  return (
                    this.undoStack.push({
                      name: ae.name,
                      args: he
                    }),
                    null != this.options.stackSizeLimit &&
                      this.undoStack.length > this.options.stackSizeLimit &&
                      this.undoStack.shift(),
                    q.trigger(ae.firstTime ? 'afterDo' : 'afterRedo', [
                      ae.name,
                      ae.args,
                      he
                    ]),
                    he
                  );
                }
                this.options.isDebug &&
                  console.log(
                    'Redoing cannot be done because redo stack is empty!'
                  );
              },
              do: function (ae, he) {
                return (
                  (this.redoStack.length = 0),
                  this.redoStack.push({
                    name: ae,
                    args: he,
                    firstTime: !0
                  }),
                  this.redo()
                );
              },
              undoAll: function () {
                for (; !this.isUndoStackEmpty(); ) this.undo();
              },
              redoAll: function () {
                for (; !this.isRedoStackEmpty(); ) this.redo();
              },
              action: function (ae, he, U) {
                return (
                  (this.actions[ae] = {
                    _do: he,
                    _undo: U
                  }),
                  this
                );
              },
              removeAction: function (ae) {
                delete this.actions[ae];
              },
              isUndoStackEmpty: function () {
                return 0 === this.undoStack.length;
              },
              isRedoStackEmpty: function () {
                return 0 === this.redoStack.length;
              },
              getUndoStack: function () {
                return this.undoStack;
              },
              getRedoStack: function () {
                return this.redoStack;
              }
            };
            return Q;
          })(ae);
      if ((V(ae, 'instance', he), q)) {
        // eslint-disable-next-line no-prototype-builtins
        for (var U in q) he.options.hasOwnProperty(U) && (he.options[U] = q[U]);
        if (q.actions) for (let U in q.actions) he.actions[U] = q.actions[U];
      }
      if (!K(ae, 'isInitialized') && !Q) {
        var oe = (function ne(q) {
          function ae(b, P, m) {
            for (
              var D = m
                  ? P
                  : (function Q(b) {
                      for (var P = {}, m = 0; m < b.length; m++)
                        P[b[m].id()] = !0;
                      return b.filter(function (T, x) {
                        'number' == typeof T && (T = x);
                        for (var M = T.parent()[0]; null != M; ) {
                          if (P[M.id()]) return !1;
                          M = M.parent()[0];
                        }
                        return !0;
                      });
                    })(P),
                T = 0;
              T < D.length;
              T++
            ) {
              var x = D[T],
                M = x.position('x'),
                C = x.position('y');
              x.isParent() ||
                x.position({
                  x: M + b.x,
                  y: C + b.y
                }),
                ae(b, x.children(), !0);
            }
          }
          function he(b) {
            return 'string' == typeof b ? q.$(b) : b;
          }
          function U(b) {
            return he(b).restore();
          }
          function oe(b) {
            var P = {};
            return (
              q
                .nodes()
                .not(':parent')
                .positions(function (m, D) {
                  'number' == typeof m && (m = D),
                    (P[m.id()] = {
                      x: m.position('x'),
                      y: m.position('y')
                    });
                  var T = b[m.id()];
                  return {
                    x: T.x,
                    y: T.y
                  };
                }),
              P
            );
          }
          function ve(b) {
            var P = {};
            if (b.firstTime) {
              var m = null == b.parentData ? null : b.parentData,
                D = b.nodes.union(b.nodes.descendants());
              (P.elesToRestore = D.union(D.connectedEdges())),
                (P.movedEles = b.nodes.move({
                  parent: m
                })),
                ae(
                  {
                    x: b.posDiffX,
                    y: b.posDiffY
                  },
                  P.movedEles
                );
            } else
              (P.elesToRestore = b.movedEles.remove()),
                (P.movedEles = b.elesToRestore.restore());
            return (
              b.callback &&
                ((P.callback = b.callback), b.callback(P.movedEles)),
              P
            );
          }
          function re(b) {
            var P = {};
            if (b.firstTime) {
              let m = null == b.parentData ? null : b.parentData;
              let D = b.nodes.union(b.nodes.descendants());
              let T = {};
              D.forEach(function (C) {
                C.parent().id() ? (T[C.id()] = C.parent()) : (T[C.id()] = null);
              });
              var x = {};
              D.forEach(function (C) {
                x[C.id()] = {};
                x[C.id()].x = C.position('x');
                x[C.id()].y = C.position('y');
              });
              P.oldParent = T;
              P.oldPosition = x;
              P.newParent = m;
              P.movedEles = D;
              b.nodes
                .move({
                  parent: m
                })
                .nodes();
              ae(
                {
                  x: b.posDiffX,
                  y: b.posDiffY
                },
                P.movedEles
              );
            } else P.oldParent = {};
            b.movedEles.forEach(function (C) {
              C.parent().id()
                ? (P.oldParent[C.id()] = C.parent())
                : (P.oldParent[C.id()] = null);
            });
            P.oldPosition = {};
            b.movedEles.forEach(function (C) {
              P.oldPosition[C.id()] = {};
              P.oldPosition[C.id()].x = C.position('x');
              P.oldPosition[C.id()].y = C.position('y');
            });
            P.newParent = b.oldParent;
            P.movedEles = b.movedEles;
            P.movedEles.forEach(function (C) {
              'object' != typeof P.newParent
                ? C.move({
                    parent: P.newParent
                  })
                : null == P.newParent[C.id()]
                ? C.move({
                    parent: null
                  })
                : C.move({
                    parent: P.newParent[C.id()].id()
                  }),
                C.position(b.oldPosition[C.id()]);
            });
            return (
              b.callback &&
                ((P.callback = b.callback), b.callback(P.movedEles)),
              P
            );
          }
          function O(b, P) {
            for (
              var m = [], T = K(q, 'instance').actions, x = 0;
              x < b.length;
              x++
            )
              // eslint-disable-next-line no-prototype-builtins
              if (!T.hasOwnProperty((M = b[x]).name))
                throw (
                  'Action ' + M.name + ' does not exist as an undoable function'
                );
            for (x = 0; x < b.length; x++) {
              var M, C;
              ((M = b[x]).param.firstTime = b.firstTime),
                (C =
                  'undo' == P
                    ? T[M.name]._undo(M.param)
                    : T[M.name]._do(M.param)),
                m.unshift({
                  name: M.name,
                  param: C
                });
            }
            return m;
          }
          return {
            add: {
              _do: function (b) {
                return b.firstTime ? q.add(b) : U(b);
              },
              _undo: q.remove
            },
            remove: {
              _do: q.remove,
              _undo: U
            },
            restore: {
              _do: U,
              _undo: q.remove
            },
            select: {
              _do: function (b) {
                return he(b).select();
              },
              _undo: function (b) {
                return he(b).unselect();
              }
            },
            unselect: {
              _do: function (b) {
                return he(b).unselect();
              },
              _undo: function (b) {
                return he(b).select();
              }
            },
            move: {
              _do: function (b) {
                var P = he(b.eles),
                  m = P.nodes(),
                  D = P.edges(),
                  T = [],
                  x = [],
                  M = [];
                return (
                  m.forEach(function (C) {
                    T.push(C.parent().length > 1 ? C.parent().id() : null);
                  }),
                  D.forEach(function (C) {
                    x.push(C.source().id()), M.push(C.target().id());
                  }),
                  {
                    oldNodesParents: T,
                    newNodes: m.move(b.location),
                    oldEdgesSources: x,
                    oldEdgesTargets: M,
                    newEdges: D.move(b.location)
                  }
                );
              },
              _undo: function (b) {
                var P = q.collection(),
                  m = {};
                if (b.newNodes.length > 0) {
                  m.parent = b.newNodes[0].parent().id();
                  for (var D = 0; D < b.newNodes.length; D++) {
                    var T = b.newNodes[D].move({
                      parent: b.oldNodesParents[D]
                    });
                    P = P.union(T);
                  }
                } else
                  for (
                    m.source = b.newEdges[0].source().id(),
                      m.target = b.newEdges[0].target().id(),
                      D = 0;
                    D < b.newEdges.length;
                    D++
                  ) {
                    var x = b.newEdges[D].move({
                      source: b.oldEdgesSources[D],
                      target: b.oldEdgesTargets[D]
                    });
                    P = P.union(x);
                  }
                return {
                  eles: P,
                  location: m
                };
              }
            },
            drag: {
              _do: function (b) {
                return (
                  b.move &&
                    (ae(b.positionDiff, b.nodes), q.elements().unselect()),
                  b
                );
              },
              _undo: function (b) {
                var m = {
                  positionDiff: b.positionDiff,
                  nodes: b.nodes,
                  move: !0
                };
                return (
                  ae(
                    {
                      x: -1 * b.positionDiff.x,
                      y: -1 * b.positionDiff.y
                    },
                    b.nodes
                  ),
                  q.elements().unselect(),
                  m
                );
              }
            },
            layout: {
              _do: function (b) {
                if (b.firstTime) {
                  var m,
                    P = (function G() {
                      for (
                        var b = {}, P = q.nodes(), m = 0;
                        m < P.length;
                        m++
                      ) {
                        var D = P[m];
                        b[D.id()] = {
                          x: D.position('x'),
                          y: D.position('y')
                        };
                      }
                      return b;
                    })();
                  return (
                    (m = b.eles
                      ? he(b.eles).layout(b.options)
                      : q.layout(b.options)) &&
                      m.run &&
                      m.run(),
                    P
                  );
                }
                return oe(b);
              },
              _undo: function (b) {
                return oe(b);
              }
            },
            changeParent: {
              _do: function (b) {
                return q.nodes()[0].component ? re(b) : ve(b);
              },
              _undo: function (b) {
                return q.nodes()[0].component ? re(b) : ve(b);
              }
            },
            batch: {
              _do: function (b) {
                return O(b, 'do');
              },
              _undo: function (b) {
                return O(b, 'undo');
              }
            }
          };
        })(ae);
        for (let U in oe) he.actions[U] = oe[U];
        (function W(q, Q) {
          var ae = null;
          q.on('grab', 'node', function () {
            ('function' == typeof Q ? Q.call(this) : Q) &&
              (((ae = {}).lastMouseDownPosition = {
                x: this.position('x'),
                y: this.position('y')
              }),
              (ae.node = this));
          }),
            q.on('free', 'node', function () {
              var he = K(q, 'instance');
              if ('function' == typeof Q ? Q.call(this) : Q) {
                if (null == ae) return;
                var U = ae.node,
                  oe = ae.lastMouseDownPosition,
                  G = {
                    x: U.position('x'),
                    y: U.position('y')
                  };
                if (G.x != oe.x || G.y != oe.y) {
                  var re,
                    ve = {
                      x: G.x - oe.x,
                      y: G.y - oe.y
                    };
                  (re = U.selected()
                    ? q.nodes(':visible').filter(':selected')
                    : q.collection([U])),
                    he.do('drag', {
                      positionDiff: ve,
                      nodes: re,
                      move: !1
                    }),
                    (ae = null);
                }
              }
            });
        })(ae, he.options.undoableDrag),
          V(ae, 'isInitialized', !0);
      }
      return he.options.ready(), he;
    });
};
export default register;
