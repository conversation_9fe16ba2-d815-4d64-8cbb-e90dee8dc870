<template>
  <el-select
    ref="select"
    style="width: 100%"
    :placeholder="placeholder"
    clearable
    v-model="selectedLabel"
    :disabled="disabled"
    popper-class="tree-select-popper"
    @change="handleChange"
    filterable
  >
    <!-- 这个选项包含树形结构 -->
    <el-option value="" style="height: auto; padding: 0; border: 0">
      <div style="max-height: 275px; overflow-y: auto; width: 100%">
        <div style="padding: 6px 10px" @click.stop>
          <el-input
            type="text"
            v-model="filterText"
            placeholder="输入关键字进行过滤"
            autocomplete="off"
            clearable
            @click.stop
          />
        </div>
        <el-tree
          ref="tree"
          node-key="id"
          highlight-current
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="nodeClick"
          default-expand-all
        ></el-tree>
      </div>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'YkMenuSelectTree',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    data: {
      type: Array,
      default: () => [],
    },
    props: {
      type: Object,
      default: () => ({
        label: 'title',
        children: 'children',
      }),
    },
  },
  data() {
    return {
      defaultProps: this.props,
      selectedValue: '',
      selectedLabel: '',
      filterText: '',
      treeData: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.selectedValue = val;
        this.$nextTick(() => {
          if (val && this.$refs.tree) {
            this.$refs.tree.setCurrentKey(val);
            // 查找并设置选中节点的标签
            this.findNodeLabel(val);
          }
        });
      },
    },
    data: {
      immediate: true,
      handler(val) {
        this.treeData = val;
        // 当数据变化时，重新查找选中节点的标签
        if (this.selectedValue) {
          this.$nextTick(() => {
            this.findNodeLabel(this.selectedValue);
          });
        }
      },
    },
    filterText(val) {
      this.$refs.tree && this.$refs.tree.filter(val);
    },
  },
  methods: {
    // 查找节点标签
    findNodeLabel(id) {
      const findLabel = (data, id) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id === id) {
            this.selectedLabel = data[i][this.defaultProps.label];
            return true;
          }
          if (data[i].children && data[i].children.length > 0) {
            if (findLabel(data[i].children, id)) {
              return true;
            }
          }
        }
        return false;
      };

      findLabel(this.treeData, id);
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1;
    },
    // 节点点击
    nodeClick(data) {
      this.selectedValue = data.id;
      this.selectedLabel = data[this.defaultProps.label];

      this.$emit('update:value', data.id);
      this.$emit('change', data);
      this.$refs.select.blur();
    },
    // 选择变化
    handleChange(val) {
      if (!val) {
        this.selectedLabel = '';
        this.$emit('update:value', '');
        console.log('asdf');
        this.$emit('change', null);
      }
    },
  },
};
</script>

<style>
.tree-select-popper {
  width: auto !important;
  min-width: 200px;
}
</style>
