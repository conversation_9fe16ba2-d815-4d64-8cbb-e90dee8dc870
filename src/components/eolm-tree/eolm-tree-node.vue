<template>
  <el-row id="root-tree" ref="root-tree">
    <el-popover
      trigger="click"
      placement="bottom"
      :width="popoverWidth"
      :visible="popoverVisible"
      popper-class="filter-popover"
    >
      <el-tree
        ref="filterTree"
        class="filter-tree"
        v-loading="treeFilterLoading"
        :data="filterData"
        default-expand-all
        node-key="id"
        :props="filterTreeDefaultProps"
        :expand-on-click-node="false"
        @node-click="filterNodeClick"
        @mouseenter="popOver"
        @mouseleave="popOut"
      >
        <template #default="{ data }">
          <span class="custom-tree-node">
            <el-row type="flex" align="middle">
              <span class="_text">
                <i
                  :class="
                    'el-icon-ud-' +
                    returnString(data.nodeCategory) +
                    ' el-alarmLevel' +
                    data.alarmLevel
                  "
                ></i>
                <span>{{ data.nodeName }} </span></span
              >
            </el-row>
          </span>
        </template>
      </el-tree>
      <template #reference>
        <el-input
          v-if="isSearch"
          ref="search"
          placeholder="输入名称进行过滤"
          v-model="filterText"
          maxlength="50"
          @focus="
            e => {
              this.searchInputFocus(e);
            }
          "
          @blur="
            () => {
              this.inputFocus = false;
            }
          "
        >
        </el-input>
      </template>
    </el-popover>
    <el-scrollbar style="flex: 1">
      <el-tree
        id="tree"
        ref="tree"
        :current-node-key="currentNodeKey"
        v-loading="treeLoading"
        class="equipment-category-tree"
        :data="treeData"
        :show-checkbox="showCheckbox"
        node-key="id"
        :expand-on-click-node="false"
        :props="treeDefaultProps"
        @node-click="nodeClick"
        :load="loadNode"
        :lazy="true"
        :default-expanded-keys="expandKeys"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <el-row type="flex" align="middle">
              <span class="_text">
                <i
                  :class="
                    'el-icon-ud-' + returnString(data.category) + ' el-alarmLevel' + data.alarmLevel
                  "
                ></i>
                <span>
                  <span v-if="data.category === 3 && data.unbind === 1">
                    <span class="unLink">{{ node.label }}</span
                    >&nbsp;
                    <el-icon class="unlinkIcon"> <Unlock /> </el-icon>
                  </span>
                  <span v-else>{{ node.label }}</span>
                </span></span
              >
            </el-row>
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
  </el-row>
</template>

<script>
import { getTree, queryTree } from '@/api/common';
import { mapGetters } from 'vuex';
import { returnString } from '@/utils/util';
import { waveConfigTreeList } from '@/api/device-info-api';
export default {
  name: 'index',
  props: {
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    hiddenButtons: {
      type: Boolean,
      default: false,
    },
    // 是否展示搜索框
    isSearch: {
      type: Boolean,
      default: true,
    },
    //是否展示测点
    isPoint_show: {
      type: Boolean,
      default: false,
    },
    // 点击图表的测点，
    currentPointObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tenantId: {
      type: String,
      default: ''
    }
  },
  watch: {
    filterText(val) {
      if (val !== '') {
        // this.expandKeys.push('1579278591113003010');
        // 查询
        this.queryAllTree(val);
      } else {
        this.filterData = [];
      }
    },
    popVisible(val) {
      this.popoverVisible = val;
    },
    // 当载设备基本信息图片点击测点的时候，定位相应的选中节点
    currentPointObj: {
      immediate: true,
      deep: true,
      handler(val) {
        // 先设置测点 ，然后 获取选中当前节点，如果对应，展开相关设备测点，不匹配，掉接口，展开，然后再设置
        this.$nextTick(() => {
          if (val.pointId) {
            this.$refs.tree && this.$refs.tree.setCurrentKey(val.pointId);
            let getNodePoint = this.$refs.tree.getCurrentKey();
            if (getNodePoint === val.pointId) {
              // 展开过，直接展开
              this.expandKeys.push(val.deviceId);
            } else {
              this.$refs.tree.getCurrentNode();
              this.expandKeys.push(val.deviceId);
              this.willSelectId = val.pointId;
            }
          }
        });
      },
    },
    // 监听路由上的pointsPath 参数是不是空的，如果是空的，不做处理，不过不是，一次展开，并定位
    '$route.params.path': {
      immediate: true,
      deep: true,
      handler(val) {
        if (val !== 'no' && val) {
          this.$nextTick(() => {
            this.pathId = val;
            this.setDefaultSelect();
          });
        }
      },
    },
    '$route.query.path': {
      immediate: true,
      deep: true,
      handler(val) {
        if (val !== 'no' && val) {
          this.$nextTick(() => {
            this.pathId = val;
            this.setDefaultSelect();
          });
        }
      },
    },
    '$route.name': {
      immediate: true,
      deep: true,
      handler(val) {
        if (val === 'expertDiagnosis' && this.treeData.length !== 0) {
          this.$nextTick(() => {
            this.setDefaultSelect();
          });
        }
      },
    },
  },
  data() {
    return {
      returnString,
      filterText: '',
      filterTree: [],
      treeFilterLoading: false,
      treeLoading: false,
      treeData: [],
      treeDefaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf',
      },
      filterTreeDefaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf',
      },
      expandKeys: [],
      currentNodeKey: '',
      filterData: [],
      popoverVisible: false,
      willSelectId: '',
      oldCurrentKey: undefined,
      popoverWidth: 0,
      pathId: undefined,
      mouseOnPop: false, // 标识当前鼠标是否在pop搜索框上
      inputFocus: false, // 标识当前搜索框是否在focus状态
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'topOrgId']),
    // 用于决定 搜索结果弹窗是否显示
    popVisible() {
      return (this.mouseOnPop || this.inputFocus) && this.filterText.trim() !== '';
    },
  },
  created() {
    window.addEventListener('resize', this.getTreeWidth);
  },
  mounted() {
    this.popoverWidth = document.getElementById('root-tree').clientWidth;
    this.init();
  },
  unmounted() {
    window.removeEventListener('resize', this.getTreeWidth);
  },
  methods: {
    getTreeWidth() {
      let tree = document.getElementById('root-tree');
      this.popoverWidth = tree && tree.clientWidth;
    },
    searchInputFocus() {
      this.inputFocus = true;
    },
    popOver() {
      this.mouseOnPop = true;
    },
    popOut() {
      this.mouseOnPop = false;
    },
    // 遍历树结构
    traverseTree(tree, callback) {
      tree.forEach(node => {
        // 执行回调函数
        callback(node);

        // 如果有子节点，递归遍历子节点
        if (node.children) {
          this.traverseTree(node.children, callback);
        }
      });
    },
    // 查询所有树结构
    async queryAllTree(val) {
      try {
        this.treeFilterLoading = true;
        const res = await queryTree({
          nodeName: val,
          tenantId: this.tenantId
        });
        let data = res.data.data || [];
        this.traverseTree(data, (node) => {
          node.category= node.nodeCategory;
          node.categoryName= node.nodeCategoryName;
          node.code= node.nodeCode;
          node.level= node.nodeLevel;
          node.name= node.nodeName;
        });
        this.filterData = data;
        this.treeFilterLoading = false;
      } catch (e) {
        this.$message.warning(e.data.msg);
        this.treeFilterLoading = false;
      }
    },
    async loadNode(node, resolve) {
      let category = 0;
      if (node.level === 0) {
        return resolve(this.data || []);
      } else if (node.data.nodeCategory === 3) {
        // 测点 查询
      } else if (node.data.nodeCategory === 2) {
        const res = await waveConfigTreeList({
          monitorId: node.data.id,
        });
        const data = res.data.data.map(it => {
          return {
            ...it,
            category: it.nodeCategory,
            categoryName: it.nodeCategoryName,
            code: it.nodeCode,
            level: it.nodeLevel,
            name: it.nodeName,
            isLeaf: true,
          };
        });
        this.$emit('waveConfigTreeList', data);
        this.setCurrentKey(node, data);
        return resolve(data);
      } else {
        // if (node.data.hasDevice) {
        //   category = 1;
        // } else if (node.data.hasMonitor) {
        //   category = 2;
        // }
        const res = await getTree({
          clientType: 'PORTAL',
          parentId: node.data.id,
          // nodeCategory: node.data.nodeCategory,
          withWave: category === 2 ? 1 : undefined, // 树结构展示到波形的时候 添加参数withWave
          tenantId: this.tenantId
        });
        const data = res.data.data.map(it => {
          let isLeaf = this.isPoint_show
            ? (it.hasChildren && it.nodeCategory === 2 || !it.hasChildren)
            : (it.nodeCategory === 0 && !it.hasChildren && !it.hasDevice) || it.nodeCategory === 1;
          return {
            ...it,
            category: it.nodeCategory,
            categoryName: it.nodeCategoryName,
            code: it.nodeCode,
            level: it.nodeLevel,
            name: it.nodeName,
            isLeaf: isLeaf,
          };
        });
        if (data.length > 0) {
          if (data[0].nodeCategory === 0) {
            node.data.hasChildren = true;
          } else if (data[0].nodeCategory === 1) {
            node.data.hasDevice = true;
          }
        } else {
          node.data.hasChildren = false;
          node.data.hasDevice = false;
        }
        this.setCurrentKey(node, data);
        return resolve(data);
      }
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    init() {
      this.getTree('0', 0);
    },
    async getTree(parentId, category) {
      try {
        this.treeLoading = true;
        const res = await getTree({ parentId: parentId, nodeCategory: category, tenantId: this.tenantId });
        this.treeData = res.data.data.map(it => {
          let isLeaf = this.isPoint_show
            ? !(it.hasChildren || it.hasDevice || it.hasMonitor)
            : (it.nodeCategory === 0 && !it.hasChildren && !it.hasDevice) || it.nodeCategory === 1;
          // 只展示到设备那一层
          return {
            ...it,
            category: it.nodeCategory,
            categoryName: it.nodeCategoryName,
            code: it.nodeCode,
            level: it.nodeLevel,
            name: it.nodeName,
            isLeaf: isLeaf,
          };
        });
        this.setDefaultSelect();
        this.treeLoading = false;
      } catch (e) {
        this.treeLoading = false;
        this.treeData = [];
      }
    },
    async reload() {
      // 记录重新加载前的选中值
      this.oldCurrentKey = this.$refs.tree.getCurrentKey();
      try {
        this.treeLoading = true;
        const res = await getTree({ parentId: '0', nodeCategory: 0, tenantId: this.tenantId });
        this.treeData = res.data.data.map(it => {
          let isLeaf = this.isPoint_show
            ? !(it.hasChildren || it.hasDevice || it.hasMonitor)
            : (it.nodeCategory === 0 && !it.hasChildren && !it.hasDevice) || it.nodeCategory === 1;
          return {
            ...it,
            category: it.nodeCategory,
            categoryName: it.nodeCategoryName,
            code: it.nodeCode,
            level: it.nodeLevel,
            name: it.nodeName,
            isLeaf: isLeaf,
          };
        });

        const findI = this.treeData.findIndex(it => {
          return it.id === (this.willSelectId || this.oldCurrentKey);
        });

        // 重新加载后 设置选中值
        if (findI !== -1) {
          this.$nextTick(() => {
            this.$refs.tree &&
              this.$refs.tree.setCurrentKey(this.willSelectId || this.oldCurrentKey);
            const node = this.$refs.tree.getNode(this.willSelectId || this.oldCurrentKey);

            this.$emit('select-category-change', node);
            this.willSelectId = '';
            this.oldCurrentKey = undefined;
          });
        } else {
          this.$emit('select-category-change', null);
        }

        this.treeLoading = false;
      } catch (e) {
        this.treeLoading = false;
        this.treeData = [];
      }
    },
    // 点击全部展示全部设备
    resetAll() {
      this.$emit('category-reset', null);
    },
    nodeClick(data, node) {
      this.$emit('category-change', node);
    },
    filterNodeClick(data, node) {
      this.expandKeys = [];
      let paths = data.path.split(',');
      const isExpand = this.$refs.tree.getNode(data);
      let id = paths[paths.length - 1];
      if (!!isExpand) {
        this.$refs.tree && this.$refs.tree.setCurrentKey(id);
      } else {
        let keys = [...paths].slice(0, paths.length - 1);
        this.expandKeys = [...keys];
      }

      this.popoverVisible = false;
      this.willSelectId = paths[paths.length - 1];
      this.$nextTick(() => {
        this.filterText = data.name || '';
        this.$emit('category-change', node);
        this.$refs['search'].blur();
      });
    },
    clear() {
      this.$refs.tree && this.$refs.tree.setCurrentKey(null);
      this.$emit('category-change', null);
    },
    clearFilter() {
      this.filterText = '';
    },
    // 设置第一个数据默认选中
    setCurrentKey(node, data) {
      const findI = data.findIndex(it => {
        return it.id === this.willSelectId;
      });

      if (node.data.id === this.willSelectId || findI !== -1) {
        this.$nextTick(() => {
          this.$refs.tree && this.$refs.tree.setCurrentKey(this.willSelectId);
          const node = this.$refs.tree.getNode(this.willSelectId);
          this.$emit('select-category-change', node);
          this.willSelectId = '';
        });
      }
    },
    setDefaultSelect() {
      if (this.treeData.length === 0) {
        return;
      }
      if (this.pathId) {
        let pathArr = this.pathId.split(',');
        let path = [...pathArr];
        pathArr.splice(-1, 1);
        let arr = [];
        pathArr.map(it => {
          arr.push(it);
        });
        this.expandKeys = arr;
        this.willSelectId = path[path.length - 1];
        this.$refs.tree && this.$refs.tree.setCurrentKey(this.willSelectId);

        if (this.willSelectId === 'undefined') {
          this.$message.warning('暂无波形');
        }
      } else {
        // 请求第一个节点的子节点
        this.expandKeys = [this.treeData[0].id];
        this.$nextTick(() => {
          const first = this.$refs.tree.getNode(this.treeData[0].id);
          this.willSelectId = this.treeData[0].id;
          // this.$emit('category-change', first);
          this.$emit('select-category-change', first);
        });
      }
    },
    // 重新懒加载某个节点
    refreshTree(node) {
      if (node === null) {
        this.reload();
        return;
      }

      //  设置未进行懒加载状态
      node.loaded = false;
      // 重新展开节点就会间接重新触发load达到刷新效果
      node.expand();
      this.willSelectId = node.data.id;
    },
    // 刷新某个节点 并选中其某个子节点
    refreshParent(parent, chilId) {
      this.willSelectId = chilId;
      if (parent === null) {
        this.reload();
      } else {
        // 设置未进行懒加载状态
        parent.loaded = false;
        // 重新展开节点就会间接重新触发load达到刷新效果
        parent.expand();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
#root-tree {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  ::v-deep {
    .el-tree-node > .el-tree-node__children {
      overflow: unset;
    }
    .el-tree-node__content:hover {
      background: var(--el-tree-node-hover-bg-color);
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background: var(--el-tree-node-hover-bg-color);
    }

    .el-tree-node:focus > .el-tree-node__content {
      background: var(--el-tree-node-hover-bg-color);
    }
  }
}

:deep(.el-scrollbar__bar) {
  display: none;
}

::v-deep {
  .el-input__inner {
  }
}

.filter-tree {
  max-height: 300px;
  padding-bottom: 20px;
  overflow: auto;

  ::v-deep {
    .el-tree-node > .el-tree-node__children {
      overflow: unset;
    }
  }
}

._text {
  i {
    margin-right: 5px;
  }

  span {
    font-size: 14px;
  }
}
.unbindColor {
  color: $four-level-alarm-color !important;
}
.normalColor {
  color: $font-color !important;
}
</style>
<style lang="scss">
.filter-popover {
  padding: 0;
  padding-right: 5px;
}
.unLink {
  color: rgb(204, 204, 204);
}
.unlinkIcon {
  color: rgb(204, 204, 204);
}
</style>
