<template>
  <div class="basic-container" :style="styleName" :class="{ 'basic-container--block': block }">
    <el-card
      class="basic-container__card"
      :style="{ height: cardHeight, overflow: autoHeight ? 'auto' : '' }"
      :body-style="{ height: '100%' }"
    >
      <el-scrollbar v-if="autoHeight">
        <slot></slot>
      </el-scrollbar>
      <template v-else>
        <slot></slot>
      </template>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'basicContainer',
  props: {
    radius: {
      type: [String, Number],
      default: 10,
    },
    background: {
      type: String,
    },
    block: {
      type: Boolean,
      default: false,
    },
    autoHeight: {
      type: Boolean,
      default: false,
    },
    zoomHeight: {
      type: Number,
      default: 0,
    },
    fullHeight: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    styleName() {
      return {
        borderRadius: this.setPx(this.radius),
        background: this.background,
        paddingBottom: 0,
      };
    },
    cardHeight() {
      let height = document.documentElement.clientHeight;
      let calcHeight = this.fullHeight ? height : height - (50 + 40 + 10 + 10 + this.zoomHeight); // 顶部菜单、tab、margin、底部空隙
      return this.autoHeight ? `${calcHeight}px` : 'auto';
    },
  },
};
</script>

<style lang="scss">
.basic-container {
  .el-scrollbar__bar {
    //display: none;
    right: 0;
  }
  box-sizing: border-box;
  padding: 10px 6px;

  &--block {
    height: 100%;

    .basic-container__card {
      height: 100%;
    }
  }

  &__card {
    width: 100%;
  }

  &:first-child {
    padding-top: 0;
  }
}
</style>
