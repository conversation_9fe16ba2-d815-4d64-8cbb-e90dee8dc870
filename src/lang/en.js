export default {
  title: 'Avue is a framework',
  tip: 'tip',
  logoutTip: 'Exit the system, do you want to continue?',
  submitText: 'submit',
  cancelText: 'cancel',
  search: 'Please input search content',
  menuTip: 'none menu list',
  wel: {
    info: 'Good morning, Smallwei, Avuex is a framework',
    dept: 'a certain technology department',
    team: 'Team ranking',
    project: 'Project access',
    count: 'Item number',
    data: {
      subtitle: 'real time',
      column1: 'Classified statistics',
      column2: 'Annex statistics',
      column3: 'Article statistics',
      key1: 'C',
      key2: 'A',
      key3: 'A',
      text1: 'Total Record Number of Classifications',
      text2: 'Number of attachments Uploaded',
      text3: 'Comment frequency'
    },
    data2: {
      column1: 'Registration today',
      column2: 'Login today',
      column3: 'Subscription today',
      column4: 'Todays review'
    },
    data3: {
      column1: 'Conversion rate（Day 28%）',
      column2: 'Attendance rate（Day 11%）',
      column3: 'Attendance rate（Day 33%）'
    },
    data4: {
      column1: 'Error log',
      column2: 'Data display',
      column3: 'Privilege management',
      column4: 'user management'
    },
    table: {
      rw: 'Work Tasks',
      nr: 'Work content',
      sj: 'Working hours'
    }
  },
  route: {
    info: 'info',
    website: 'website',
    avuexwebsite: 'avuex',
    dashboard: 'dashboard',
    more: 'more',
    tags: 'tags',
    store: 'store',
    permission: 'permission',
    api: 'api',
    logs: 'logs',
    table: 'table',
    form: 'form',
    top: 'backtop',
    data: 'data',
    error: 'error',
    test: 'test'
  },
  login: {
    title: 'Login ',
    info: 'Enterprise Development Platform',
    tenantId: 'Please input tenantId',
    username: 'Please input username',
    password: 'Please input a password',
    wechat: 'Wechat',
    qq: 'QQ',
    github: 'github',
    gitee: 'gitee',
    phone: 'Please input a phone',
    code: 'Please input a code',
    submit: 'Login',
    userLogin: 'userLogin',
    phoneLogin: 'phoneLogin',
    thirdLogin: 'thirdLogin',
    msgText: 'send code',
    msgSuccess: 'reissued code'
  },
  navbar: {
    info: 'info',
    logOut: 'logout',
    userinfo: 'userinfo',
    switchDept: 'switch dept',
    dashboard: 'dashboard',
    lock: 'lock',
    bug: 'none bug',
    bugs: 'bug',
    screenfullF: 'exit screenfull',
    screenfull: 'screenfull',
    language: 'language',
    notice: 'notice',
    theme: 'theme',
    color: 'color'
  },
  tagsView: {
    search: 'Search',
    menu: 'menu',
    clearCache: 'Clear Cache',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  }
};
