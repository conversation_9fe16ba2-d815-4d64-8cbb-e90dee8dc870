/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.wrapper;

import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.system.dto.GeneralDto;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IGeneralService;
import com.snszyk.system.vo.DeptVO;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.vo.OrgCodeVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class DeptWrapper extends BaseEntityWrapper<Dept, DeptVO> {

	public static DeptWrapper build() {
		return new DeptWrapper();
	}

	@Override
	public DeptVO entityVO(Dept dept) {
		DeptVO deptVO = Objects.requireNonNull(BeanUtil.copy(dept, DeptVO.class));
		if (Func.equals(dept.getParentId(), SzykConstant.TOP_PARENT_ID)) {
			deptVO.setParentName(SzykConstant.TOP_PARENT_NAME);
		} else {
			Dept parent = SysCache.getDept(dept.getParentId());
			deptVO.setParentName(parent.getDeptName());
		}
		String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());

		// 机构类型
		String orgCode = dept.getOrgCode();
		if(StringUtil.isNoneBlank(orgCode)){
			String[] split = orgCode.split(",");
			for (String s : split) {
				String value = DictBizCache.getValue(DictBizEnum.OQ_DM.getName(), s);
				if(StringUtil.isNoneBlank(value)){
					deptVO.getOrgCodeList().add(OrgCodeVO.builder().orgCode(s).orgName(value).build());
				}
			}
		}
		deptVO.setDeptCategoryName(category);
		//停用启用
		if (Func.equals(dept.getDeptStatus(), CommonConstant.ONE)) {
			deptVO.setDeptStatusName("启用");
		} else {
			deptVO.setDeptStatusName("停用");
		}
		//全宗
		Long generalId = dept.getGeneralId();
		deptVO.setGeneralCode(null);
		if(generalId!=null){
			GeneralDto generalDto = SpringUtil.getBean(IGeneralService.class).fetchById(generalId);
			if(generalDto!=null){
				deptVO.setGeneralName(generalDto.getGeneralName());
				deptVO.setGeneralCode(generalDto.getGeneralCode());

			}
		}
		return deptVO;
	}

	public List<DeptVO> listNodeVO(List<Dept> list) {
		List<DeptVO> collect = list.stream().map(dept -> {
			DeptVO deptVO = BeanUtil.copy(dept, DeptVO.class);
			String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());
			Objects.requireNonNull(deptVO).setDeptCategoryName(category);
			return deptVO;
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

	public List<DeptVO> listNodeLazyVO(List<DeptVO> list) {
		List<DeptVO> collect = list.stream().peek(dept -> {
			String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());
			Objects.requireNonNull(dept).setDeptCategoryName(category);
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
