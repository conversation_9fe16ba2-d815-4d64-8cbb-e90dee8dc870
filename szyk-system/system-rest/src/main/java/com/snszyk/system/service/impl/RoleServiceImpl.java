/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.entity.*;
import com.snszyk.system.mapper.UserMapper;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.RoleVO;
import com.snszyk.system.wrapper.RoleWrapper;
import lombok.AllArgsConstructor;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.mapper.RoleMapper;
import com.snszyk.system.service.IRoleMenuService;
import com.snszyk.system.service.IRoleScopeService;
import com.snszyk.system.service.IRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@AllArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

	private final IRoleMenuService roleMenuService;
	private final IRoleScopeService roleScopeService;
	private final UserMapper userMapper;

	@Override
	public IPage<RoleVO> selectRolePage(IPage<RoleVO> page, RoleVO role) {
		return page.setRecords(baseMapper.selectRolePage(page, role));
	}

	@Override
	public List<RoleVO> tree(String tenantId) {
		String userRole = AuthUtil.getUserRole();
		String excludeRole = null;
		if (!CollectionUtil.contains(Func.toStrArray(userRole), RoleConstant.ADMIN) && !CollectionUtil.contains(Func.toStrArray(userRole), RoleConstant.ADMINISTRATOR)) {
			excludeRole = RoleConstant.ADMIN;
		}
		return ForestNodeMerger.merge(baseMapper.tree(tenantId, excludeRole));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean grant(@NotEmpty List<Long> roleIds, List<Long> menuIds, List<Long> dataScopeIds, List<Long> apiScopeIds) {
		return grantRoleMenu(roleIds, menuIds) && grantDataScope(roleIds, dataScopeIds) && grantApiScope(roleIds, apiScopeIds);
	}

	private boolean grantRoleMenu(List<Long> roleIds, List<Long> menuIds) {
		// 防止越权配置超管角色
		int administratorCount = baseMapper.selectCount(Wrappers.<Role>query().lambda().eq(Role::getRoleAlias, RoleConstant.ADMINISTRATOR).in(Role::getId, roleIds));
		if (!AuthUtil.isAdministrator() && administratorCount > 0) {
			throw new ServiceException("无权配置超管角色!");
		}
		// 防止越权配置管理员角色
		int adminCount = baseMapper.selectCount(Wrappers.<Role>query().lambda().eq(Role::getRoleAlias, RoleConstant.ADMIN).in(Role::getId, roleIds));
		if (!AuthUtil.isAdmin() && adminCount > 0) {
			throw new ServiceException("无权配置管理员角色!");
		}
		// 删除角色配置的菜单集合
		roleMenuService.remove(Wrappers.<RoleMenu>update().lambda().in(RoleMenu::getRoleId, roleIds));
		// 组装配置
		List<RoleMenu> roleMenus = new ArrayList<>();
		roleIds.forEach(roleId -> menuIds.forEach(menuId -> {
			RoleMenu roleMenu = new RoleMenu();
			roleMenu.setRoleId(roleId);
			roleMenu.setMenuId(menuId);
			roleMenus.add(roleMenu);
		}));
		// 新增配置
		roleMenuService.saveBatch(roleMenus);
		// 递归设置下属角色菜单集合
		recursionRoleMenu(roleIds, menuIds);
		return true;
	}

	private void recursionRoleMenu(List<Long> roleIds, List<Long> menuIds) {
		roleIds.forEach(roleId -> baseMapper.selectList(Wrappers.<Role>query().lambda().eq(Role::getParentId, roleId)).forEach(role -> {
			List<RoleMenu> roleMenuList = roleMenuService.list(Wrappers.<RoleMenu>query().lambda().eq(RoleMenu::getRoleId, role.getId()));
			// 子节点过滤出父节点删除的菜单集合
			List<Long> collectRoleMenuIds = roleMenuList.stream().map(RoleMenu::getMenuId).filter(menuId -> !menuIds.contains(menuId)).collect(Collectors.toList());
			if (collectRoleMenuIds.size() > 0) {
				// 删除子节点权限外的菜单集合
				roleMenuService.remove(Wrappers.<RoleMenu>update().lambda().eq(RoleMenu::getRoleId, role.getId()).in(RoleMenu::getMenuId, collectRoleMenuIds));
				// 递归设置下属角色菜单集合
				recursionRoleMenu(Collections.singletonList(role.getId()), menuIds);
			}
		}));
	}

	private boolean grantDataScope(List<Long> roleIds, List<Long> dataScopeIds) {
		// 删除角色配置的数据权限集合
		roleScopeService.remove(Wrappers.<RoleScope>update().lambda().eq(RoleScope::getScopeCategory, CommonConstant.DATA_SCOPE_CATEGORY).in(RoleScope::getRoleId, roleIds));
		// 组装配置
		List<RoleScope> roleDataScopes = new ArrayList<>();
		roleIds.forEach(roleId -> dataScopeIds.forEach(scopeId -> {
			RoleScope roleScope = new RoleScope();
			roleScope.setScopeCategory(CommonConstant.DATA_SCOPE_CATEGORY);
			roleScope.setRoleId(roleId);
			roleScope.setScopeId(scopeId);
			roleDataScopes.add(roleScope);
		}));
		// 新增配置
		roleScopeService.saveBatch(roleDataScopes);
		return true;
	}

	private boolean grantApiScope(List<Long> roleIds, List<Long> apiScopeIds) {
		// 删除角色配置的接口权限集合
		roleScopeService.remove(Wrappers.<RoleScope>update().lambda().eq(RoleScope::getScopeCategory, CommonConstant.API_SCOPE_CATEGORY).in(RoleScope::getRoleId, roleIds));
		// 组装配置
		List<RoleScope> roleApiScopes = new ArrayList<>();
		roleIds.forEach(roleId -> apiScopeIds.forEach(scopeId -> {
			RoleScope roleScope = new RoleScope();
			roleScope.setScopeCategory(CommonConstant.API_SCOPE_CATEGORY);
			roleScope.setScopeId(scopeId);
			roleScope.setRoleId(roleId);
			roleApiScopes.add(roleScope);
		}));
		// 新增配置
		roleScopeService.saveBatch(roleApiScopes);
		return true;
	}

	@Override
	public String getRoleIds(String tenantId, String roleNames) {
		List<Role> roleList = baseMapper.selectList(Wrappers.<Role>query().lambda().eq(Role::getTenantId, tenantId).in(Role::getRoleName, Func.toStrList(roleNames)));
		if (roleList != null && roleList.size() > 0) {
			return roleList.stream().map(role -> Func.toStr(role.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<String> getRoleNames(String roleIds) {
		return baseMapper.getRoleNames(Func.toLongArray(roleIds));
	}

	@Override
	public List<String> getRoleAliases(String roleIds) {
		return baseMapper.getRoleAliases(Func.toLongArray(roleIds));
	}

	@Override
	public boolean submit(Role role) {
		if (!AuthUtil.isAdministrator()) {
			if (Func.toStr(role.getRoleAlias()).equals(RoleConstant.ADMINISTRATOR)) {
				throw new ServiceException("无权限创建超管角色！");
			}
		}
		if (Func.isEmpty(role.getParentId())) {
			role.setTenantId(AuthUtil.getTenantId());
			role.setParentId(SzykConstant.TOP_PARENT_ID);
		}
		if (role.getParentId() > 0) {
			Role parent = getById(role.getParentId());
			if (Func.toLong(role.getParentId()) == Func.toLong(role.getId())) {
				throw new ServiceException("父节点不可选择自身!");
			}
			role.setTenantId(parent.getTenantId());
		}
		role.setIsDeleted(SzykConstant.DB_NOT_DELETED);
		return saveOrUpdate(role);
	}

	@Override
	public List<RoleVO> search(String roleName, Long parentId) {
		String tenantId = AuthUtil.getTenantId();
		LambdaQueryWrapper<Role> queryWrapper = Wrappers.<Role>query().lambda();
		if (Func.isNotEmpty(roleName)) {
			queryWrapper.like(Role::getRoleName, roleName);
		}
		if (Func.isNotEmpty(parentId) && parentId > 0L) {
			queryWrapper.eq(Role::getParentId, parentId);
		}
		if (Func.isNotEmpty(tenantId)) {
			queryWrapper.eq(Role::getTenantId, tenantId);
		}
		List<Role> roleList = baseMapper.selectList(queryWrapper);
		return RoleWrapper.build().listNodeVO(roleList);
	}

	@Override
	public boolean removeRole(String ids) {
		Integer cnt = baseMapper.selectCount(Wrappers.<Role>query().lambda().in(Role::getParentId, Func.toLongList(ids)));
		if (cnt > 0) {
			throw new ServiceException("请先删除子节点!");
		}
		return removeByIds(Func.toLongList(ids));
	}

	@Override
	public DelResultVO checkAndRemoveRole(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.stream().forEach(id -> {
			//查询角色信息
			Role role = this.getById(id);
			//如果角色不存在，直接失败返回
			if (role == null) {
				resultVO.getDetailVOList().add(new DelDetailVO(id.toString(),Boolean.FALSE, "角色不存在"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在子节点，不允许删除
			Integer childRoleCnt = baseMapper.selectCount(Wrappers.<Role>query().lambda().in(Role::getParentId, Arrays.asList(id)));
			if (childRoleCnt > 0) {
				resultVO.getDetailVOList().add(new DelDetailVO(role.getRoleName(),Boolean.FALSE, "存在子节点，不允许删除!"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在用户引用，不允许删除
			List<User> refs = userMapper.selectList(Wrappers.<User>query().lambda().like(User::getRoleId, id).eq(User::getIsDeleted, 0));
			if (CollectionUtil.isEmpty(refs)) {
				//如果无用户引用，则删除此角色
				this.removeById(id);
				resultVO.getDetailVOList().add(new DelDetailVO(role.getRoleName(),Boolean.TRUE, "删除成功"));
				//成功次数+1
				resultVO.setSuccessNumber(resultVO.getSuccessNumber() + 1);
			} else {
				//存在用户引用不允许删除，收集引用的用户名称放入失败提示信息
				Set<String> refNameSet = refs.stream().map(User::getRealName).collect(Collectors.toSet());
				resultVO.getDetailVOList().add(new DelDetailVO(role.getRoleName(),Boolean.FALSE, "角色信息被用户引用，无法删除，具体如下："
					+ StringUtil.collectionToDelimitedString(refNameSet, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}
}
