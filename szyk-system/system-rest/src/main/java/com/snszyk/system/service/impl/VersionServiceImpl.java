/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.resource.vo.AttachVO;
import com.snszyk.system.dto.VersionDTO;
import com.snszyk.system.entity.Version;
import com.snszyk.system.mapper.VersionMapper;
import com.snszyk.system.service.IVersionService;
import com.snszyk.system.vo.VersionVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 版本控制表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-13
 */
@Service
@AllArgsConstructor
public class VersionServiceImpl extends ServiceImpl<VersionMapper, Version> implements IVersionService {

	private final IAttachService attachClient;

	@Override
	public IPage<VersionDTO> page(IPage<VersionDTO> page, VersionVO version) {
		return page.setRecords(baseMapper.page(page, version));
	}

	@Override
	public VersionDTO detail(Long id) {
		Version version = this.getById(id);
		if (Func.isEmpty(version)) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		VersionDTO versionDTO = Objects.requireNonNull(BeanUtil.copy(version, VersionDTO.class));
		if (Func.isNotEmpty(versionDTO.getHotUpdate())) {
			Attach attachR = attachClient.getById(versionDTO.getHotUpdate());
			versionDTO.setAttach(Objects.requireNonNull(BeanUtil.copy(attachR, AttachVO.class)));

		}
		return versionDTO;
	}

	@Override
	public boolean add(VersionVO vo) {
		Version version = Objects.requireNonNull(BeanUtil.copy(vo, Version.class));
		version
			.setPublishTime(DateUtil.now())
//			.setLatestPublishTime(DateUtil.now())
			.setPublishUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now());
		return this.save(version);
	}

	@Override
	public boolean edit(VersionVO vo) {
		Version version = this.getById(vo.getId());
		if (Func.isEmpty(version)) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		BeanUtil.copy(vo, version);
		return baseMapper.updateById(version) >= 0;
	}

	@Override
	public VersionDTO latestVersion() {
		List<Version> list = baseMapper.selectList((Wrappers.<Version>query().lambda().orderByDesc(Version::getPublishTime)));
		if (Func.isEmpty(list)) {
			return null;
		}
		VersionDTO versionDTO = Objects.requireNonNull(BeanUtil.copy(list.get(0), VersionDTO.class));
		if (Func.isNotEmpty(versionDTO.getHotUpdate())) {
			Attach attachR = attachClient.getByIdIgnoreTenant(versionDTO.getHotUpdate());
			versionDTO.setAttach(Objects.requireNonNull(BeanUtil.copy(attachR, AttachVO.class)));
		}
		return versionDTO;
	}

	@Override
	public boolean removeByIds(List<Long> ids) {
		return baseMapper.removeByIds(ids) >= 0;
	}

}
