/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.system.entity.Role;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.RoleVO;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IRoleService extends IService<Role> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param role
	 * @return
	 */
	IPage<RoleVO> selectRolePage(IPage<RoleVO> page, RoleVO role);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @return
	 */
	List<RoleVO> tree(String tenantId);

	/**
	 * 权限配置
	 *
	 * @param roleIds      角色id集合
	 * @param menuIds      菜单id集合
	 * @param dataScopeIds 数据权限id集合
	 * @param apiScopeIds  接口权限id集合
	 * @return 是否成功
	 */
	boolean grant(@NotEmpty List<Long> roleIds, List<Long> menuIds, List<Long> dataScopeIds, List<Long> apiScopeIds);

	/**
	 * 获取角色ID
	 *
	 * @param tenantId
	 * @param roleNames
	 * @return
	 */
	String getRoleIds(String tenantId, String roleNames);

	/**
	 * 获取角色名
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> getRoleNames(String roleIds);

	/**
	 * 获取角色名
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> getRoleAliases(String roleIds);

	/**
	 * 提交
	 *
	 * @param role
	 * @return
	 */
	boolean submit(Role role);

	/**
	 * 角色信息查询
	 *
	 * @param roleName
	 * @param parentId
	 * @return
	 */
	List<RoleVO> search(String roleName, Long parentId);

	/**
	 * 删除角色
	 *
	 * @param ids
	 * @return
	 */
	boolean removeRole(String ids);

	/**
	 * 校验并删除角色
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveRole(List<Long> ids);


}
