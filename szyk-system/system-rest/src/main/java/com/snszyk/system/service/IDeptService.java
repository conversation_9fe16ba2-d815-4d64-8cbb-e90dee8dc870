/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IDeptService extends IService<Dept> {

	/**
	 * 懒加载部门列表
	 *
	 * @param tenantId
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<DeptVO> lazyList(String tenantId, Long parentId, Map<String, Object> param);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @return
	 */
	List<DeptVO> tree(String tenantId);

	/**
	 * 懒加载树形结构
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<DeptVO> lazyTree(String tenantId, Long parentId);

	/**
	 * 获取部门ID
	 *
	 * @param tenantId
	 * @param deptNames
	 * @return
	 */
	String getDeptIds(String tenantId, String deptNames);

	/**
	 * 获取部门ID
	 *
	 * @param tenantId
	 * @param deptNames
	 * @return
	 */
	String getDeptIdsByFuzzy(String tenantId, String deptNames);

	/**
	 * 获取部门名
	 *
	 * @param deptIds
	 * @return
	 */
	List<String> getDeptNames(String deptIds);

	/**
	 * 获取子部门ID
	 *
	 * @param deptId
	 * @return
	 */
	List<Dept> getDeptChild(Long deptId);

	/**
	 * 删除部门
	 *
	 * @param ids
	 * @return
	 */
	boolean removeDept(String ids);

	/**
	 * 提交
	 *
	 * @param dept
	 * @return
	 */
	boolean submit(Dept dept);

	/**
	 * 部门信息查询
	 *
	 * @param deptName
	 * @param parentId
	 * @return
	 */
	List<DeptVO> search(String deptName, Long parentId);

	/**
	 * 校验并删除机构
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveDept(List<Long> ids);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @return
	 */
	List<DeptAttilaVO> attilaTree(String tenantId);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @return
	 */
	List<DeptUserAttilaVO> attilaTreeUser(String tenantId);



	/**
	 * 查询组织关联一级企业列表
	 *
	 * @param orgId
	 * @return 关联一级企业列表
	 */
	List<DeptLinkVO> selectLinkList(Long orgId);

	DeptVO getDeptByUserId(Long userId);

	/**
	 * 获取所有部门列表
	 * @param tenantId 租户id
	 * @return
	 */
	List<DeptVO> allDeptList(String tenantId);

	List<DeptVO> lazyTreeByParent(String tenantId, Long parentId);

	List<String> selectAncestorIdList(String tenantId, String deptName);

	List<String> selectAncestorIdListByIds(String tenantId, List<Long> ids);
}
