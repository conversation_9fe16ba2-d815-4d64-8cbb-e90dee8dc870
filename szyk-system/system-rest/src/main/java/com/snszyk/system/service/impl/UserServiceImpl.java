/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.auth.enums.UserEnum;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.constant.TenantConstant;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.SzykTenantProperties;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.*;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.cache.ParamCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.dto.UserDeptDTO;
import com.snszyk.system.entity.*;
import com.snszyk.system.excel.UserExcel;
import com.snszyk.system.mapper.UserMapper;
import com.snszyk.system.service.*;
import com.snszyk.system.vo.UserVO;
import com.snszyk.system.wrapper.UserWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.snszyk.core.tool.utils.DigestUtil.sha1Hex;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {
	private static final String GUEST_NAME = "guest";

	private final IUserRoleService userRoleService;
	private final IUserDeptService userDeptService;
	private final IUserOauthService userOauthService;
	private final IRoleService roleService;
	private final SzykTenantProperties tenantProperties;
	private final IDeptService deptService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(User user) {
		if (StringUtil.isBlank(user.getTenantId())) {
			user.setTenantId(SzykConstant.ADMIN_TENANT_ID);
		}
		String tenantId = user.getTenantId();
		Tenant tenant = SysCache.getTenant(tenantId);
		if (Func.isNotEmpty(tenant)) {
			Integer accountNumber = tenant.getAccountNumber();
			if (tenantProperties.getLicense()) {
				String licenseKey = tenant.getLicenseKey();
				String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
				accountNumber = JsonUtil.parse(decrypt, Tenant.class).getAccountNumber();
			}
			Integer tenantCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId));
			if (accountNumber != null && accountNumber > 0 && accountNumber <= tenantCount) {
				throw new ServiceException("当前租户已到最大账号额度!");
			}
		}
		if (Func.isNotEmpty(user.getPassword())) {
			user.setPassword(sha1Hex((user.getPassword())));
		}
		Integer userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId).eq(User::getAccount, user.getAccount()));
		if (userCount > 0 && Func.isEmpty(user.getId())) {
			throw new ServiceException(StringUtil.format("当前用户 [{}] 已存在!", user.getAccount()));
		}
		return save(user) && submitUserDept(user) && submitUserRole(user);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateUser(User user) {
		String tenantId = user.getTenantId();
		Integer userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getAccount, user.getAccount())
				.notIn(User::getId, user.getId())
		);
		if (userCount > 0) {
			throw new ServiceException(StringUtil.format("当前用户 [{}] 已存在!", user.getAccount()));
		}
		return updateUserInfo(user) && submitUserDept(user) && submitUserRole(user);
	}

	@Override
	public boolean updateUserInfo(User user) {
		user.setPassword(null);
		return updateById(user);
	}

	private boolean submitUserRole(User user) {
		List<Long> roleIdList = Func.toLongList(user.getRoleId());
		List<UserRole> userRoleList = new ArrayList<>();
		roleIdList.forEach(roleId -> {
			UserRole userRole = new UserRole();
			userRole.setUserId(user.getId());
			userRole.setRoleId(roleId);
			userRoleList.add(userRole);
		});
		userRoleService.remove(Wrappers.<UserRole>update().lambda().eq(UserRole::getUserId, user.getId()));
		return userRoleService.saveBatch(userRoleList);
	}

	private boolean submitUserDept(User user) {
		List<Long> deptIdList = Func.toLongList(user.getDeptId());
		List<UserDept> userDeptList = new ArrayList<>();
		deptIdList.forEach(deptId -> {
			UserDept userDept = new UserDept();
			userDept.setUserId(user.getId());
			userDept.setDeptId(deptId);
			userDeptList.add(userDept);
		});
		userDeptService.remove(Wrappers.<UserDept>update().lambda().eq(UserDept::getUserId, user.getId()));
		return userDeptService.saveBatch(userDeptList);
	}

	@Override
	public IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId) {
		List<Long> deptIdList = SysCache.getDeptChildIds(deptId);
		return page.setRecords(baseMapper.selectUserPage(page, user, deptIdList, tenantId));
	}

	@Override
	public IPage<UserVO> selectUserPage(IPage<UserVO> page, String tenantId, Long deptId, String userName) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return page.setRecords(baseMapper.selectUserPageByNameAndDept(page, tenantId, deptId, userName));
	}

	@Override
	public IPage<UserVO> selectUserSearch(UserVO user, Query query) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		String tenantId = AuthUtil.getTenantId();
		if (StringUtil.isNotBlank(tenantId)) {
			queryWrapper.eq(User::getTenantId, tenantId);
		}
		if (StringUtil.isNotBlank(user.getName())) {
			queryWrapper.like(User::getName, user.getName());
		}
		if (StringUtil.isNotBlank(user.getDeptName())) {
			String deptIds = SysCache.getDeptIdsByFuzzy(AuthUtil.getTenantId(), user.getDeptName());
			if (StringUtil.isNotBlank(deptIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(deptIds);
					ids.forEach(id -> wrapper.like(User::getDeptId, id).or());
				});
			}
		}
		if (StringUtil.isNotBlank(user.getPostName())) {
			String postIds = SysCache.getPostIdsByFuzzy(AuthUtil.getTenantId(), user.getPostName());
			if (StringUtil.isNotBlank(postIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(postIds);
					ids.forEach(id -> wrapper.like(User::getPostId, id).or());
				});
			}
		}
		IPage<User> pages = this.page(Condition.getPage(query), queryWrapper);
		return UserWrapper.build().pageVO(pages);
	}

	@Override
	public User userByAccount(String tenantId, String account) {
		return baseMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId).eq(User::getAccount, account).eq(User::getIsDeleted, SzykConstant.DB_NOT_DELETED));
	}

	@Override
	public UserInfo userInfo(Long userId) {
		User user = baseMapper.selectById(userId);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, String password) {
		User user = baseMapper.getUser(tenantId, account, password);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, String password, UserEnum userEnum) {
		User user = baseMapper.getUser(tenantId, account, password);
		return buildUserInfo(user, userEnum);
	}

	private UserInfo buildUserInfo(User user) {
		return buildUserInfo(user, UserEnum.WEB);
	}

	private UserInfo buildUserInfo(User user, UserEnum userEnum) {
		if (ObjectUtil.isEmpty(user)) {
			return null;
		}
		UserInfo userInfo = new UserInfo();
		userInfo.setUser(user);
		if (Func.isNotEmpty(user)) {
			List<String> roleAlias = roleService.getRoleAliases(user.getRoleId());
			userInfo.setRoles(roleAlias);
		}
		// 根据每个用户平台，建立对应的detail表，通过查询将结果集写入到detail字段
		Kv detail = Kv.create().set("type", userEnum.getName());
		if (userEnum == UserEnum.WEB) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else if (userEnum == UserEnum.APP) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		}
		userInfo.setDetail(detail);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo userInfo(UserOauth userOauth) {
		UserOauth uo = userOauthService.getOne(Wrappers.<UserOauth>query().lambda().eq(UserOauth::getUuid, userOauth.getUuid()).eq(UserOauth::getSource, userOauth.getSource()));
		UserInfo userInfo;
		if (Func.isNotEmpty(uo) && Func.isNotEmpty(uo.getUserId())) {
			userInfo = this.userInfo(uo.getUserId());
			userInfo.setOauthId(Func.toStr(uo.getId()));
		} else {
			userInfo = new UserInfo();
			if (Func.isEmpty(uo)) {
				userOauthService.save(userOauth);
				userInfo.setOauthId(Func.toStr(userOauth.getId()));
			} else {
				userInfo.setOauthId(Func.toStr(uo.getId()));
			}
			User user = new User();
			user.setAccount(userOauth.getUsername());
			user.setTenantId(userOauth.getTenantId());
			userInfo.setUser(user);
			userInfo.setRoles(Collections.singletonList(GUEST_NAME));
		}
		return userInfo;
	}

	@Transactional
	@Override
	public boolean grant(String userIds, String roleIds) {
		if (StringUtil.isBlank(userIds) || StringUtil.isBlank(roleIds)) {
			throw new ServiceException("用户或角色不能为空!");
		}
		// 修改用户表
		User user = new User();
		user.setRoleId(roleIds);
		boolean updateUser = this.lambdaUpdate().set(User::getRoleId, roleIds).in(User::getId, Func.toLongList(userIds)).update();
		if (!updateUser) {
			throw new ServiceException("用户角色修改失败!");
		}
		// 修改关联表
		String[] userIdList = userIds.split(",");
		String[] roleIdList = roleIds.split(",");
		List<Long> userIdLongList = Arrays.stream(userIdList).map(Long::valueOf).collect(Collectors.toList());
		boolean remove = userRoleService.removeByUserIds(userIdLongList);
		if (!remove) {
			throw new ServiceException("用户角色修改失败!");
		}
		List<UserRole> needAddList = new ArrayList<>();
		for (Long userId : userIdLongList) {
			for (String roleId : roleIdList) {
				UserRole userRole = new UserRole();
				userRole.setRoleId(Long.valueOf(roleId));
				userRole.setUserId(userId);
				needAddList.add(userRole);
			}
		}
		boolean saveBatch = userRoleService.saveBatch(needAddList);
		if (!saveBatch) {
			throw new ServiceException("角色关联关系保存失败!");
		}
		return true;
	}

	@Override
	public boolean resetPassword(String userIds) {
		User user = new User();
		user.setPassword(DigestUtil.encrypt(Func.toStr(ParamCache.getValue(CommonConstant.DEFAULT_PARAM_PASSWORD), CommonConstant.DEFAULT_PASSWORD)));
		user.setUpdateTime(DateUtil.now());

		return this.lambdaUpdate().in(User::getId, Func.toLongList(userIds)).set(User::getPassword, user.getPassword())
			.set(User::getUpdateTime, user.getUpdateTime())
			.update();
	}

	@Override
	public boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1) {
		User user = getById(userId);
		if (!newPassword.equals(newPassword1)) {
			throw new ServiceException("请输入正确的确认密码!");
		}
		if (!user.getPassword().equals(DigestUtil.hex(oldPassword))) {
			throw new ServiceException("原密码不正确!");
		}
		return this.update(Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.hex(newPassword)).eq(User::getId, userId));
	}

	@Override
	public boolean removeUser(String userIds) {
		if (Func.contains(Func.toLongArray(userIds), AuthUtil.getUserId())) {
			throw new ServiceException("不能删除本账号!");
		}
		return deleteLogic(Func.toLongList(userIds));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importUser(List<UserExcel> data, Boolean isCovered) {
		String loginTenantId = AuthUtil.getTenantId();
		data.forEach(userExcel -> {
			User user = Objects.requireNonNull(BeanUtil.copy(userExcel, User.class));
			String tenantId = user.getTenantId();
			if (Func.isBlank(tenantId)) {
				throw new ServiceException("租户编号不能为空");
			}
			if (!"000000".equals(loginTenantId) && !loginTenantId.equals(tenantId)) {
				throw new ServiceException("无权限操作此租户，您的租户编码为：" + loginTenantId);
			}
			String account = user.getAccount();
			if (Func.isBlank(account)) {
				throw new ServiceException("账户不能为空");
			}
			String realName = user.getRealName();
			if (Func.isBlank(realName)) {
				throw new ServiceException("姓名不能为空");
			}
			String roleName = userExcel.getRoleName();
			if (Func.isBlank(roleName)) {
				throw new ServiceException("角色名称不能为空");
			}
			String deptName = userExcel.getDeptName();
			if (Func.isBlank(deptName)) {
				throw new ServiceException("部门名称不能为空");
			}


			// 设置用户平台
			user.setUserType(Func.toInt(DictCache.getKey(DictEnum.USER_TYPE, userExcel.getUserTypeName()), 1));
			// 设置部门ID
			user.setDeptId(Func.toStrWithEmpty(SysCache.getDeptIds(userExcel.getTenantId(), userExcel.getDeptName()), StringPool.EMPTY));
			// 设置岗位ID
			user.setPostId(Func.toStrWithEmpty(SysCache.getPostIds(userExcel.getTenantId(), userExcel.getPostName()), StringPool.EMPTY));
			// 设置角色ID
			user.setRoleId(Func.toStrWithEmpty(SysCache.getRoleIds(userExcel.getTenantId(), userExcel.getRoleName()), StringPool.EMPTY));
			// 设置租户ID
			if (!AuthUtil.isAdministrator() || StringUtil.isBlank(user.getTenantId())) {
				user.setTenantId(AuthUtil.getTenantId());
			}
			String deptId = user.getDeptId();
			if (Func.isEmpty(deptId)) {
				throw new ServiceException("【" + deptName + "】" + "部门不存在");
			}
			String roleId = user.getRoleId();
			if (Func.isEmpty(roleId)) {
				throw new ServiceException("【" + roleName + "】" + "角色不存在");
			}
			// 覆盖数据
			if (isCovered) {
				// 查询用户是否存在
				User oldUser = UserCache.getUser(userExcel.getTenantId(), userExcel.getAccount());
				if (oldUser != null && oldUser.getId() != null) {
					user.setId(oldUser.getId());
					this.updateUser(user);
					return;
				}
			}
			// 获取默认密码配置
			String initPassword = ParamCache.getValue(CommonConstant.DEFAULT_PARAM_PASSWORD);
			user.setPassword(initPassword);
			this.submit(user);
		});
	}

	@Override
	public List<UserExcel> exportUser(Map<String, Object> queryWrapper) {
		List<UserExcel> userList = baseMapper.exportUser(queryWrapper);
		userList.forEach(user -> {
			user.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, user.getUserType()));
			user.setRoleName(StringUtil.join(SysCache.getRoleNames(user.getRoleId())));
			user.setDeptName(StringUtil.join(SysCache.getDeptNames(user.getDeptId())));
			user.setPostName(StringUtil.join(SysCache.getPostNames(user.getPostId())));
		});
		return userList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerGuest(User user, Long oauthId) {
		Tenant tenant = SysCache.getTenant(user.getTenantId());
		if (tenant == null || tenant.getId() == null) {
			throw new ServiceException("租户信息错误!");
		}
		UserOauth userOauth = userOauthService.getById(oauthId);
		if (userOauth == null || userOauth.getId() == null) {
			throw new ServiceException("第三方登陆信息错误!");
		}
		user.setRealName(user.getName());
		user.setAvatar(userOauth.getAvatar());
		user.setRoleId(StringPool.MINUS_ONE);
		user.setDeptId(StringPool.MINUS_ONE);
		user.setPostId(StringPool.MINUS_ONE);
		boolean userTemp = this.submit(user);
		userOauth.setUserId(user.getId());
		userOauth.setTenantId(user.getTenantId());
		boolean oauthTemp = userOauthService.updateById(userOauth);
		return (userTemp && oauthTemp);
	}

	@Override
	public boolean updatePlatform(Long userId, Integer userType, String userExt) {
		if (userType.equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userWeb.setId(query.getId());
			}
			userWeb.setUserId(userId);
			userWeb.setUserExt(userExt);
			return userWeb.insertOrUpdate();
		} else if (userType.equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userApp.setId(query.getId());
			}
			userApp.setUserId(userId);
			userApp.setUserExt(userExt);
			return userApp.insertOrUpdate();
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userOther.setId(query.getId());
			}
			userOther.setUserId(userId);
			userOther.setUserExt(userExt);
			return userOther.insertOrUpdate();
		}
	}

	@Override
	public UserVO platformDetail(User user) {
		User detail = baseMapper.selectOne(Condition.getQueryWrapper(user));
		UserVO userVO = UserWrapper.build().entityVO(detail);
		if (userVO.getUserType().equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else if (userVO.getUserType().equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		}
		return userVO;
	}

	@Override
	public List<UserDept> queryUserDeptListByCondition(String userName) {

		// 根据名称模糊查询用户数据
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.like(StringUtils.isNotBlank(userName), User::getRealName, userName);
		queryWrapper.eq(User::getIsDeleted, 0);
		List<User> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		// 用户ID
		List<Long> ids = list.stream().map(User::getId).collect(Collectors.toList());

		// 查询用户与部门对应关系
		LambdaQueryWrapper<UserDept> wrapper = Wrappers.<UserDept>query().lambda();
		wrapper.in(CollectionUtils.isNotEmpty(ids), UserDept::getUserId, ids);
		List<UserDept> userDeptList = userDeptService.list(wrapper);
		if (CollectionUtils.isEmpty(userDeptList)) {
			return null;
		}

		return userDeptList;
	}

	@Override
	public List<UserDTO> queryUserDeptListByIds(List<Long> idList) {

		List<UserDTO> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(idList)) {
			return result;
		}

		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.in(CollectionUtils.isNotEmpty(idList), User::getId, idList);
		queryWrapper.eq(User::getIsDeleted, 0);
		List<User> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		for (User user : list) {
			UserDTO userDTO = new UserDTO();
			BeanUtil.copyProperties(user, userDTO);

			// 查询部门信息
			Dept dept = deptService.getById(user.getDeptId());
			userDTO.setDeptName(dept == null ? null : dept.getDeptName());

			result.add(userDTO);
		}
		return result;
	}

	@Override
	public List<UserDTO> queryUserDeptListByDeptIds(List<Long> deptIdList) {

		List<UserDTO> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(deptIdList)) {
			return result;
		}

		LambdaQueryWrapper<UserDept> queryWrapper = Wrappers.<UserDept>query().lambda();
		queryWrapper.in(UserDept::getDeptId, deptIdList);
		List<UserDept> userDeptList = userDeptService.list(queryWrapper);
		if (CollectionUtils.isEmpty(userDeptList)) {
			return result;
		}

		List<Long> userIds = userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toList());

		return queryUserDeptListByIds(userIds);
	}

	@Override
	public List<UserDTO> queryUserDeptListByPostIds(List<Long> postIdList) {
		List<UserDTO> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(postIdList)) {
			return result;
		}

		List<Long> userIds = new ArrayList();

		for (Long postId : postIdList) {
			LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
			queryWrapper.like(User::getPostId, postId);
			queryWrapper.eq(User::getIsDeleted, 0);
			List<User> list = baseMapper.selectList(queryWrapper);
			if (CollectionUtils.isEmpty(list)) {
				continue;
			}
			userIds.addAll(list.stream().map(User::getId).collect(Collectors.toList()));
		}

		if (CollectionUtils.isEmpty(userIds)) {
			return result;
		}

		List<Long> newUserIds = userIds.stream().distinct().collect(Collectors.toList());

		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.in(User::getId, newUserIds);
		queryWrapper.eq(User::getIsDeleted, 0);
		List<User> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		for (User user : list) {
			UserDTO userDTO = new UserDTO();
			BeanUtil.copyProperties(user, userDTO);

			// 查询部门信息
			Dept dept = deptService.getById(user.getDeptId());
			userDTO.setDeptName(dept == null ? null : dept.getDeptName());

			result.add(userDTO);
		}

		return result;
	}

	@Override
	public List<UserDTO> queryUserDeptListByRoleIds(List<Long> roleIdList) {
		List<UserDTO> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(roleIdList)) {
			return result;
		}

		List<Long> userIds = new ArrayList();

		for (Long roleId : roleIdList) {
			LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
			queryWrapper.like(User::getRoleId, roleId);
			queryWrapper.eq(User::getIsDeleted, 0);
			List<User> list = baseMapper.selectList(queryWrapper);
			if (CollectionUtils.isEmpty(list)) {
				continue;
			}
			userIds.addAll(list.stream().map(User::getId).collect(Collectors.toList()));
		}

		if (CollectionUtils.isEmpty(userIds)) {
			return result;
		}

		List<Long> newUserIds = userIds.stream().distinct().collect(Collectors.toList());

		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.in(User::getId, newUserIds);
		queryWrapper.eq(User::getIsDeleted, 0);
		List<User> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		for (User user : list) {
			UserDTO userDTO = new UserDTO();
			BeanUtil.copyProperties(user, userDTO);

			// 查询部门信息
			Dept dept = deptService.getById(user.getDeptId());
			userDTO.setDeptName(dept == null ? null : dept.getDeptName());

			result.add(userDTO);
		}

		return result;
	}

	@Override
	public UserDTO queryUserById(Long userId) {

		UserDTO result = new UserDTO();

		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.eq(userId != null, User::getId, userId);
		User user = baseMapper.selectOne(queryWrapper);
		if (user == null) {
			return result;
		}

		BeanUtil.copyProperties(user, result);

		// 查询部门信息
		Dept dept = deptService.getById(user.getDeptId());
		result.setDeptName(dept == null ? null : dept.getDeptName());

		return result;
	}

	@Override
	public UserDeptDTO queryUserDept(Long userId, Long deptId) {
		UserDeptDTO result = new UserDeptDTO();

		// 查询部门信息
		Dept dept = deptService.getById(deptId);

		// 查询用户信息
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.eq(User::getId, userId);
		queryWrapper.eq(User::getIsDeleted, 0);
		User user = baseMapper.selectOne(queryWrapper);

		result.setEmployeeName(user == null ? null : user.getRealName());
		result.setAvatar(user == null ? null : user.getAvatar());
		result.setDeptName(dept == null ? null : dept.getDeptName());
		result.setOrgName(dept == null ? null : dept.getDeptName());
		return result;
	}

	@Override
	public List<String> roleKeyList(Long userId, Long deptId) {

		// 查询用户信息
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		queryWrapper.eq(User::getId, userId);
		queryWrapper.eq(User::getIsDeleted, 0);
		User user = baseMapper.selectOne(queryWrapper);

		if (user == null || StringUtils.isBlank(user.getRoleId())) {
			return null;
		}

		List<String> result = Arrays.asList(user.getRoleId().split(","));

		return result;
	}

}
