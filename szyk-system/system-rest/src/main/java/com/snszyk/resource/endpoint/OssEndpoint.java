/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.endpoint;

import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.oss.model.OssFile;
import com.snszyk.core.oss.model.SzykFile;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.utils.FileUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.builder.oss.OssBuilder;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import liquibase.pro.packaged.E;
import lombok.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * 对象存储端点
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@RequiredArgsConstructor
@Api(value = "对象存储端点", tags = "对象存储端点")
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/oss/endpoint")
public class OssEndpoint {
	private static final String[] FILE_FORMAT = {"doc", "docx", "xls", "xlsx", "pdf","jpeg", "jpg", "png","JPEG","JPG","PNG","ppt","pptx"};
	private static final String[] WGT_FORMAT_ARR = {"wgt"};
	private static final String[] FILE_FORMAT_ARR = {"jpeg", "jpg", "png","JPEG","JPG","PNG"};
	private static final String[] MODEL_FORMAT_ARR = {"fbx", "FBX"};
	@Value("${oss.endpoint}")
	@Getter
	@Setter
	private String ossEndPoint;
	@Getter
	@Setter
	@Value("${oss.endpoint-display}")
	private String ossEndPointDisplay;

	/**
	 * 对象存储构建类
	 */
	private final OssBuilder ossBuilder;

	/**
	 * 附件表服务
	 */
	private final IAttachService attachService;

	/**
	 * 创建存储桶
	 *
	 * @param bucketName 存储桶名称
	 * @return Bucket
	 */
	@SneakyThrows
	@PostMapping("/make-bucket")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R makeBucket(@RequestParam String bucketName) {
		ossBuilder.template().makeBucket(bucketName);
		return R.success("创建成功");
	}

	/**
	 * 创建存储桶
	 *
	 * @param bucketName 存储桶名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-bucket")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R removeBucket(@RequestParam String bucketName) {
		ossBuilder.template().removeBucket(bucketName);
		return R.success("删除成功");
	}

	/**
	 * 拷贝文件
	 *
	 * @param fileName       存储桶对象名称
	 * @param destBucketName 目标存储桶名称
	 * @param destFileName   目标存储桶对象名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/copy-file")
	public R copyFile(@RequestParam String fileName, @RequestParam String destBucketName, String destFileName) {
		ossBuilder.template().copyFile(fileName, destBucketName, destFileName);
		return R.success("操作成功");
	}

	/**
	 * 获取文件信息
	 *
	 * @param fileName 存储桶对象名称
	 * @return InputStream
	 */
	@SneakyThrows
	@GetMapping("/stat-file")
	public R<OssFile> statFile(@RequestParam String fileName) {
		return R.data(ossBuilder.template().statFile(fileName));
	}

	/**
	 * 获取文件相对路径
	 *
	 * @param fileName 存储桶对象名称
	 * @return String
	 */
	@SneakyThrows
	@GetMapping("/file-path")
	public R<String> filePath(@RequestParam String fileName) {
		return R.data(ossBuilder.template().filePath(fileName));
	}


	/**
	 * 获取文件外链
	 *
	 * @param fileName 存储桶对象名称
	 * @return String
	 */
	@SneakyThrows
	@GetMapping("/file-link")
	public R<String> fileLink(@RequestParam String fileName) {
		return R.data(ossBuilder.template().fileLink(fileName));
	}

	/**
	 * 上传文件
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file")
	public R<SzykFile> putFile(@RequestParam MultipartFile file) {
		validFileExtension(file);
		SzykFile szykFile = ossBuilder.template().putFile(file.getOriginalFilename(), file.getInputStream());
		return R.data(szykFile);
	}

	private static void validFileExtension(MultipartFile file) {
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(FILE_FORMAT).contains(fileExtension)) {
			throw new ServiceException("仅支持上传以下格式的文件：" + String.join(",", Arrays.asList(FILE_FORMAT)));
		}
	}

	/**
	 * 上传文件
	 *
	 * @param fileName 存储桶对象名称
	 * @param file     文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-by-name")
	public R<SzykFile> putFile(@RequestParam String fileName, @RequestParam MultipartFile file) {
		validFileExtension(file);
		SzykFile szykFile = ossBuilder.template().putFile(fileName, file.getInputStream());
		return R.data(szykFile);
	}

	/**
	 * 上传文件并保存至附件表
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-attach")
	public R<SzykFile> putFileAttach(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		validFileExtension(file);
		SzykFile szykFile = ossBuilder.template().putFile(fileName, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}
	@SneakyThrows
	@PostMapping("/put-wgt-attach")
	public R<SzykFile> putWgtAttach(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(WGT_FORMAT_ARR).contains(fileExtension)) {
			throw new ServiceException("仅支持上传.wgt格式的文件！");
		}
		SzykFile szykFile = ossBuilder.template().putFile(fileName, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}
	/**
	 * 上传文件并保存至附件表
	 *
	 * @param fileName 存储桶对象名称
	 * @param file     文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-attach-by-name")
	public R<SzykFile> putFileAttach(@RequestParam String fileName, @RequestParam MultipartFile file) {
		validFileExtension(file);
		SzykFile szykFile = ossBuilder.template().putFile(fileName, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}

	/**
	 * 构建附件表
	 *
	 * @param fileName  文件名
	 * @param fileSize  文件大小
	 * @param szykFile 对象存储文件
	 * @return attachId
	 */
	private Long buildAttach(String fileName, Long fileSize, SzykFile szykFile) {
		String fileExtension = FileUtil.getFileExtension(fileName);
		Attach attach = new Attach();
		attach.setDomain(szykFile.getDomain().replace(ossEndPoint,ossEndPointDisplay));
		attach.setLink(szykFile.getLink().replace(ossEndPoint,ossEndPointDisplay));
		attach.setName(szykFile.getName());
		attach.setOriginalName(szykFile.getOriginalName());
		attach.setAttachSize(fileSize);
		attach.setExtension(fileExtension);
		attachService.save(attach);
		return attach.getId();
	}

	/**
	 * 删除文件
	 *
	 * @param fileName 存储桶对象名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-file")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R removeFile(@RequestParam String fileName) {
		ossBuilder.template().removeFile(fileName);
		return R.success("操作成功");
	}

	/**
	 * 批量删除文件
	 *
	 * @param fileNames 存储桶对象名称集合
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-files")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R removeFiles(@RequestParam String fileNames) {
		ossBuilder.template().removeFiles(Func.toStrList(fileNames));
		return R.success("操作成功");
	}

	/**
	 * 下载文件
	 * @param attachId 文件attachId
	 * @param request req
	 * @param response res
	 */
	@GetMapping("/download/{attachId}")
	@ApiOperation(value = "下载文件", notes = "传入文件attachId")
	public void download(@PathVariable String attachId, HttpServletRequest request, HttpServletResponse response) {
		attachService.download(attachId, request, response);
	}
}
