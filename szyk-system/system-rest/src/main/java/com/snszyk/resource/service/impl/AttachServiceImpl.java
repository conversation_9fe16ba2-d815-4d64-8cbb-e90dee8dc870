/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.utils.FileReadUtil;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.mapper.AttachMapper;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.resource.utils.DownloadUtil;
import com.snszyk.resource.vo.AttachVO;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 附件表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class AttachServiceImpl extends BaseServiceImpl<AttachMapper, Attach> implements IAttachService {


	private static final Set<String> CONVERT_FILE_EXTENSIONS = new HashSet<>(Arrays.asList(
		"doc", "docx", "xls", "xlsx", "ppt", "pptx"));
	private static final Set<String> WORD_FILE_EXTENSIONS = new HashSet<>(Arrays.asList(
		"doc", "docx"));
	private static final Set<String> EXCEL_FILE_EXTENSIONS = new HashSet<>(Arrays.asList(
		"xls", "xlsx"));
	private static final String PDF_STR = "pdf";
	private static final String PPT_STR = "ppt";
	private static final String PPTX_STR = "pptx";

	private static final String XLS_STR = "xls";
	private static final String XLSX_STR = "xlsx";
	private static final String DOC_STR = "doc";
	private static final String DOCX_STR = "docx";

	private static final Set<String> IMG_EXTENSIONS = new HashSet<>(Arrays.asList(
		"jpg", "jpeg", "png", "gif"));


	@Override
	public IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachVO attach) {
		return page.setRecords(baseMapper.selectAttachPage(page, attach));
	}

	/**
	 * 下载文件
	 * @param attachId 附件id
	 * @param request req
	 * @param response res
	 */
	@Override
	public void download(String attachId, HttpServletRequest request, HttpServletResponse response) {
		Attach attach = getByIdIgnoreTenant(Long.valueOf(attachId));
		//文件不存在
		if (attach == null) {
			return;
		}
		//下载
		DownloadUtil.buildDownloadResponse(attach.getOriginalName(), request, response);
		try {
			FileCopyUtils.copy(new URL(attach.getLink()).openStream(), response.getOutputStream());
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Override
	public String readOffice(Long id) {
		String content = "";
		try {
			Attach attach = this.getById(id);
			String fileExtension = attach.getExtension();
			InputStream inputStream = FileReadUtil.openStream(attach.getLink());
			if (PDF_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readPdf(inputStream);
			}
			if (DOC_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readDoc(inputStream);
			}
			if (DOCX_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readDocx(inputStream);
			}
			if (XLS_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readXls(inputStream);
			}
			if (XLSX_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readXlsx(inputStream);
			}
			if (PPT_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readPpt(inputStream);
			}
			if (PPTX_STR.equalsIgnoreCase(fileExtension)) {
				content = FileReadUtil.readPptx(inputStream);
			}
			inputStream.close();
		} catch (Exception e) {
			log.error("文件解析:读取文件失败", e);
			e.printStackTrace();
		}
		return content;
	}

	@Override
	public Attach getByIdIgnoreTenant(Long hotUpdate) {
		return baseMapper.selectByIdIgnoreTenant(hotUpdate);

	}

	public static void main(String[] args) {
		try {
			InputStream inputStream = FileReadUtil.openStream("http://192.168.102.12:9000/cmks-dev/upload/20250103/a0dbc2e3197f0660ed1bfa3f6fa03f1b.pdf");

			String s = FileReadUtil.readPdf(inputStream);
			System.out.println(s);
			String sd = "�����\n" +
				"�������单层香蕉筛";
			//怎么去掉乱码的字符
			System.out.println(s.replaceAll("�", ""));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


}
