/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.auth.granter;

import com.snszyk.auth.provider.ITokenGranter;
import com.snszyk.auth.provider.TokenParameter;
import com.snszyk.core.jwt.JwtUtil;
import com.snszyk.core.jwt.props.JwtProperties;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.UserInfo;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import com.snszyk.core.launch.constant.TokenConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.auth.utils.TokenUtil;
import com.snszyk.system.service.IRoleService;
import com.snszyk.system.service.ITenantService;
import com.snszyk.system.service.IUserService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RefreshTokenGranter
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class RefreshTokenGranter implements ITokenGranter {

	public static final String GRANT_TYPE = "refresh_token";

	private final IUserService userService;
	private final IRoleService roleService;
	private final ITenantService tenantService;
	private final JwtProperties jwtProperties;

	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		String tenantId = tokenParameter.getArgs().getStr("tenantId");
		String grantType = tokenParameter.getArgs().getStr("grantType");
		String refreshToken = tokenParameter.getArgs().getStr("refreshToken");
		String deptId = tokenParameter.getArgs().getStr("deptId");
		String roleId = tokenParameter.getArgs().getStr("roleId");
		UserInfo userInfo = null;
		if (Func.isNoneBlank(grantType, refreshToken) && grantType.equals(TokenConstant.REFRESH_TOKEN)) {
			// 判断令牌合法性
			if (!judgeRefreshToken(grantType, refreshToken)) {
				throw new ServiceException(TokenUtil.TOKEN_NOT_PERMISSION);
			}
			Claims claims = AuthUtil.parseJWT(refreshToken);
			if (claims != null) {
				String tokenType = Func.toStr(claims.get(TokenConstant.TOKEN_TYPE));
				if (tokenType.equals(TokenConstant.REFRESH_TOKEN)) {
					// 获取租户信息
					Tenant tenant = tenantService.getByTenantId(tenantId);
					if (TokenUtil.judgeTenant(tenant)) {
						throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
					}
					// 获取用户信息
					userInfo = userService.userInfo(Func.toLong(claims.get(TokenConstant.USER_ID)));
					// 设置多部门信息
					if (Func.isNotEmpty(deptId) && userInfo.getUser().getDeptId().contains(deptId)) {
						userInfo.getUser().setDeptId(deptId);
					}
					// 设置多角色信息
					if (Func.isNotEmpty(roleId) && userInfo.getUser().getRoleId().contains(roleId)) {
						userInfo.getUser().setRoleId(roleId);
						List<String> roleAliases = roleService.getRoleAliases(roleId);
						userInfo.setRoles(roleAliases);
					}
				}
			}
		}
		return userInfo;
	}

	/**
	 * 校验refreshToken合法性
	 *
	 * @param grantType    认证类型
	 * @param refreshToken refreshToken
	 */
	private boolean judgeRefreshToken(String grantType, String refreshToken) {
		if (jwtProperties.getState() && jwtProperties.getSingle()) {
			Claims claims = JwtUtil.parseJWT(refreshToken);
			String tenantId = String.valueOf(claims.get("tenant_id"));
			String userId = String.valueOf(claims.get("user_id"));
			String token = JwtUtil.getRefreshToken(tenantId, userId, refreshToken);
			return StringUtil.equalsIgnoreCase(token, refreshToken);
		}
		return true;
	}
}
