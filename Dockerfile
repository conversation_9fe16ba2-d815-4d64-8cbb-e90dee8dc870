# FROM adoptopenjdk/openjdk8-openj9:alpine-slim
FROM registry.cn-hangzhou.aliyuncs.com/szyk_hub/alpine-java:openjdk8-openj9_cn_slim

MAINTAINER <EMAIL>

# 更新 apk 索引

# 更新 apk 索引

RUN apk update && \
    # 安装 openssh-client
    apk add --no-cache openssh-client


RUN mkdir -p /szyk

WORKDIR /szyk

EXPOSE 8800

ADD ./szyk-admin/target/archive-server.jar ./app.jar

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]

# CMD ["--spring.profiles.active=dev"]
