package com.snszyk.message.rabbit;

import com.alibaba.fastjson.JSON;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.message.socket.WebSocketServer;
import com.snszyk.message.vo.MessageVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 消息推送接收者
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class MessageReceiver {

	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_MESSAGE)
	public void pointValueReceive(MessageVo messageVo) {
		log.info("消息推送接收者收到消息：{}", messageVo);
		WebSocketServer.sendInfo(JSON.toJSONString(messageVo), messageVo.getTo());
	}

}
