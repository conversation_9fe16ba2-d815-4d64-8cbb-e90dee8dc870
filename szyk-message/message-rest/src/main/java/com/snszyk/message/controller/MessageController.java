package com.snszyk.message.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.dto.MessagePushDto;
import com.snszyk.message.dto.UnreadMessageCountDto;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessagePushVo;
import com.snszyk.message.vo.MessageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 消息 控制器
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/message")
@Api(value = "消息中心", tags = "消息中心接口")
public class MessageController extends SzykController {

	private final MessageLogicService messageLogicService;

	/**
	 * 提交消息（新增、编辑） - 推送消息
	 *
	 * @param v v
	 * @return
	 */
	@ApiOperationSupport(order = 1)
	@PostMapping(value = "/commitMessage")
	@ApiOperation(value = "提交消息（新增、编辑）", notes = "MessageVo")
	public R pushMessage(@RequestBody MessageVo v) {
		return messageLogicService.commitMessage(v);
	}

	/**
	 * 消息详情
	 */
	@ApiOperationSupport(order = 2)
	@GetMapping("/detail/{messageId}")
	@ApiOperation(value = "消息详情", notes = "传入消息id")
	public R<MessageDto> detail(@PathVariable String messageId) {
		return messageLogicService.detail(messageId);
	}

	/**
	 * 管理端消息分页 - 发送方
	 */
	@GetMapping("/pageSend")
	@ApiOperationSupport(order = 3)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "title", value = "消息标题", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "content", value = "消息内容", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "scheduleTime", value = "发送消息时间", paramType = "query", dataType = "int")
	})
	@ApiOperation(value = "管理端消息分页 - 发送方", notes = "MessageVo")
	public R<IPage<MessageDto>> pageSend(@ApiIgnore MessageVo vo, Query query) {
		IPage<MessageDto> messageList = messageLogicService.pageSend(Condition.getPage(query), vo);
		return R.data(messageList);
	}

	/**
	 * 删除消息 - 发送方
	 */
	@ApiOperationSupport(order = 4)
	@PostMapping("/deleteMessage")
	@ApiOperation(value = "删除消息 - 发送方", notes = "传入消息id")
	public R deleteMessage(Long id) {
		messageLogicService.deleteMessage(id);
		return R.success("删除成功");
	}

	/**
	 * 消息接收分页列表 - 接收方
	 */
	@GetMapping("/pageReceive")
	@ApiOperationSupport(order = 5)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "type", value = "消息类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bizType", value = "消息业务类型：SENSOR_OFFLINE：传感器离线；SENSOR_FALL：传感器脱落；SAMPLE_DATA_EXCEPTION：传感器采样数据异常", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "keyword", value = "关键词 - 标题或内容模糊匹配", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "hasRead", value = "是否已读：", paramType = "query", dataType = "int")
	})
	@ApiOperation(value = "消息接收分页列表 - 接收方", notes = "MessagePushVo")
	public R<IPage<MessagePushDto>> pageReceive(@ApiIgnore MessagePushVo v, Query query, SzykUser user) {
		v.setReceiver(user.getUserId());
		IPage<MessagePushDto> pageQueryResult = messageLogicService.pageReceive(Condition.getPage(query), v);
		return R.data(pageQueryResult);
	}

	/**
	 *  已读消息
	 */
	@ApiOperationSupport(order = 6)
	@PostMapping(value = "/readMessage")
	@ApiOperation(value = "已读消息", notes = "MessagePushVo")
	public R<Boolean> readMessage(@RequestBody MessagePushVo v) {
		return R.data(messageLogicService.readMessage(v));
	}

	/**
	 * 消息全部已读
	 */
	@ApiOperationSupport(order = 7)
	@PostMapping("/realAllMessage")
	@ApiOperation(value = "消息全部已读")
	public R<Boolean> realAllMessage(MessagePushVo v, SzykUser user) {
		v.setReceiver(user.getUserId());
		return R.data(messageLogicService.updateAllReadMessagePushVo(v));
	}

	/**
	 * 消息未读总数
	 */
	@ApiOperationSupport(order = 8)
	@GetMapping("/countNotReadMessage")
	@ApiOperation(value = "消息未读总数", notes = "MessagePushVo")
	public R<UnreadMessageCountDto> countNotReadMessage(SzykUser user) {
		UnreadMessageCountDto dto = messageLogicService.countNotReadMessage(user.getUserId());
		return R.data(dto);
	}

	/**
	 * 删除所有已读消息
	 */
	@ApiOperationSupport(order = 9)
	@GetMapping("/deleteAllReadMessage")
	@ApiOperation(value = "删除所有已读消息")
	public R<Boolean> deleteAllReadMessage(SzykUser user) {
		return R.data(messageLogicService.deleteAllReadMessage(user.getUserId()));
	}

}
