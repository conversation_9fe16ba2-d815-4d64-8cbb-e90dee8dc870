package com.snszyk.message.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.dto.MessageSettingDTO;
import com.snszyk.message.service.logic.MessageSettingLogicService;
import com.snszyk.message.vo.MessageSettingSaveOrUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 消息设置管理控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/message-setting")
@Api(value = "消息设置管理", tags = "消息设置管理接口")
@Validated
public class MessageSettingController extends SzykController {

	private final MessageSettingLogicService messageSettingLogicService;

	/**
	 * 新增或修改
	 */
	@PostMapping
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增或修改", notes = "传入messageSetting")
	public R saveOrUpdate(@Valid @RequestBody MessageSettingSaveOrUpdateVO v) {
		return R.status(messageSettingLogicService.saveOrUpdate(v));
	}

	/**
	 * 根据业务类型获取消息设置
	 */
	@GetMapping
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据业务类型获取消息设置", notes = "传入tenantId和bizType")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "tenantId", value = "租户ID", paramType = "query", dataType = "string", required = true),
		@ApiImplicitParam(name = "bizType", value = "业务类型", paramType = "query", dataType = "string", required = true)
	})
	public R<MessageSettingDTO> getByBizType(
		@NotBlank(message = "租户ID不能为空") String tenantId,
		@NotBlank(message = "业务类型不能为空") String bizType) {
		MessageSettingDTO messageSetting = messageSettingLogicService.getByBizType(tenantId, bizType);
		return R.data(messageSetting);
	}
}
