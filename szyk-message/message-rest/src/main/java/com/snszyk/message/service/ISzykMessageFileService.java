/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.message.dto.SzykMessageFileDto;
import com.snszyk.message.vo.SzykMessageFileVo;

import java.util.List;

/**
 * 信息附件表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
public interface ISzykMessageFileService extends IBaseCrudService<SzykMessageFileDto, SzykMessageFileVo> {

	boolean deleteByBusiness(Long businessId, Integer businessType);

	boolean saveBatch(List<SzykMessageFileDto> attachList);

	List<SzykMessageFileDto> listAttachByBusinessId(List<Long> businessIds, String businessType);
}
