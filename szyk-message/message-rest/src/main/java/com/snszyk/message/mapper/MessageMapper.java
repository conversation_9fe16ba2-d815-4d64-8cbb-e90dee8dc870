/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.entity.Message;
import com.snszyk.message.vo.MessageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息 mapper
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
public interface MessageMapper extends BaseMapper<Message> {

	/**
	 * 消息发送分页查询
	 * @param page 分页
	 * @param vo 查询条件
	 * @return
	 */
    List<MessageDto> pageSend(IPage<MessageDto> page, @Param("vo") MessageVo vo);
}
