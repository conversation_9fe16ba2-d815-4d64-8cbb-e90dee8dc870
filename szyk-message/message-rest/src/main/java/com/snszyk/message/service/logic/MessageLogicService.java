package com.snszyk.message.service.logic;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.kafka.producer.KafkaProducer;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.constant.KafkaTopic;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.dto.MessagePushDto;
import com.snszyk.message.dto.UnreadMessageCountDto;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.IMessageAppService;
import com.snszyk.message.service.IMessageLogicService;
import com.snszyk.message.service.IMessagePushService;
import com.snszyk.message.service.IMessageService;
import com.snszyk.message.vo.MessagePushVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.entity.UserRole;
import com.snszyk.system.service.IUserDeptService;
import com.snszyk.system.service.IUserRoleService;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息logic
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
@Slf4j
public class MessageLogicService extends BaseCrudLogicService<MessageDto, MessageVo> implements IMessageLogicService {

	private final IMessageService messageService;

	private final IMessagePushService messagePushService;

	private final KafkaProducer kafkaProducer;

	private final IMessageAppService messageAppService;

	private final IUserDeptService userDeptService;

	private final IUserRoleService userRoleService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.messageService;
	}

	/**
	 * 消息推送
	 *
	 * @param messageVo
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R commitMessage(MessageVo messageVo) {
		if (StringUtil.isBlank(messageVo.getTitle())) {
			return R.fail("消息标题不能为空");
		}
		if (StringUtil.isBlank(messageVo.getContent())) {
			return R.fail("消息内容不能为空");
		}
		if (StringUtil.isBlank(messageVo.getType())) {
			return R.fail("消息类型不能为空");
		}
		if (StringUtil.isBlank(messageVo.getSender())) {
			return R.fail("发送人不能为空");
		}

		//保存消息
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			messageVo.setScheduleTime(new Date());
		}
		messageVo.setReceiverInfo(JSON.toJSONString(messageVo.getReceiverInfoVo()));
		messageVo.setAppKey("aaa");
		MessageDto messageDto = messageService.save(messageVo);

		//立即发送 - 根据用户类型获取消息接收者
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			String receiverType = messageVo.getReceiverType();
			ReceiverInfoVo receiverInfoVo = messageVo.getReceiverInfoVo();
			if (ReceiverTypeEnum.USER.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.UserVo> userList = receiverInfoVo.getUserList();
				messageVo.setTo(userList.stream()
					.map(ReceiverInfoVo.UserVo::getId)
					.distinct().toArray(Long[]::new));
			} else if (ReceiverTypeEnum.DEPT.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.DeptVo> deptList = receiverInfoVo.getDeptList();
				QueryWrapper<UserDept> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().in(UserDept::getDeptId,
					deptList.stream().map(ReceiverInfoVo.DeptVo::getDeptId).collect(Collectors.toList()));
				List<UserDept> userDeptList = userDeptService.list(queryWrapper);
				messageVo.setTo(userDeptList.stream()
					.map(UserDept::getUserId)
					.distinct().toArray(Long[]::new));
			} else if (ReceiverTypeEnum.ROLE.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.RoleVo> roleList = receiverInfoVo.getRoleList();
				QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().in(UserRole::getRoleId,
					roleList.stream().map(ReceiverInfoVo.RoleVo::getRoleId).collect(Collectors.toList()));
				List<UserRole> userRoleList = userRoleService.list(queryWrapper);
				messageVo.setTo(userRoleList.stream()
					.map(UserRole::getUserId)
					.distinct().toArray(Long[]::new));
			} else {
				throw new ServiceException("非法的推送用户类型：" + receiverType);
			}

			//发送消息
			Long[] to = messageVo.getTo();
			if (to != null) {
				String[] typeArray = messageVo.getType().split(",");
				for (Long toLong : to) {
					for (String type : typeArray) {
						MessagePushVo messagePushVo = new MessagePushVo();
						messagePushVo.setMessageId(messageDto.getId());
						messagePushVo.setReceiver(toLong);
						messagePushVo.setType(type);
						messagePushVo.setHasRead(0);
						messagePushService.save(messagePushVo);
					}
				}
				kafkaProducer.producerMessage(KafkaTopic.TOPIC1, messageVo);
				return R.success("推送成功");
			} else {
				return R.fail("消息接收人不能为空");
			}
		} else {
			return R.success("提交成功");
		}
	}

	/**
	 * 已读消息
	 *
	 * @param messagePushVo
	 * @return
	 */
	public MessagePushDto readMessage(MessagePushVo messagePushVo) {
		messagePushVo.setHasRead(1);
		messagePushVo.setReadTime(new Date());
		return messagePushService.save(messagePushVo);
	}

	/**
	 * 消息接收分页列表
	 *
	 * @param messagePushVo 查询条件
	 * @return
	 */
	public IPage<MessagePushDto> pageReceive(MessagePushVo messagePushVo) {
		return messagePushService.pageReceive(messagePushVo);
	}

	/**
	 * 消息全部已读
	 *
	 * @param v
	 */
	public void updateAllReadMessagePushVo(MessagePushVo v) {
		messagePushService.updateAllReadMessagePushVo(v);
	}

	/**
	 * 消息详情
	 * @param messageId 消息id
	 * @return
	 */
	public R<MessageDto> detail(String messageId) {
		MessageDto messageDetail = messageService.fetchById(Long.parseLong(messageId));
		messageDetail.setReceiverInfoVo(JSON.parseObject(messageDetail.getReceiverInfo(), ReceiverInfoVo.class));
		return R.data(messageDetail);
	}

	/**
	 * 消息发送分页列表 - 管理员
	 * @param vo 查询条件
	 * @return
	 */
	public IPage<MessageDto> pageSend(MessageVo vo) {
		return messageService.pageSend(vo);
	}

	/**
	 * 删除消息
	 * @param id 消息id
	 */
	public void deleteMessage(Long id) {
		MessageDto messageDto = messageService.fetchById(id);
		if (messageDto == null) {
			throw new ServiceException("消息不存在，id = " + id);
		}
		if (YesNoEnum.YES.getCode().equals(messageDto.getIsImmediate())) {
			throw new ServiceException("消息已发送，不允许删除！");
		}
		messageService.deleteById(id);
	}

	/**
	 * 获取未读消息数
	 * @param userId 用户id
	 * @return
	 */
	public UnreadMessageCountDto countNotReadMessage(Long userId) {
		UnreadMessageCountDto dto = new UnreadMessageCountDto();
		dto.setInAppCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.IN_APP.getCode()));
		dto.setWorkToDoCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.WORK_TODO.getCode()));
		dto.setWarningCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.WARNING.getCode()));
		dto.setNoticeCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.NOTICE.getCode()));
		dto.setTotalCount(dto.getInAppCount() + dto.getWorkToDoCount() + dto.getWarningCount() + dto.getNoticeCount());
		return dto;
	}
}
