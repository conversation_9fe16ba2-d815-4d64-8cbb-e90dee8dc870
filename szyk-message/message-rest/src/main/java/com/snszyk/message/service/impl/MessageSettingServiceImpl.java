package com.snszyk.message.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.message.entity.MessageSetting;
import com.snszyk.message.mapper.MessageSettingMapper;
import com.snszyk.message.service.IMessageSettingService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息设置表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class MessageSettingServiceImpl extends BaseServiceImpl<MessageSettingMapper, MessageSetting> implements IMessageSettingService {
	@Override
	public MessageSetting getBy(String tenantId, String bizType) {
		return this.lambdaQuery()
			.eq(MessageSetting::getTenantId, tenantId)
			.eq(MessageSetting::getBizType, bizType)
			.one();
	}

	@Override
	public List<MessageSetting> listBy(String bizType) {
		return this.lambdaQuery()
			.eq(MessageSetting::getBizType, bizType)
			.list();
	}
}
