<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.message.mapper.MessagePushMapper">

    <select id="pageReceive" resultType="com.snszyk.message.dto.MessagePushDto">
        SELECT smp.id, smp.message_id, smp.receiver, smp.has_read,
            smp.read_time, smp.create_time, sm.title, sm.content
        FROM szyk_message_push smp
        LEFT JOIN szyk_message sm ON smp.message_id = sm.id
        <where>
            smp.is_deleted = 0 AND smp.receiver = #{vo.receiver}
            <if test="vo.keyword != null and vo.keyword !=''">
                AND (sm.title LIKE concat('%',#{vo.keyword},'%') OR sm.content LIKE concat('%',#{vo.keyword},'%'))
            </if>
            <if test="vo.type != null and vo.type !=''">
                AND smp.type = #{vo.type}
            </if>
            <if test="vo.hasRead != null">
                AND smp.has_read = #{vo.hasRead}
            </if>
        </where>
        ORDER BY smp.has_read ASC, smp.create_time DESC
    </select>
</mapper>
