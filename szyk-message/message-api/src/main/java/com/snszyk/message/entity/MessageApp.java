package com.snszyk.message.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 消息应用entity
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Data
@Accessors(chain = true)
@TableName("szyk_message_app")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MessageApp对象", description = "MessageApp对象")
public class MessageApp extends BaseEntity {

	/**
	 * appKey
	 */
	@ApiModelProperty(value = "appKey")
	private String appKey;

	/**
	 * app名称
	 */
	@ApiModelProperty(value = "app名称")
	private String appName;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String mark;

}
