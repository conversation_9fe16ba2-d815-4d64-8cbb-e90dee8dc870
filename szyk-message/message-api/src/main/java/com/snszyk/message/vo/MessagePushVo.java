package com.snszyk.message.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 消息推送vo
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Data
@ApiModel
public class MessagePushVo extends BaseCrudVo {

	/**
	 * 消息id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "消息id")
	private Long messageId;

	/**
	 * 收信人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "收信人")
	private Long receiver;

	/**
	 * 消息类型
	 */
	@ApiModelProperty(value = "消息类型")
	private String type;

	/**
	 * 是否已读
	 */
	@ApiModelProperty(value = "是否已读")
	private Integer hasRead;

	/**
	 * 读信时间
	 */
	@ApiModelProperty(value = "读信时间")
	private Date readTime;

	/**
	 * 关键词 - 标题或内容模糊匹配
	 */
	@ApiModelProperty(value = "关键词")
	private String keyword;
}
