package com.snszyk.common.utils;

public interface InfluxdbQuerySQL {

	String bucket();
	int limit();

	InfluxdbQuerySQL configLimit(int limit);

	InfluxdbQuerySQL addFilter(String column,String value,String operator);
	InfluxdbQuerySQL setRange(Long start,Long end);

	InfluxdbQuerySQL addSort(String column,boolean desc);

	InfluxdbQuerySQL setPage(int pagesize,int pageno);
	String createQuerySQL();

	/**
	 * 行转列处理
	 * 增加 |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
	 * @return SQL
	 */
	StringBuffer getQuerySQL();
}
