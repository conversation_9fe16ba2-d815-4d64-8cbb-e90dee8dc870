package com.snszyk.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 * @date ：Created in 2020/9/5 13:46
 */
public class DateUtil {

    Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static final String ERROR_MESSAGE = "时间格式不正确";

    public static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取时间间隔（毫秒）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 间隔毫秒
     */
    public static long interval(Long startTime, Long endTime) {

        if (startTime == null || endTime == null || startTime >= endTime) {
            return 0;
        }
        return endTime - startTime;
    }

    public static Date reduce(Date startTime, Long period){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.set(Calendar.MILLISECOND,-(period.intValue()));
        return calendar.getTime();
    }

    public static Date plus(Date startTime, Long period){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.set(Calendar.MILLISECOND,period.intValue());
        return calendar.getTime();
    }

    public static String longToStr(Date date){
        return sdf.format(date);
    }

    /**
     * 获取UTC时间，并减少8小时
     * @param timeStr
     * @return
     */
    public static String getUTC (String timeStr) throws ParseException {
        DateFormat dtfUTC = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//注意月份是MM
        Date date1 = simpleDateFormat.parse(timeStr);
        Calendar ca = Calendar.getInstance();
        ca.setTime(date1);
        ca.add(Calendar.HOUR,-8);//日期减8小时
        Date dt1=ca.getTime();
        String reslut = dtfUTC.format(dt1);
        return reslut;
    }
}
