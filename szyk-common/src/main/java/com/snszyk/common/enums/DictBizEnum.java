/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务字典枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DictBizEnum {

	/**
	 * 测试
	 */
	TEST("test"),
	/**
	 * 基础树节点类型
	 */
	DEVICE_CATEGORY("device_category"),
	/**
	 * 设备分类
	 */
	EQ_CATEGORY("eq_category"),
	/**
	 * 设备类型
	 */
	EQ_TYPE("eq_type"),
	/**
	 * 传感器分类
	 */
	SENSOR_CATEGORY("sensor_category"),
	/**
	 * 故障类型数据类型
	 */
	FAULT_TYPE_CATEGORY("fault_type_category"),
	/**
	 * 测量类型
	 */
	MEASURE_TYPE("measure_type"),
	/**
	 * 故障管理-单据状态
	 */
	FAULT_BIZ_STATUS("fault_biz_status"),
	/**
	 * 故障管理-故障等级
	 */
	FAULT_LEVEL("fault_level"),
	/**
	 * 故障管理-设备状态
	 */
	DEVICE_STATUS("device_status"),
	/**
	 * 故障管理-检维护类型
	 */
	MAINTAIN_TYPE("maintain_type"),
	/**
	 * 故障管理-故障原因
	 */
	FAULT_REASON("fault_reason"),
	/**
	 * 故障管理-操作步骤
	 */
	OPERATE_STEP("operate_step"),
	/**
	 * 故障管理-验证结论
	 */
	VERIFY_CONCLUSION("fault_verification_conclusion"),
	/**
	 * 报警门限-报警类型
	 */
	THRESHOLD_ALARM_TYPE("alarm_type"),
	/**
	 * 报警管理-单据状态
	 */
	ALARM_BIZ_STATUS("alarm_biz_status"),
	/**
	 * 报警管理-报警类型
	 */
	ALARM_BIZ_TYPE("alarm_biz_type"),
	/**
	 * 报警管理-报警指标
	 */
	ALARM_INDEX("alarm_index"),
	/**
	 * 报警管理-振动分区
	 */
	VIBRATE_SUBAREA("vibrate_subarea"),
	/**
	 * 报警管理-诊断类型
	 */
	DIAGNOSIS_TYPE("diagnosis_type"),
	/**
	 * 报警管理-报警等级
	 */
	ALARM_LEVEL("alarm_level"),
	/**
	 * 报警管理-报警指标
	 */
	THRESHOLD_QUOTA_TYPE("threshold_quota_type"),
	/**
	 * 振动类型
	 */
	VIBRATION_TYPE("vibration_type"),
	/**
	 * 采样数据类型
	 */
	SAMPLED_DATA_TYPE("sampled_data_type"),
	/**
	 * 采样数据单位
	 */
	SAMPLED_DATA_UNIT("sampled_data_unit"),
	/**
	 * 诊断算法名称
	 */
	ARITHMETIC_NAME("arithmetic_name"),

	/**
	 * 传感器or采集站在线状态：0-离线；1-在线
	 */
	ONLINE_STATE("online_state"),

	/**
	 * 传感器类型：0-有线；1-无线；
	 */
	SENSOR_TYPE("sensor_type"),

	/**
	 * 生产工艺流程
	 */
	PRODUCE_TECH("produce_tech"),

	/**
	 * 传感器轴向
	 */
	SENSOR_SHAFT("sensor_shaft"),

	/**
	 * 传感器测量方向
	 */
	SENSOR_MEASURE_DIRECTION("sensor_measure_direction"),

	/**
	 * 起始频率
	 */
	INITIAL_FREQ("initial_freq"),

	/**
	 * 采样频率
	 */
	SAMPLING_FREQ("sampling_freq"),

	/**
	 * 采样点数
	 */
	SAMPLING_POINTS("sampling_points"),

	/**
	 * 传感器厂家
	 */
	SENSOR_SUPPLIER("sensor_supplier"),

	/**
	 * 传感器振动类型 - 特征值
	 */
	SENSOR_FEATURE("acceleration_feature"),

	/**
	 * 设备归属类型
	 */
	EQUIPMENT_TYPE("equipment_type"),

	/**
	 * 功率范围
	 */
	POWER_RANGE("power_range"),

	/**
	 * 机理类型
	 */
	MODEL_TYPE("model_type"),

	/**
	 * 数据类型
	 */
	DATA_TYPE("apply_data_type"),

	/**
	 * 机理模型阈值类型
	 */
	MODEL_PARAM_TYPE("model_param_type"),

	/**
	 * 机理模型阈值参数
	 */
	MODEL_PARAM("model_param"),

	/**
	 * 非振动采样数据类型
	 */
	NON_VIBRATION_TYPE_PARAMETER("non_vibration_type_parameter"),

	/**
	 * 振动采样数据类型
	 */
	VIBRATION_TYPE_PARAMETER("vibration_type_parameter"),

	/**
	 * 设备运行状态
	 */
	RUN_STATUS("run_status"),

	/**
	 * 设备等级
	 */
	EQUIPMENT_GRADE("equipment_grade"),
	/**
	 * 所属行业
	 */
	INDUSTRY("industry"),
	/**
	 * 设备的过滤
	 */
	EQUIPMENT_FILTER("equipment_filter"),

	/**
	 * 诊断报告类型
	 */
	DIAGNOSIS_REPORT_TYPE("diagnosis_report_type"),

	/**
	 * 报告发布状态
	 */
	REPORT_PUBLISH_STATUS("report_publish_status"),
	/**
	 * 报警关闭原因
	 */
	ALARM_CLOSE_REASON("alarm_close_reason")
	;

	final String name;

}
