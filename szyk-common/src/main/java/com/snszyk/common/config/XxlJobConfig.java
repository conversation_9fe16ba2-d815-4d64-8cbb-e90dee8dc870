package com.snszyk.common.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Slf4j
@Configuration
public class XxlJobConfig {
	private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

	@Value("${xxl.job.admin.addresses}")
	private String adminAddress;
	@Value("${xxl.job.admin.accessToken}")
	private String accessToken;
	@Value("${xxl.job.admin.timeout}")
	private Integer timeout;
	@Value("${xxl.job.executor.appname}")
	private String appname;
	@Value("${xxl.job.executor.logretentiondays}")
	private Integer logRetentionDays;
	@Value("${xxl.job.executor.logPath}")
	private String logPath;


	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		logger.info(">>>>>>>>>>> xxl-job config init.");
		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
		xxlJobSpringExecutor.setAdminAddresses(adminAddress);
		xxlJobSpringExecutor.setAppName(appname);
		xxlJobSpringExecutor.setLogPath(logPath);
		xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
		xxlJobSpringExecutor.setAccessToken(accessToken);
		return xxlJobSpringExecutor;
	}


}
