<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.snszyk</groupId>
        <artifactId>szyk-elasticsearch</artifactId>
        <version>2.0.0.RELEASE</version>
    </parent>

    <groupId>com.snszyk</groupId>
    <artifactId>elasticsearch-rest</artifactId>
    <version>2.0.0.RELEASE</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- Szyk -->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-boot</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.snszyk</groupId>
                    <artifactId>szyk-core-cloud</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-crud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-swagger</artifactId>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>elasticsearch-api</artifactId>
            <version>2.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>system-rest</artifactId>
            <version>2.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>zbusiness-api</artifactId>
            <version>2.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <!--        <finalName>${project.name}</finalName>-->
        <resources>
            <!--            <resource>-->
            <!--                <directory>src/main/resources</directory>-->
            <!--            </resource>-->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
