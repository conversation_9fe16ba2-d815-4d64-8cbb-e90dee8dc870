package com.snszyk.elasticsearch.service;

import com.snszyk.elasticsearch.dto.EsFullTextDto;
import com.snszyk.elasticsearch.vo.EsKnowledgeSearchPageVo;
import com.snszyk.elasticsearch.vo.EsKnowledgeVo;
import com.snszyk.zbusiness.file.dto.FileDto;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.util.List;

/**

 *
 */
public interface IEsKnowledgeService {

	Integer save(EsKnowledgeVo vo);


	EsPageInfo<EsFullTextDto> pageFullText(EsKnowledgeSearchPageVo vo);

	List<Long> findSimilarDocumentsByIds(List<Long> ids, int pageSize, int pageNum);

	List<FileDto> recommendDoc(String fileIds);

	EsFullTextDto oneFileStat(String fileId);

	Integer removeByIds(List<Long> removeEsFileIds);

	Integer rename(Long id,String fileName);

	EsPageInfo<EsFullTextDto> spacePageFullText(EsKnowledgeSearchPageVo vo);
}
