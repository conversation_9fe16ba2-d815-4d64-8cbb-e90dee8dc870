package com.snszyk.elasticsearch.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: EsArchiveFileDto
 * Package: com.snszyk.elasticsearch.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/4/23 16:10
 */
@Data
public class EsArchiveFileDto {

	private Long id;
	/**
	 * 档案idid
	 */
	@ApiModelProperty(value = "档案id")
	private Long archiveId;
	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private String fileName;
	/**
	 * 文件类型，数据字典
	 */
	@ApiModelProperty("文件类型名称")
	private String fileTypeName;
	/**
	 * 文件大小
	 */
	@ApiModelProperty(value = "文件大小")
	private Long fileSize;
	/**
	 * 文件版本
	 */
	@ApiModelProperty(value = "文件版本")
	private String fileVersion;
	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer pageNumbers;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;
	/**
	 * 长期保存id
	 */
	@ApiModelProperty(value = "长期保存id")
	private Long longTerm;
	/**
	 * 附件内容
	 */
	@ApiModelProperty("附件内容")
	private EsAttachmentDto attachment;
}
