package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器类型参数表
 * <AUTHOR>
 */
@Data
@TableName("basic_sensor_type_param")
@Accessors(chain = true)
@EqualsAndHashCode()
@ApiModel(value = "传感器类型参数表")
public class SensorTypeParam implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private Long id;

	/**
	 * 传感器类型id
	 */
	@ApiModelProperty(value = "传感器类型id")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private Long typeId;

	/**
	 * 参数名称
	 */
	@ApiModelProperty(value = "参数名称")
	private String paramName;

	/**
	 * 采样数据类型（字典：sampled_data_type）
	 */
	@ApiModelProperty(value = "采样数据类型（字典：sampled_data_type）")
	private String sampleDataType;

	/**
	 * 是否振动（0：非振动:1：振动）
	 */
	@ApiModelProperty(value = "是否振动（0：非振动:1：振动）")
	private Integer vibrationType;

	/**
	 * 轴方向（0:z轴，1:x轴，2:y轴）
	 */
	@ApiModelProperty(value = "轴方向（0:z轴，1:x轴，2:y轴）")
	private Integer axialDirection;

	/**
	 * 最大采样点数（字典：sampling_points）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样点数")
	private Integer maxSamplingPoints;

	/**
	 * 最大采样频率KHz（字典：sampling_freq）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal maxSamplingFreq;

	/**
	 * 默认采样点数（字典：sampling_points）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "默认采样点数")
	private Integer defaultSamplingPoints;

	/**
	 * 默认采样频率KHz（字典：sampling_freq）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "默认采样频率（KHz）")
	private BigDecimal defaultSamplingFreq;

	/**
	 * 默认特征值（多个用英文逗号分隔）
	 */
	@ApiModelProperty(value = "默认特征值（多个用英文逗号分隔）")
	private String defaultFeatures;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;
}
