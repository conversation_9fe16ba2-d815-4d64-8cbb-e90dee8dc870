package com.snszyk.zbusiness.base.enums;

import com.snszyk.common.constant.IBaseDict;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 参考类型
 * Description
 *
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ReferenceTypeEnum implements IBaseDict {
	none("1", "无"),
	dict("2", "业务字典"),
	classification("3", "档案门类"),
	;
	private String key;
	private String value;
}
