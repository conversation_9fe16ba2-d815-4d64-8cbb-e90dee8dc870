/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 报警记录表实体类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@Accessors(chain = true)
@TableName("eolm_alarm_record")
@ApiModel(value = "AlarmRecord对象", description = "报警记录表")
public class AlarmRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 报警id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警id")
	private Long alarmId;

	/**
	 * 报警明细id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警明细id")
	private Long alarmDetailId;

	/**
	 * 传感器采样数据id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("传感器采样数据id")
	private Long sensorDataId;

	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("波形id")
	private Long waveId;

	/**
	 * 采样数据类型（字典：sampled_data_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("采样数据类型（字典：sampled_data_type）")
	private String sampleDataType;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 报警点
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警点")
	private Long monitorId;
	/**
	 * 报警点名称
	 */
	@ApiModelProperty(value = "报警点名称")
	private String monitorName;
	/**
	 * 测点路径
	 */
	@ApiModelProperty(value = "测点路径")
	private String monitorPath;
	/**
	 * 机理模型故障类型
	 */
	@ApiModelProperty(value = "机理模型故障类型")
	private String faultType;
	/**
	 * 机理模型故障等级
	 */
	@ApiModelProperty(value = "机理模型故障等级")
	private String faultGrade;
	/**
	 * 报警类型（字典：alarm_biz_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警类型（字典：alarm_biz_type）")
	private Integer alarmType;
	/**
	 * 报警等级（字典：alarm_level）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警等级（字典：alarm_level）")
	private Integer alarmLevel;
	/**
	 * 报警指标
	 */
	@ApiModelProperty(value = "报警指标")
	private String alarmIndex;
	/**
	 * 报警值
	 */
	@ApiModelProperty(value = "报警值")
	private BigDecimal val;
	/**
	 * 报警值单位
	 */
	@ApiModelProperty(value = "报警值单位")
	private String unit;
	/**
	 * 报警时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "报警时间")
	private Date alarmTime;

}
