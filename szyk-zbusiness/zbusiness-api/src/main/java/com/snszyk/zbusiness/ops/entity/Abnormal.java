/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备异常信息表实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
@TableName("eolm_abnormal")
@ApiModel(value = "Abnormal对象", description = "设备异常信息表")
public class Abnormal implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;
	/**
	 * 设备ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备ID")
	private Long equipmentId;
	/**
	 * 异常等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;
	/**
	 * 异常原因
	 */
	@ApiModelProperty(value = "异常原因")
	private String abnormalReason;
	/**
	 * 诊断结论（针对机理模型）
	 */
	@ApiModelProperty(value = "诊断结论（针对机理模型）")
	private String conclusion;
	/**
	 * 检维修建议（针对机理模型）
	 */
	@ApiModelProperty(value = "检维修建议（针对机理模型）")
	private String suggestion;
	/**
	 * 首次异常时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "首次异常时间")
	private Date firstTime;
	/**
	 * 最新异常时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "最新异常时间")
	private Date lastTime;
	/**
	 * 是否生成故障
	 */
	@ApiModelProperty(value = "是否生成故障")
	private Integer isFault;
	/**
	 * 故障id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "故障id")
	private Long faultId;
	/**
	 * 手动关闭异常原因
	 */
	@ApiModelProperty(value = "手动关闭异常原因")
	private String closeReason;
	/**
	 * 关闭时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "关闭时间")
	private Date closeTime;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

}
