package com.snszyk.zbusiness.ai.dto;

import lombok.Data;

import java.util.List;

@Data
public class AiMonitorDto {
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 父主键
	 */
	private Long equipmentId;
	/**
	 * 编码
	 */
	private String code;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 照片
	 */
	private Long image;
	/**
	 * 测量方向（字典：sensor_measure_direction）
	 */
	private Integer measureDirection;
	/**
	 * 全路径
	 */
	private String path;
	/**
	 * 全路径名称
	 */
	private String pathName;
	/**
	 * 设备归属类型（字典：equipment_type）
	 */
	private Integer equipmentType;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 测点波形列表
	 */
	private List<AiWaveDto> waveList;

}
