package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备异常参数配置
 *
 * <AUTHOR>
 * @date 2024-05-21 20:07
 */
@Data
@ApiModel(value = "AbnormalParamVO", description = "AbnormalParamVO")
public class AbnormalParamVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 最大权重
	 */
	@ApiModelProperty(value = "最大权重")
	private Integer maxWeight;
	/**
	 * 连续策略-超限累加权重
	 */
	@ApiModelProperty(value = "连续策略-超限累加权重")
	private Integer accumulatedWeight;
	/**
	 * 连续策略-连续超限次数
	 */
	@ApiModelProperty(value = "连续策略-连续超限次数")
	private Integer continuousTimes;
	/**
	 * 非连续策略-统计天数
	 */
	@ApiModelProperty(value = "非连续策略-统计天数")
	private Integer statisticsDays;
	/**
	 * 非连续策略-报警次数
	 */
	@ApiModelProperty(value = "非连续策略-报警次数")
	private Integer alarmTimes;
	/**
	 * 是否启用
	 */
	@ApiModelProperty(value = "是否启用（0：停用，1：启用）")
	private Integer isEnabled;

}
