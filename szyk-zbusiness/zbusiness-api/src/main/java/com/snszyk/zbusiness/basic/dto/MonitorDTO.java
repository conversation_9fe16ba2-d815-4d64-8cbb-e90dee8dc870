/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.resource.entity.Attach;
import com.snszyk.zbusiness.basic.entity.Monitor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备测点表实体类
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MonitorDTO对象", description = "设备测点表")
public class MonitorDTO extends Monitor {

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> imageList;

	/**
	 * 传感器信息
	 */
	@ApiModelProperty(value = "传感器信息")
	private List<SensorInstanceDTO> sensorInfos;

	/**
	 * 绑定传感器数量
	 */
	@ApiModelProperty(value = "绑定传感器数量")
	private Integer sensorNumber;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备分类名称
	 */
	@ApiModelProperty(value = "设备分类名称")
	private String equipmentCategory;

	/**
	 * 3D场景id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "3D场景id")
	private Long sceneId;

	/**
	 * 专家诊断图片列表
	 */
	@ApiModelProperty(value = "专家诊断图片列表")
	private List<Attach> diagnosticImageList;

	/**
	 * 是否可编辑
	 */
	@ApiModelProperty(value = "是否可编辑")
	private Boolean canEdit;

	/**
	 * 位置信息
	 */
	@ApiModelProperty(value = "位置信息")
	private DeviceDTO deviceInfo;

	/**
	 * 设备信息
	 */
	@ApiModelProperty(value = "设备信息")
	private EquipmentDTO equipmentInfo;

	/**
	 * 测量方向名称
	 */
	@ApiModelProperty(value = "测量方向名称")
	private String measureDirectionName;

	/**
	 * 设备归属类型名称
	 */
	@ApiModelProperty(value = "设备归属类型名称")
	private String equipmentTypeName;

	/**
	 * 是否应用模型
	 */
	@ApiModelProperty(value = "是否应用模型")
	private Boolean isModelApply;

	/**
	 * 模型是否可用
	 */
	@ApiModelProperty(value = "模型是否可用")
	private Boolean isModelEnabled;

	public MonitorDTO(){

	}

	public MonitorDTO(Long id){
		this.setId(id);
	}

}
