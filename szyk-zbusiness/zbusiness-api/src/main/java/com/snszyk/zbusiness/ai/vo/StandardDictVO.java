/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ai.vo;

import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ai.entity.StandardDict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * ISO10816标准字典表 视图实体类
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StandardDictVO对象", description = "ISO10816标准字典表")
public class StandardDictVO extends StandardDict {

	private static final long serialVersionUID = 1L;

	/**
	 * 采样数据类型名称
	 */
	@ApiModelProperty(value = "采样数据类型名称")
	private String sampleDataTypeName;
	/**
	 * 应用设备类型名称
	 */
	@ApiModelProperty(value = "应用设备类型名称")
	private String applyEquipmentName;
	/**
	 * 指标名称
	 */
	@ApiModelProperty(value = "指标名称")
	private String quotaName;
	/**
	 * 报警类型名称
	 */
	@ApiModelProperty(value = "报警类型名称")
	private String alarmTypeName;
	/**
	 * 波形id列表
	 */
	@ApiModelProperty(value = "波形id列表")
	private List<Long> waveIds;
	/**
	 * 振动类型（0：非振动，1：振动）
	 */
	@ApiModelProperty(value = "振动类型（0：非振动，1：振动）")
	private Integer vibrationType;
	/**
	 * 部位id
	 */
	@ApiModelProperty(value = "部位id")
	private List<Long> monitorIds;
	/**
	 * 设备类型名称
	 */
	@ApiModelProperty(value = "设备类型名称")
	private String deviceCategoryName;

	@Override
	public BigDecimal getFirstThresholdLower() {
		return convert(super.getFirstThresholdLower());
	}

	@Override
	public BigDecimal getFirstThresholdUpper() {
		return convert(super.getFirstThresholdUpper());
	}

	@Override
	public BigDecimal getSecondThresholdLower() {
		return convert(super.getSecondThresholdLower());
	}

	@Override
	public BigDecimal getSecondThresholdUpper() {
		return convert(super.getSecondThresholdUpper());
	}

	@Override
	public BigDecimal getThirdThresholdLower() {
		return convert(super.getThirdThresholdLower());
	}

	@Override
	public BigDecimal getThirdThresholdUpper() {
		return convert(super.getThirdThresholdUpper());
	}

	@Override
	public BigDecimal getFourthThresholdLower() {
		return convert(super.getFourthThresholdLower());
	}

	@Override
	public BigDecimal getFourthThresholdUpper() {
		return convert(super.getFourthThresholdUpper());
	}

	private BigDecimal convert(BigDecimal a) {
		if (Func.isNotEmpty(a)) {
			String b = a.stripTrailingZeros().toPlainString();
			return new BigDecimal(b);
		}
		return null;
	}

}
