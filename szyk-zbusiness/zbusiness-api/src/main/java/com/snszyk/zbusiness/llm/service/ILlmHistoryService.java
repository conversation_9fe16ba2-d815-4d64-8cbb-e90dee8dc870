/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.llm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.llm.dto.LlmHistoryDto;
import com.snszyk.zbusiness.llm.entity.LlmHistory;
import com.snszyk.zbusiness.llm.vo.LlmHistoryPageVo;

/**
 * 智能问题的历史记录 服务类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ILlmHistoryService extends BaseService<LlmHistory> {



    /**
    * 分页查询
    */
    IPage<LlmHistoryDto> pageList(LlmHistoryPageVo v);

    /**
    * 详情
    */
    LlmHistoryDto detail(Long id);

}
