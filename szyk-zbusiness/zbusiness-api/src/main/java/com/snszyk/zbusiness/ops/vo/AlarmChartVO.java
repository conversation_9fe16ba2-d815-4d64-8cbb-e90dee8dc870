/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.snszyk.zbusiness.ops.entity.AlarmChart;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报警管理图谱表视图实体类
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmChartVO对象", description = "报警管理图谱表")
public class AlarmChartVO extends AlarmChart {
	private static final long serialVersionUID = 1L;

}
