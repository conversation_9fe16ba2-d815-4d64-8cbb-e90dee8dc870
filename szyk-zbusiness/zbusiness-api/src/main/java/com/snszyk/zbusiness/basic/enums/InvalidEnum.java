/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 传感器原始数据是否异常枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InvalidEnum {

	/**
	 * 正常数据
	 */
	VALID(0, "正常数据"),

	/**
	 * 振动类型数据
	 */
	INVALID(1, "异常数据");

	final Integer code;
	final String name;

	public static InvalidEnum getByCode(Integer code){
		for (InvalidEnum value : InvalidEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
