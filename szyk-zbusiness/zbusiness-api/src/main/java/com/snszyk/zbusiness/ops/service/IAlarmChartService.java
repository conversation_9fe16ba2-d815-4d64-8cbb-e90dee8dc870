/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.ops.entity.AlarmChart;
import com.snszyk.zbusiness.ops.vo.AlarmChartVO;

import java.util.List;

/**
 * 报警管理图谱表 服务类
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface IAlarmChartService extends IService<AlarmChart> {

	/**
	 * 诊断报告图
	 *
	 * @param recordIds
	 * @return
	 */
	List<AlarmChartVO> chartByAlarmRecord(String recordIds);

}
