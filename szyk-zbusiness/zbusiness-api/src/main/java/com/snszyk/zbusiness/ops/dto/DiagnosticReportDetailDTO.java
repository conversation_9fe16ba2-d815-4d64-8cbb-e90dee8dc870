/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.snszyk.zbusiness.ops.entity.DiagnosticReportDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 诊断报告明细表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DiagnosticReportDetailDTO extends DiagnosticReportDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 测点名称
	 */
	@ApiModelProperty(value = "测点名称")
	private String monitorName;

	/**
	 * 报警类型名称
	 */
	@ApiModelProperty(value = "报警类型名称")
	private String alarmTypeName;

	/**
	 * 报警等级名称
	 */
	@ApiModelProperty(value = "报警等级名称")
	private String alarmLevelName;

	/**
	 * 报警指标名称
	 */
	@ApiModelProperty(value = "报警指标名称")
	private String alarmIndexName;

}
