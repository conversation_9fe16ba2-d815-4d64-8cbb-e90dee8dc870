/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告发布状态枚举类
 * 对应字典编码：report_publish_status
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Getter
@AllArgsConstructor
public enum PublishStatusEnum {

    /**
     * 未发布
     */
    UNPUBLISHED(0, "未发布"),
    
    /**
     * 已发布
     */
    PUBLISHED(1, "已发布");

    /**
     * 编码
     */
    final Integer code;
    
    /**
     * 名称
     */
    final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举实例
     */
    public static PublishStatusEnum getByCode(Integer code) {
        if (code == null) {
            return UNPUBLISHED;
        }
        for (PublishStatusEnum value : PublishStatusEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return UNPUBLISHED;
    }
}
