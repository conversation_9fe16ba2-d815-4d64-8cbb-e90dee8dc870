/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
import com.snszyk.zbusiness.ops.entity.DiagnosticReport;
import com.snszyk.zbusiness.ops.vo.DiagnosticReportVO;

/**
 * 诊断报告表 服务类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface IDiagnosticReportService extends BaseService<DiagnosticReport> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param diagnosticReport
	 * @return
	 */
	IPage<DiagnosticReportDTO> page(IPage<DiagnosticReportDTO> page, DiagnosticReportVO diagnosticReport);

	DiagnosticReportDTO getByEquipmentId(Long equipmentId);
}
