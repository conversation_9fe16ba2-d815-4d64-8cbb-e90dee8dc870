/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备统计数据
 *
 * <AUTHOR>
 * @since 2023-07-06
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentStatisticsDTO对象", description = "EquipmentStatisticsDTO对象")
public class EquipmentStatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "健康指数")
	private HealthIndex healthIndex;

	@ApiModelProperty(value = "健康状态")
	private Integer healthStatus;

	@ApiModelProperty(value = "运行状态（0：停机，1：运行）")
	private Integer runningState;

	@ApiModelProperty(value = "通讯状态")
	private Integer communicationStatus;

	@ApiModelProperty(value = "实时报警")
	private Integer alarmCount;

	@ApiModelProperty(value = "报警总个数")
	private Integer AlarmSum;

	@ApiModelProperty(value = "振动模型")
	private Integer modelCount;

	@ApiModelProperty(value = "合计运行总时长")
	private BigDecimal runningTimeSum;

	@ApiModelProperty(value = "本次运行总时长")
	private BigDecimal runningTimeCurrent;

	@ApiModelProperty(value = "运行效率")
	private BigDecimal runningRate;

	@ApiModelProperty(value = "缺陷停机率")
	private BigDecimal downRate;



}
