/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.file.dto.FileShareDto;
import com.snszyk.zbusiness.file.entity.FileShare;
import com.snszyk.zbusiness.file.vo.FileSharePageVo;

/**
 * 文件分享 服务类
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
public interface IFileShareService extends BaseService<FileShare> {



    /**
    * 分页查询
    */
    IPage<FileShareDto> pageList(FileSharePageVo v);

    /**
    * 详情
    */
    FileShareDto detail(Long id);

	Integer noReadCount(Long userId);

	Boolean read(Long shareId);
}
