/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.entity.WaveMark;
import com.snszyk.zbusiness.basic.vo.ParamBearingVO;
import com.snszyk.zbusiness.basic.vo.ParamGearVO;
import com.snszyk.zbusiness.basic.vo.ParamRpmVO;
import com.snszyk.zbusiness.basic.vo.WaveMarkVO;

import java.util.List;

/**
 * 波形标注表 服务类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface IWaveMarkService extends IService<WaveMark> {

	/**
	 * 提交
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(WaveMarkVO vo);

	/**
	 * 详情
	 *
	 * @param vo
	 * @return
	 */
	WaveMarkVO detail(WaveMarkVO vo);

	/**
	 * 获取波形配置的转频
	 *
	 * @param waveId 波形id
	 * @return
	 */
	double getRotatingFreqByWave(Long waveId);

	/**
	 * 选择轴承型号
	 *
	 * @param waveId
	 * @return
	 */
	List<ParamBearingVO> bearingSelect(Long waveId);

	/**
	 * 选择转速
	 *
	 * @param waveId
	 * @param originTime
	 * @return
	 */
	List<ParamRpmVO> rpmSelect(Long waveId, String originTime);

	/**
	 * 选择齿数
	 *
	 * @param waveId
	 * @return
	 */
	List<ParamGearVO> gearTeethSelect(Long waveId);

}
