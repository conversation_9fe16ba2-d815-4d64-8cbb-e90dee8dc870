/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.EquipmentDTO;
import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.ops.entity.AlarmDetail;
import com.snszyk.zbusiness.ops.enums.AlarmBizTypeEnum;
import com.snszyk.zbusiness.ops.enums.AlarmLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 报警管理明细表视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmDetailVO对象", description = "报警管理明细表")
public class AlarmDetailVO extends AlarmDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String devicePath;

	/**
	 * 设备路径名称
	 */
	@ApiModelProperty(value = "设备路径名称")
	private String pathName;

	/**
	 * 传感器采样数据id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("传感器采样数据id")
	private Long sensorDataId;

	/**
	 * 创建人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("创建人")
	private Long createUser;

	/**
	 * 创建部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("创建部门")
	private Long createDept;

	/**
	 * 更新人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("更新人")
	private Long updateUser;

	/**
	 * 报警指标名称
	 */
	@ApiModelProperty("报警指标名称")
	private String alarmIndexName;

	/**
	 * 报警等级名称
	 */
	@ApiModelProperty("报警等级名称")
	private String alarmLevelName;

	/**
	 * 采样数据类型
	 */
	@ApiModelProperty("采样数据类型")
	private String sampleDataType;

	/**
	 * 测量方向
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测量方向")
	private Integer measureDirection;

	/**
	 * 应力波编号
	 */
	@ApiModelProperty(value = "应力波编号")
	private String stressNumber;

	public AlarmDetailVO() {

	}

	/**
	 * 构造函数
	 *
	 * @param sensorDataId
	 * @param waveId
	 * @param val
	 */
	public AlarmDetailVO(Long sensorDataId, Long waveId, BigDecimal val) {
		if (Func.isNotEmpty(sensorDataId)) {
			this.setSensorDataId(sensorDataId);
		}
		if (Func.isNotEmpty(waveId)) {
			this.setWaveId(waveId);
		}
		this.setVal(val);
	}

	/**
	 * 构造函数
	 *
	 * @param alarmType
	 * @param waveId
	 * @param alarmLevel
	 */
	public AlarmDetailVO(AlarmBizTypeEnum alarmType, Long waveId, AlarmLevelEnum alarmLevel) {
		this.setAlarmType(alarmType.getCode());
		this.setWaveId(waveId);
		this.setAlarmLevel(alarmLevel.getCode());
	}

	/**
	 * 构建VO
	 *
	 * @param monitor
	 * @param alarmType
	 * @param alarmLevel
	 * @return
	 */
	public AlarmDetailVO toAlarmDetailVO(MonitorDTO monitor, AlarmBizTypeEnum alarmType, Integer alarmLevel, Date originTime) {
		AlarmDetailVO vo = new AlarmDetailVO();
		EquipmentDTO equipment = monitor.getEquipmentInfo();
		vo.setTenantId(equipment.getTenantId())
			.setDevicePath(equipment.getPath())
			.setEquipmentName(equipment.getName())
			.setPathName(equipment.getPathName())
			.setCreateUser(equipment.getCreateUser())
			.setCreateDept(equipment.getCreateDept())
			.setUpdateUser(equipment.getUpdateUser())
			.setEquipmentId(equipment.getId())
			.setMonitorId(monitor.getId())
			.setMonitorName(monitor.getName())
			.setMonitorPath(monitor.getPath())
			.setFirstAlarmTime(originTime)
			.setLastAlarmTime(originTime)
			.setAlarmDataTime(originTime)
			.setAlarmType(alarmType.getCode())
			.setAlarmLevel(alarmLevel);
		return vo;
	}

}
