/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.SensorType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 传感器类型表 视图实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SensorTypeVO对象", description = "传感器类型表")
public class SensorTypeVO extends SensorType {

	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String nameOrModel;

	/**
	 * 传感器参数列表
	 */
	@ApiModelProperty(value = "传感器参数列表")
	private List<SensorTypeParamVO> sensorTypeParamList;

}
