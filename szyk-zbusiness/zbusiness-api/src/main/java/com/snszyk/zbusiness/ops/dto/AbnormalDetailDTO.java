/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;


import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.ops.entity.AbnormalDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备异常详情表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AbnormalDetailDTO extends AbnormalDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位信息
	 */
	@ApiModelProperty(value = "部位信息")
	private MonitorDTO monitorInfo;

	/**
	 * 波形异常列表
	 * 使用@ApiModelProperty(hidden = true)注解避免Swagger文档循环引用
	 */
	@ApiModelProperty(hidden = true)
	private List<AbnormalDetailDTO> waveAbnormalList;

	/**
	 * 异常明细列表
	 */
	@ApiModelProperty(value = "异常明细列表")
	private List<AbnormalRecordDTO> abnormalRecordList;

	/**
	 * 波形异常等级
	 */
	@ApiModelProperty(value = "波形异常等级")
	private String abnormalLevelName;

	public AbnormalDetailDTO(){
		super();
	}

	public AbnormalDetailDTO(Long monitorId) {
		super();
		this.setMonitorId(monitorId);
	}

}
