/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单位枚举类
 *
 * <AUTHOR>
 * @date 2023/03/08 16:37
 **/
@Getter
@AllArgsConstructor
public enum EolmUnitEnum {

	/**
	 * 速度
	 */
	VELOCITY(0, "mm/s"),

	/**
	 * 加速度
	 */
	ACCELERATION(1, "m/s^2"),

	/**
	 * 温度
	 */
	TEMPERATURE(2, "℃"),

	/**
	 * 电量
	 */
	POWER(3, "%"),

	;

	final Integer code;
	final String name;

	public static EolmUnitEnum getByCode(Integer code) {
		for (EolmUnitEnum value : EolmUnitEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return null;
	}

}
