/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 诊断报告明细表实体类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Accessors(chain = true)
@TableName("eolm_diagnostic_report_detail")
@ApiModel(value = "DiagnosticReportDetail对象", description = "诊断报告明细表")
public class DiagnosticReportDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 报告id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报告id")
	private Long reportId;
	/**
	 * 报警时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "报警时间")
	private Date alarmTime;
	/**
	 * 报警点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警点id")
	private Long monitorId;
	/**
	 * 报警类型（字典：alarm_biz_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警类型（字典：alarm_biz_type）")
	private Integer alarmType;
	/**
	 * 报警等级（字典：alarm_level）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警等级（字典：alarm_level）")
	private Integer alarmLevel;
	/**
	 * 报警指标
	 */
	@ApiModelProperty(value = "报警指标")
	private String alarmIndex;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
