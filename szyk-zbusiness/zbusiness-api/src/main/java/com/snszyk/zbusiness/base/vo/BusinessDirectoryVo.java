/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.base.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务目录实体类
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessDirectoryVo对象", description = "业务目录")
public class BusinessDirectoryVo extends BaseCrudVo {

	/**
	 * 类型：业务/非业务,1-业务；0-非业务
	 */
	@ApiModelProperty(value = "类型：业务/非业务,1-业务；0-非业务")
	private Boolean type;
	/**
	 * 父级id
	 */
	@ApiModelProperty(value = "父级id")
	private Long parentId;
	/**
	 * 祖级id
	 */
	@ApiModelProperty(value = "祖级id")
	private String ancestors;
	/**
	 * 分组名称
	 */
	@ApiModelProperty(value = "分组名称")
	private String groupName;
	/**
	 * 档案分类id
	 */
	@ApiModelProperty(value = "档案分类id")
	private Long classificationId;
	/**
	 * 组织id
	 */
	@ApiModelProperty(value = "组织id")
	private Long orgId;
	/**
	 * 是否必须归档
	 */
	@ApiModelProperty(value = "是否必须归档")
	private Boolean archiveRequire;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;


}
