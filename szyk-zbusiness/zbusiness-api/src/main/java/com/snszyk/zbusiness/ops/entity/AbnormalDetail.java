/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备异常详情表实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
@TableName("eolm_abnormal_detail")
@ApiModel(value = "AbnormalDetail对象", description = "设备异常详情表")
public class AbnormalDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 异常ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常ID")
	private Long abnormalId;
	/**
	 * 部位ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位ID")
	private Long monitorId;
	/**
	 * 波形ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形ID")
	private Long waveId;
	/**
	 * 波形名称
	 */
	@ApiModelProperty(value = "波形名称")
	private String waveName;
	/**
	 * 异常等级（字典：alarm_level）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级（字典：alarm_level）")
	private Integer abnormalLevel;
	/**
	 * 异常时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "异常时间")
	private Date abnormalTime;
	/**
	 * 状态（0：未处理，1：已成故障，2：已关闭）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "状态（0：未处理，1：已成故障，2：已关闭）")
	private Integer status;
	/**
	 * 关闭时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "关闭时间")
	private Date closeTime;

}
