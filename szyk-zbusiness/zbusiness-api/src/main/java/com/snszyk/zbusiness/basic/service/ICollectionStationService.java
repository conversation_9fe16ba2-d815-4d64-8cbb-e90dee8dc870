/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.basic.dto.CollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 采集站表 服务类
 *
 * <AUTHOR>
 */
public interface ICollectionStationService extends BaseService<CollectionStation> {


	/**
	 * 分页查询采集站
	 *
	 * @param page 分页参数
	 * @param vo   查询条件
	 * @return
	 */
	IPage<CollectionStationDTO> selectStationPage(IPage<CollectionStationDTO> page, CollectionStationVO vo);

	/**
	 * 统计采集站在线、离线数
	 *
	 * @param tenantId 租户
	 * @return
	 */
	CollectionStationOnlineStatDTO onlineStat(String tenantId);

	/**
	 * 导出Excel
	 *
	 * @param collectionStation
	 * @param response
	 */
	void exportExcel(CollectionStationVO collectionStation, HttpServletResponse response);

	List<CollectionStationDTO> listByTenantIds(List<String> tenantIds);
}
