/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 文件统计实体类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StatDto对象", description = "文件统计")
public class StatDto extends BaseCrudDto {

	/**
	 * 浏览次数
	 */
		@ApiModelProperty(value = "浏览次数")
		private Long viewNum;
	/**
	 * 收藏次数
	 */
		@ApiModelProperty(value = "收藏次数")
		private Long favoriteNum;
	/**
	 * 点赞次数
	 */
		@ApiModelProperty(value = "点赞次数")
		private Long starNum;
	/**
	 * 合计
	 */
		@ApiModelProperty(value = "合计")
		private Long totalNum;


}
