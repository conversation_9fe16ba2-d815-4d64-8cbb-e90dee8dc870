/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.archive.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.system.excel.ExcelDate;
import com.snszyk.system.excel.ExcelDict;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDate;

/**
 * 档案表实体类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@ApiModel(value = "ArchiveVo对象", description = "档案表")
public class ArchiveImportVo {

	/**
	 * 业务id
	 */
	@ExcelIgnore
	private Long businessId;
	/**
	 * 目录分组id
	 */
	@ExcelIgnore
	private Long groupId;

	/**
	 * 关联业务模板id
	 */
	@ExcelIgnore
	private Long directoryTemplate;


	@ExcelProperty(value = "*机构代码(问题)")
	@ExcelDict(require = true,dict = DictBizEnum.OQ_DM)
	private String oqDmName;

	@ExcelIgnore
	private String oqDm;

	/**
	 * 档案门类,数据字典
	 */
	@ExcelIgnore
	private String archiveType;

	@ExcelDict(require = true, dict = DictBizEnum.ARCHIVE_TYPE)
	@ExcelProperty(value = "*档案门类")
	private String archiveTypeName;
	/**
	 * 责任者,数据字典
	 */
	@ExcelIgnore
	private String responsiblePerson;
	/**
	 * 责任者,数据字典
	 */
	@ExcelProperty(value = "责任者")
	@ExcelDict(require = false, dict = DictBizEnum.RESPONSIBLE_PERSON)
	private String responsiblePersonName;
	/**
	 * 文号
	 */
	@ExcelProperty(value = "文号")
	private String docNo;
	/**
	 * 题名
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*题名")
	private String title;
	/**
	 * 详细说明
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*详细说明")
	private String fullExplanation;
	/**
	 * 保管期限，业务字典
	 */
	@ExcelIgnore
	private String retentionPeriod;

	@ExcelDict(require = true, dict = DictBizEnum.RETENTION_PERIOD)
	@ExcelProperty(value = "*保管期限")
	private String retentionPeriodName;
	/**
	 * 密级，业务字典
	 */
	@ExcelIgnore
	private String secrecyLevel;

	@ExcelDict(require = true, dict = DictBizEnum.SECRECY_LEVEL)
	@ExcelProperty(value = "*密级")
	private String secrecyLevelName;
	/**
	 * 公开级别，业务字典
	 */
	@ExcelIgnore
	private String publicLevel;

	@ExcelDict(require = true, dict = DictBizEnum.PUBLIC_LEVEL)
	@ExcelProperty(value = "*公开级别")
	private String publicLevelName;
	/**
	 * 整理人id
	 */
	@ExcelIgnore
	private Long buildPersonId;


	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*整理人")
	private String buildPersonName;
	/**
	 * 整理日期
	 */

	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*整理日期")
	@ExcelDate
	private String buildDateStr;
	@ExcelIgnore
	private LocalDate buildDate;
	/**
	 * 检查人id
	 */
	@ExcelIgnore
	private Long inspectorId;

	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*检查人")
	private String inspectorName;

	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*归档日期")
	@ExcelDate
	private String archivalDateStr;
	@ExcelIgnore
	private LocalDate archivalDate;
	/**
	 * 归档部门
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*归档部门")
	private String archivalOrgName;

	@ExcelIgnore
	private Long archivalOrgId;


	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*起止日期-开始")
	@ExcelDate
	private String spanStartStr;

	@ExcelIgnore
	private LocalDate spanStart;
	/**
	 * 起止时间结束
	 */

	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*起止日期-结束")
	@ExcelDate
	private String spanEndStr;
	@ExcelIgnore
	private LocalDate spanEnd;
	/**
	 * 年度
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*年度")
	private Integer yearData;
	/**
	 * 页号
	 */
	@ExcelProperty(value = "页号")
	private String pageNo;
	/**
	 * 页数
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*页数")
	private Integer pageNumbers;
	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注")
	private String remark;
	@ColumnWidth(value =15)

	@ExcelDict(require=true)
	@ExcelProperty(value = "*文件份数")
	private Integer fileNumbers;
	/**
	 * 实体份数
	 */
	@ExcelDict(require = true, dict = DictBizEnum.NONE)
	@ExcelProperty(value = "*实体份数")
	private Integer physicalNumbers;




	@ExcelProperty(value = "错误提示")
	private String message;

	@ExcelIgnore
	private Long orgId;

	/**
	 * 原文数量
	 */
	@ExcelIgnore
	private Integer originalNumbers=0;
}
