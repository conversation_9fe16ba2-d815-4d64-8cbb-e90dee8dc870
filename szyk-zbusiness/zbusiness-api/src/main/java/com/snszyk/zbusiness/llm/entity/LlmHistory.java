/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.llm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能问题的历史记录 实体类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@TableName("k_llm_history")
@EqualsAndHashCode(callSuper = true)
public class LlmHistory extends TenantEntity {

	/**
	 * 1用户问题 2系统回答
	 */
	private String msgType;
	/**
	 * 信息
	 */
	private String msg;
	/**
	 * 1默认知识库 2设备诊断库
	 */
	private String knowledgeBaseType;

	private String source;

	private String graphSource;


}
