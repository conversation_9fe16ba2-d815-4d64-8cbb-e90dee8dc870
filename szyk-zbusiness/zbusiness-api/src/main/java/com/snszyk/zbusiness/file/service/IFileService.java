/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.file.dto.FileDto;
import com.snszyk.zbusiness.file.dto.FileTreeDto;
import com.snszyk.zbusiness.file.entity.KFile;
import com.snszyk.zbusiness.file.vo.DirectoryPageVo;
import com.snszyk.zbusiness.file.vo.FilePageVo;
import com.snszyk.zbusiness.file.vo.TenantStatVo;

import java.util.List;

/**
 * 文件 服务类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface IFileService extends BaseService<KFile> {

    /**
    * 名称校验
    */
    void checkName(Long id, String name);

    /**
    * 分页查询
    */
    IPage<FileDto> pageList(FilePageVo v);

    /**
    * 详情
    */
    FileDto detail(Long id);

	KFile getByIdIgnoreDel(Long id);

	List<FileTreeDto> listData(Long parentId, String tenantId, Boolean excludePlatform);

	List<FileDto> listEsDetailByIds(List<Long> fileIds);

	List<FileDto> listLast(int num);

	List<FileDto> listDetailByIds(List<Long> fileIds);

	KFile getByIdIgnoreTenant(Long fileId);

    IPage<FileDto> myUploads(DirectoryPageVo v);

	List<FileDto> latestFiles(String tenantId);

	List<TenantStatVo> tenantStat(String tenantName);

	List<KFile> listAll();
}
