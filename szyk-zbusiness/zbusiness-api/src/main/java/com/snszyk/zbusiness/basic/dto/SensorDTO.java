/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 绑定传感器数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-02-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorDto对象", description = "SensorDto对象")
public class SensorDTO implements Serializable {

	/**
	 * 传感器实例id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("传感器实例id")
	private Long instanceId;

	/**
	 * 传感器名称
	 */
	@ApiModelProperty(value = "传感器名称")
	private String sensorName;

	/**
	 * 传感器型号
	 */
	@ApiModelProperty(value = "传感器型号")
	private String sensorModel;

	/**
	 * 是否无线：0-有线；1-无线
	 */
	@ApiModelProperty(value = "是否无线：0-有线；1-无线")
	private String isWireless;

	/**
	 * 传感器类型（字典：sensor_shaft）
	 */
	@ApiModelProperty(value = "传感器类型（已废弃，见axisCount）")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer type;

	/**
	 * 轴数（0-非振动；1-单轴；3-三轴）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴数（0-非振动；1-单轴；3-三轴）")
	private Integer axisCount;

	/**
	 * 起始频率（字典：initial_freq）
	 */
	@ApiModelProperty(value = "起始频率（字典：initial_freq）")
	private BigDecimal initialFreq;

	/**
	 * 传感器库id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器库id（已废弃，见typeId）")
	private Long sensorId;

	/**
	 * 传感器库id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器类型id")
	private Long typeId;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;

	/**
	 * 传感器编码
	 */
	@ApiModelProperty(value = "传感器编码")
	private String sensorCode;

	/**
	 * 路径名称
	 */
	@ApiModelProperty(value = "路径名称")
	private String pathName;

	/**
	 * 采集点code
	 */
	@ApiModelProperty(value = "采集点code")
	private String iotCode;

	/**
	 * 传感器参数列表
	 */
	@ApiModelProperty(value = "传感器参数列表")
	private List<SensorInstanceParamDTO> sensorInstanceParamList;
}
