package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.dto.SampleTimeDTO;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.basic.vo.WaveFormVO;

import java.util.List;

/**
 * 传感器原始数据 服务接口
 * <AUTHOR>
 */
public interface ISensorDataService extends IService<SensorData> {

	/**
	 * 自定义分页查询
	 * @param page page
	 * @param vo vo
	 * @return
	 */
	IPage<SensorDataDTO> page(IPage<SensorDataDTO> page, SensorDataVO vo);


	/**
	 * 查询特征值数据
	 *
	 * @param sensorData
	 * @return
	 */
	List<SensorDataDTO> queryCharacterData(SensorDataVO sensorData);

	/**
	 * 分页查询采样时间
	 * @param page 分页对象
	 * @return 分页对象
	 */
	IPage<SampleTimeDTO> sampleTimePageByWave(IPage<SampleTimeDTO> page, SensorDataVO vo);

	/**
	 * 非振动特征值波形（温度、电量、电压等）
	 * @param monitorId 测点id
	 * @param waveId 波形id
	 * @param dataType 数据类型
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 分页对象
	 */
	List<SensorDataDTO> queryCommonCharacters(Long monitorId, Long waveId, String dataType, String startDate, String endDate);

	/**
	 * 查询波形的6中通用指标特征图谱
	 * @param waveForm 参数
	 * @return
	 */
	List<SensorDataDTO> specificChromatogram(WaveFormVO waveForm);

	List<SensorDataDTO> energyList(SensorDataVO vo);

	/**
	 * 特征频率标注查询转速
	 *
	 * @param waveList 转速波形
	 * @param originTime
	 * @return
	 */
	SensorDataDTO queryRealRev(List<Wave> waveList, String originTime);

}
