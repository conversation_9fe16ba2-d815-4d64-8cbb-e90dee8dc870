/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.base.service;


import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.base.dto.TemplateMetadataDto;
import com.snszyk.zbusiness.base.vo.TemplateMetadataVo;

import java.util.List;

/**
 * 模版著录项 服务类
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
public interface ITemplateMetadataService extends IBaseCrudService<TemplateMetadataDto, TemplateMetadataVo> {

	List<TemplateMetadataDto> listByMetadataId(Long metadataId);

	List<TemplateMetadataDto> listByTemplateId(Long templateId, Integer searchable, Integer listDisplay);

	Boolean saveBatchList(List<TemplateMetadataVo> templateMetadataVoList);

	Boolean updateOrder(Long id, Integer order);

	List<TemplateMetadataDto> listByTemplateIds(List<Long> templateIds);

	Boolean realDelByTemplateId(Long templateId, List<Long> metadataIdList);
}
