/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.DeviceCollectionStation;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 厂区采集站绑定 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceCollectionStationVO对象", description = "DeviceCollectionStationVO对象")
public class DeviceCollectionStationVO extends DeviceCollectionStation {

	private static final long serialVersionUID = 1L;

}
