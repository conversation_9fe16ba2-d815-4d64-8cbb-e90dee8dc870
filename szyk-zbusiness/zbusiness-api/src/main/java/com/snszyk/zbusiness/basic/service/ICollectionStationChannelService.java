/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 采集站通道表 服务类
 *
 * <AUTHOR>
 */
public interface ICollectionStationChannelService extends IService<CollectionStationChannel> {


	/**
	 * 查询采集站的通道列表
	 *
	 * @param stationId 采集站id
	 * @return
	 */
	List<CollectionStationChannelDTO> selectChannelList(Long stationId);


	List<CollectionStationChannelDTO> listBy(Collection<? extends Serializable> stationIdList);
}
