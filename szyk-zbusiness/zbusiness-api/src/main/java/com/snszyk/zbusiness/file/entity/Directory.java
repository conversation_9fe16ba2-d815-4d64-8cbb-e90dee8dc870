/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件目录 实体类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
@TableName("k_directory")
@EqualsAndHashCode(callSuper = true)
public class Directory extends TenantEntity {

	/**
	 * 名称
	 */
	private String name;
	/**
	 * 父级目录id
	 */
	private Long parentId;
	/**
	 * 祖籍列表
	 */
	private String ancestors;
	private Integer sort;

}
