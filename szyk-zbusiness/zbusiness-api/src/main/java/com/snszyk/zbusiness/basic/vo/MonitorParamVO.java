/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.MonitorParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备测点参数配置表视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MonitorParamVO对象", description = "设备测点参数配置表")
public class MonitorParamVO extends MonitorParam {
	private static final long serialVersionUID = 1L;

	/**
	 * 转速信息
	 */
	@ApiModelProperty(value = "转速信息")
	private List<ParamRpmVO> rpmInfoList;

	/**
	 * 叶片信息
	 */
	@ApiModelProperty(value = "叶片信息")
	private List<ParamBladeVO> bladeInfoList;

	/**
	 * 轴承id集合
	 */
	@ApiModelProperty(value = "轴承信息")
	private List<ParamBearingVO> bearingInfoList;

	/**
	 * 齿轮信息
	 */
	@ApiModelProperty(value = "齿轮信息")
	private List<ParamGearVO> gearInfoList;

}
