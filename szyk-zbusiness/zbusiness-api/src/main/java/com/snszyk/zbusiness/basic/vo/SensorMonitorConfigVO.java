/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 3D模型配置表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorMonitorConfigVO对象", description = "3D模型配置表")
public class SensorMonitorConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;
	/**
	 * 虚拟传感器id
	 */
	@ApiModelProperty(value = "虚拟传感器id")
	private String virtualSensorId;
	/**
	 * 传感器编码
	 */
	@ApiModelProperty(value = "传感器编码")
	private String sensorCode;
	/**
	 * 模型配置列表
	 */
	@ApiModelProperty(value = "模型配置列表")
	private List<DeviceModelConfigVO> deviceModelConfigList;

	public SensorMonitorConfigVO(){

	}

	public SensorMonitorConfigVO(Long monitorId){
		this.monitorId = monitorId;
	}

}
