package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TimeDomainWaveFormDTO {

	/**
	 * 采样频率（KHz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;

	@ApiModelProperty(value = "采样时间")
	private BigDecimal samplingTime;

	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;

	@ApiModelProperty(value = "波形数据列表")
	private List<BigDecimal> waveForm;
}
