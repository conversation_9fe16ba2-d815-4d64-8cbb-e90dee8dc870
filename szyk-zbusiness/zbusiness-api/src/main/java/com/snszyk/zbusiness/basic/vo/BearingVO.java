/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 轴承库视图实体类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@ApiModel(value = "BearingVO对象", description = "轴承库")
public class BearingVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * 制造厂商
	 */
	@ApiModelProperty(value = "制造厂商")
	private String manufacturer;
	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 滚子/滚柱数目(N)
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "滚子/滚柱数目(N)")
	private Integer rollerNumber;
	/**
	 * 外圈特征频率
	 */
	@ApiModelProperty(value = "外圈特征频率")
	private BigDecimal bpfo;
	/**
	 * 内圈特征频率
	 */
	@ApiModelProperty(value = "内圈特征频率")
	private BigDecimal bpfi;
	/**
	 * 保持架的特征频率
	 */
	@ApiModelProperty(value = "保持架的特征频率")
	private BigDecimal ftf;
	/**
	 * 滚动体的特征频率
	 */
	@ApiModelProperty(value = "滚动体的特征频率")
	private BigDecimal bsf;
	/**
	 * 滚子/滚柱直径(d)
	 */
	@ApiModelProperty(value = "滚子/滚柱直径(d)")
	private BigDecimal rollerDiameter;
	/**
	 * 节圆直径(D)
	 */
	@ApiModelProperty(value = "节圆直径(D)")
	private BigDecimal pitchDiameter;
	/**
	 * 接触角(α)
	 */
	@ApiModelProperty(value = "接触角(α)")
	private BigDecimal contactAngle;
	/**
	 * 内圆直径
	 */
	@ApiModelProperty(value = "内圆直径")
	private BigDecimal innerDiameter;
	/**
	 * 外圆直径
	 */
	@ApiModelProperty(value = "外圆直径")
	private BigDecimal outerDiameter;
	/**
	 * 宽度
	 */
	@ApiModelProperty(value = "宽度")
	private BigDecimal width;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

}
