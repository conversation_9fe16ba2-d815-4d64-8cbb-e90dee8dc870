/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.ops.entity.AlarmDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 报警管理明细表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AlarmDetailDTO extends AlarmDetail {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "报警等级名称")
	private String alarmLevelName;

	@ApiModelProperty(value = "报警类型名称")
	private String alarmTypeName;

	@ApiModelProperty(value = "报警指标名称")
	private String alarmIndexName;

	@ApiModelProperty(value = "波形id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long waveId;

	@ApiModelProperty(value = "采样频率")
	private Integer samplingFreq;

	@ApiModelProperty(value = "起始频率")
	private Integer initialFreq;

	@ApiModelProperty(value = "截止频率")
	private Integer cutoffFreq;

	@ApiModelProperty(value = "报警值")
	private String alarmValue;

}
