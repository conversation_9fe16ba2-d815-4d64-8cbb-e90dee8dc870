/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 历史任务参与者表实体类
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FlwHisTaskActorDto对象", description = "历史任务参与者表")
public class FlwHisTaskActorDto extends BaseCrudDto {

	/**
	 * 流程实例ID
	 */
	@ApiModelProperty(value = "流程实例ID")
	private Long instanceId;
	/**
	 * 任务ID
	 */
	@ApiModelProperty(value = "任务ID")
	private Long taskId;
	/**
	 * 参与者ID
	 */
	@ApiModelProperty(value = "参与者ID")
	private String actorId;
	/**
	 * 参与者名称
	 */
	@ApiModelProperty(value = "参与者名称")
	private String actorName;
	/**
	 * 参与者类型 0，用户 1，角色 2，部门
	 */
	@ApiModelProperty(value = "参与者类型 0，用户 1，角色 2，部门")
	private Integer actorType;
	/**
	 * 票签权重
	 */
	@ApiModelProperty(value = "票签权重")
	private Integer weight;

	@ApiModelProperty(value = "审批意见")
	private String approvalComment;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "审批时间")
	private LocalDateTime approvalTime;

	/**
	 * 任务状态 0，活动 1，跳转 2，完成 3，拒绝 4，撤销审批  5，超时 6，终止 7，驳回终止
	 */
	protected Integer taskState;

	private String taskName;

	private Long orgId;
}
