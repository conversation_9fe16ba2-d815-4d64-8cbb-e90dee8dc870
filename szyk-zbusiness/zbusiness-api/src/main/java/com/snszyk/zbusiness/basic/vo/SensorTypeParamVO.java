/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.SensorTypeParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 传感器类型参数表 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SensorTypeParamVO对象", description = "传感器类型参数表")
public class SensorTypeParamVO extends SensorTypeParam {

	private static final long serialVersionUID = 1L;

}
