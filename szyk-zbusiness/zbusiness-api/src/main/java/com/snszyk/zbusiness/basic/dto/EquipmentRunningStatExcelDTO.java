package com.snszyk.zbusiness.basic.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.zbusiness.basic.converter.RunningStatusConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 设备运行统计DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(25)
public class EquipmentRunningStatExcelDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 编码
	 */
	@ExcelProperty("设备编号")
	@ApiModelProperty(value = "设备编号")
	private String code;

	/**
	 * 名称
	 */
	@ExcelProperty("设备名称")
	@ApiModelProperty(value = "设备名称")
	private String name;

	/**
	 * 型号
	 */
	@ExcelProperty("规格型号")
	@ApiModelProperty(value = "规格型号")
	private String model;


	/**
	 * 路径名称
	 */
	@ExcelProperty("设备路径")
	@ApiModelProperty(value = "设备路径")
	private String pathName;


	/**
	 * 功率
	 */
	@ExcelProperty("设备功率")
	@ApiModelProperty(value = "设备功率")
	private BigDecimal power;


	/**
	 * 制造厂商
	 */
	@ExcelProperty("制造厂商")
	@ApiModelProperty(value = "制造厂商")
	private String manufacturer;


	/**
	 * 运行状态：0-停机；1-运行中。
	 */
	@ApiModelProperty(value = "运行状态：0-停机；1-运行中。")
	@ExcelProperty(value = "运行状态", converter = RunningStatusConverter.class)
	private Integer isRunning;

	/**
	 * 最新运行时间
	 */
	@ExcelIgnore
	@ApiModelProperty("最新运行时间")
	private Date originRuntime;

	/**
	 * 运行时长（小时）
	 */
	@ApiModelProperty(value = "总运行时间（h）")
	@ExcelProperty("总运行时间（h）")
	private BigDecimal runningTime;

	/**
	 * 停机时长（小时）
	 */
	@ApiModelProperty(value = "总运行时间（h）")
	@ExcelIgnore
	private BigDecimal shutdownTime;

	/**
	 * 运行率
	 */
	@ApiModelProperty(value = "运行率")
	@ExcelProperty("运行率")
	private BigDecimal runningPercentage;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@ExcelProperty("创建时间")
	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

}
