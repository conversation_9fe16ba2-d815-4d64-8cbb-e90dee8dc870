/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 文件分享实体类
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FileShareVo对象", description = "文件分享")
public class FileShareVo extends BaseCrudVo {

	/**
	 * 父级目录id
	 */
	@ApiModelProperty(value = "父级目录id")
	private Long sendUser;
	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private Long receiveUser;
	/**
	 * 文件id
	 */
	@ApiModelProperty(value = "文件id")
	private Long fileId;
	/**
	 * 文件名
	 */
	@ApiModelProperty(value = "文件名")
	private String fileName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
