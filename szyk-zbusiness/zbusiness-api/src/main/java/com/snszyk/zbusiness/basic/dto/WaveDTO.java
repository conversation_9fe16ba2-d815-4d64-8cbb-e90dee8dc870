package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.MeasureDirectionEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 传感器波形DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WaveDTO extends Wave implements Comparable<WaveDTO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 路径（用","分隔）
	 */
	@ApiModelProperty(value = "路径（用','分隔）")
	private String path;

	/**
	 * 路径名称
	 */
	@ApiModelProperty(value = "路径名称")
	private String pathName;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 应用设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用设备类型")
	private Integer equipmentType;

	/**
	 * 应用设备类型名称
	 */
	@ApiModelProperty(value = "应用设备类型名称")
	private String equipmentTypeName;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	/**
	 * 节点类型（0：位置，1：设备，2：部位，3：波形）
	 */
	@ApiModelProperty(value = "节点类型（0：位置，1：设备，2：部位，3：波形）")
	private Integer nodeCategory;

	/**
	 * 节点类型名称（0：位置，1：设备，2：部位，3：波形）
	 */
	@ApiModelProperty(value = "节点类型名称（0：位置，1：设备，2：部位，3：波形）")
	private String nodeCategoryName;

	/**
	 * 节点等级：波形默认为3
	 */
	@ApiModelProperty(value = "节点等级：波形默认为3")
	private Integer nodeLevel;

	/**
	 * 节点名称：取值为波形名称
	 */
	@ApiModelProperty(value = "节点名称：取值为波形名称")
	private String nodeName;

	/**
	 * 起始频率
	 */
	@ApiModelProperty(value = "起始频率")
	private BigDecimal initialFreq;

	/**
	 * 截止频率
	 */
	@ApiModelProperty(value = "截止频率")
	private BigDecimal cutoffFreq;

	/**
	 * 采样频率（KHz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;

	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;

	/**
	 * 测量时间
	 */
	@ApiModelProperty(value = "测量时间")
	private Double samplingTime;

	/**
	 * 振动类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer vibrationType;

	/**
	 * 单位
	 */
	@ApiModelProperty("单位")
	private Integer dataUnit;

	/**
	 * 单位名称
	 */
	@ApiModelProperty("单位名称")
	private String dataUnitName;

	/**
	 * 设备ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备ID")
	private Long equipmentId;

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;

	@Override
	public int compareTo(WaveDTO other) {
		// 0）是否解绑
		int unbindCompare = this.getUnbind().compareTo(other.getUnbind());
		if (unbindCompare != 0) {
			return unbindCompare;
		}

		// 1）类型排序：SampledDataTypeEnum中声明的顺序
		int typeCompare = SampledDataTypeEnum.getByCode(this.getSampleDataType())
			.compareTo(SampledDataTypeEnum.getByCode(other.getSampleDataType()));
		if (typeCompare != 0) {
			return typeCompare;
		}

		// 2-1）温振-方向排序：MeasureDirectionEnum中声明的顺序
		int directionCompare = MeasureDirectionEnum.getByCode(this.getMeasureDirection() == null ? 0 : this.getMeasureDirection())
			.compareTo(MeasureDirectionEnum.getByCode(other.getMeasureDirection() == null ? 0 : other.getMeasureDirection()));
		if (directionCompare != 0) {
			return directionCompare;
		}

		// 2-2）应力波-序号：自然排序
		if (Func.isNotEmpty(this.getNumber()) && Func.isNotEmpty(other.getNumber())) {
			int numberCompare = this.getNumber().compareTo(other.getNumber());
			if (numberCompare != 0) {
				return numberCompare;
			}
		}

		// 2-3）电流-相位：自然排序
		if (Func.isNotEmpty(this.getPhase()) && Func.isNotEmpty(other.getPhase())) {
			int phaseCompare = this.getPhase().compareTo(other.getPhase());
			if (phaseCompare != 0) {
				return phaseCompare;
			}
		}

		// 3）采样频率从大到小排序
		return Integer.compare(other.getSamplingFreq() == null ? 0 : other.getSamplingFreq().intValue(),
			this.getSamplingFreq() == null ? 0 : this.getSamplingFreq().intValue());
	}
}
