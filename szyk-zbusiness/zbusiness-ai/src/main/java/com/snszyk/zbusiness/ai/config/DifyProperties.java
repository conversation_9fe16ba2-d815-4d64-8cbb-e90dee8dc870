package com.snszyk.zbusiness.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "dify")
public class DifyProperties {
    private String baseUrl;
    private Map<String,String> apiKey;
}
