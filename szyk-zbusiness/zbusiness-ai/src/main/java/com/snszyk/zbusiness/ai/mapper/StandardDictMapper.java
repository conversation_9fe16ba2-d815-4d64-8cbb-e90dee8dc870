/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.ai.entity.StandardDict;
import com.snszyk.zbusiness.ai.vo.StandardDictVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * ISO10816标准字典表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface StandardDictMapper extends BaseMapper<StandardDict> {

	/**
	 * 分页
	 *
	 * @param page
	 * @param standardDict
	 * @return
	 */
	List<StandardDict> page(IPage page, @Param("standardDict") StandardDictVO standardDict);

	/**
	 * 是否满足添加条件验证
	 *
	 * @param id
	 * @param tenantId
	 * @param deviceCategory
	 * @param powerLower
	 * @param powerUpper
	 * @param quotaType
	 * @return
	 */
	int validatePower(@Param("id") Long id, @Param("tenantId") String tenantId, @Param("deviceCategory") Integer deviceCategory, @Param("powerLower") BigDecimal powerLower, @Param("powerUpper") BigDecimal powerUpper, @Param("quotaType") String quotaType);
}
