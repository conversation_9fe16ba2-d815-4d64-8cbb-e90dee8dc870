<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.warehouse.mapper.CompactShelveColumnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="compactShelveColumnResultMap" type="com.snszyk.zbusiness.warehouse.entity.CompactShelveColumn">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="shelve_id" property="shelveId"/>
        <result column="column_no" property="columnNo"/>
        <result column="column_name" property="columnName"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectCompactShelveColumnPage" resultMap="compactShelveColumnResultMap">
        select * from arc_compact_shelve_column where is_deleted = 0
    </select>
    <select id="listData" resultType="com.snszyk.zbusiness.warehouse.dto.CompactShelveColumnDto">
        select t.* from arc_compact_shelve_column t
        left join arc_compact_shelve s on t.shelve_id = s.id and s.is_deleted = 0
        left join arc_warehouse w on s.warehouse_id = w.id and w.is_deleted = 0
        <where>
            <if test="v.columnName != null and v.columnName != ''">
                and t.column_name like concat('%',#{v.columnName},'%')
            </if>
            and w.general_id= #{v.generalId}
        </where>
    </select>

</mapper>
