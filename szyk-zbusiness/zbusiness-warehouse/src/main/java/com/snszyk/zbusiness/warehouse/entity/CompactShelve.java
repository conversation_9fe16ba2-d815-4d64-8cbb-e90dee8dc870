/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.warehouse.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.common.base.BaseCrudGeneralEntity;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 密集架实体类
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
@TableName("arc_compact_shelve")
@EqualsAndHashCode(callSuper = true)
public class CompactShelve extends BaseCrudGeneralEntity {

	/**
	 * 库房id
	 */
	private Long warehouseId;
	/**
	 * 密集架编号
	 */
	private String shelveNo;
	/**
	 * 密集架名称
	 */
	private String shelveName;
	/**
	 * 密集架类型，数据字典
	 */
	private String shelveType;
	/**
	 * 列数
	 */
	private Integer columnNumber;
	/**
	 * 每列宽度
	 */
	private Integer columnWidth;
	/**
	 * 节数
	 */
	private Integer sectionNumber;
	/**
	 * 节长度
	 */
	private Integer sectionWidth;
	/**
	 * 层数
	 */
	private Integer layerNumber;
	/**
	 * 层高度
	 */
	private Integer layerHeight;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;


}
