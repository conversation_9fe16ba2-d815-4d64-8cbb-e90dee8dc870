<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>szyk-zbusiness</artifactId>
        <groupId>com.snszyk</groupId>
        <version>2.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.snszyk</groupId>
    <artifactId>zbusiness-customer</artifactId>
    <version>2.0.0.RELEASE</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-common</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>system-rest</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>zbusiness-api</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>
        <!-- Szyk -->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-boot</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.snszyk</groupId>
                    <artifactId>szyk-core-cloud</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-crud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-swagger</artifactId>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <!--        <finalName>${project.name}</finalName>-->
        <resources>
            <!--            <resource>-->
            <!--                <directory>src/main/resources</directory>-->
            <!--            </resource>-->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
