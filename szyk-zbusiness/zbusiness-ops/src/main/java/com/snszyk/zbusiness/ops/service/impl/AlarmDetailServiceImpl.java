/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.basic.enums.AlarmIndexEnum;
import com.snszyk.zbusiness.ops.dto.AlarmDetailDTO;
import com.snszyk.zbusiness.ops.entity.AlarmDetail;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.enums.AlarmBizTypeEnum;
import com.snszyk.zbusiness.ops.enums.AlarmLevelEnum;
import com.snszyk.zbusiness.ops.mapper.AlarmDetailMapper;
import com.snszyk.zbusiness.ops.service.IAlarmDetailService;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 报警管理明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Service
@AllArgsConstructor
public class AlarmDetailServiceImpl extends ServiceImpl<AlarmDetailMapper, AlarmDetail> implements IAlarmDetailService {

	private final IAlarmRecordService alarmRecordService;

	@Override
	public AlarmDetailDTO queryAlarmDetail(Long monitorId, Long waveId, String alarmIndex) {
		return baseMapper.queryAlarmDetail(monitorId, waveId, alarmIndex);
	}

	@Override
	public List<AlarmDetailDTO> getAlarmDetailList(Long alarmId) {
		List<AlarmDetail> list = this.list(Wrappers.<AlarmDetail>query().lambda().eq(AlarmDetail::getAlarmId, alarmId));
		return list.stream().map(alarmDetail -> {
			AlarmDetailDTO dto = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmDetailDTO.class));
			dto.setAlarmLevelName(AlarmLevelEnum.getByCode(dto.getAlarmLevel()).getName())
				.setAlarmTypeName(AlarmBizTypeEnum.getByCode(dto.getAlarmType()).getName());
			return dto;
		}).collect(Collectors.toList());
	}

	@Override
	public List<AlarmDetailDTO> alarmDetailForDiagnosis(Long alarmId, String recordIds) {
		if (StringUtil.isBlank(recordIds) || Func.isEmpty(alarmId)) {
			return Collections.emptyList();
		}
		List<AlarmRecord> list = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
			.in(AlarmRecord::getId, Func.toLongList(recordIds))
			.orderByDesc(AlarmRecord::getAlarmTime));
		List<AlarmDetailDTO> alarmDetails = list.stream()
			.map(alarmRecord -> {
				AlarmDetail alarmDetail = this.getOne(Wrappers.<AlarmDetail>query().lambda().
					eq(AlarmDetail::getAlarmId, alarmId).eq(AlarmDetail::getMonitorId, alarmRecord.getMonitorId())
					.eq(AlarmDetail::getAlarmType, alarmRecord.getAlarmType())
					.eq(Func.isNotEmpty(alarmRecord.getAlarmIndex()), AlarmDetail::getAlarmIndex, alarmRecord.getAlarmIndex())
					.orderByDesc(AlarmDetail::getLastAlarmTime));
				AlarmDetailDTO dto = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmDetailDTO.class));
				dto.setAlarmTypeName(AlarmBizTypeEnum.getByCode(dto.getAlarmType()).getName())
					.setAlarmLevelName(AlarmLevelEnum.getByCode(dto.getAlarmLevel()).getName());
				if (Func.isNotEmpty(alarmRecord.getAlarmIndex())) {
					dto.setAlarmIndexName(AlarmIndexEnum.getByCode(dto.getAlarmIndex()).getName());
				}
				return dto;
			}).collect(Collectors.toList());
		return alarmDetails;
	}

	@Override
	public Integer countByMonitorId(Long monitorId) {
		return this.lambdaQuery()
			.eq(AlarmDetail::getMonitorId, monitorId)
			.count();
	}

	@Override
	public Integer countByEquipmentId(Long equipmentId) {
		return this.lambdaQuery()
			.eq(AlarmDetail::getEquipmentId, equipmentId)
			.count();
	}
}
