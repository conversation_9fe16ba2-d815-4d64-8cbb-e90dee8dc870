/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.entity.Alarm;
import com.snszyk.zbusiness.ops.vo.AlarmVO;

import java.util.Objects;

/**
 * 报警管理信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
public class AlarmWrapper extends BaseEntityWrapper<Alarm, AlarmVO> {

	public static AlarmWrapper build() {
		return new AlarmWrapper();
 	}

	@Override
	public AlarmVO entityVO(Alarm alarm) {
		AlarmVO alarmVO = Objects.requireNonNull(BeanUtil.copy(alarm, AlarmVO.class));

		//User createUser = UserCache.getUser(alarm.getCreateUser());
		//User updateUser = UserCache.getUser(alarm.getUpdateUser());
		//alarmVO.setCreateUserName(createUser.getName());
		//alarmVO.setUpdateUserName(updateUser.getName());

		return alarmVO;
	}

}
