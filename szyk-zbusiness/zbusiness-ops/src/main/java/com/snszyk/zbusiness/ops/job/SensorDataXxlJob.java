package com.snszyk.zbusiness.ops.job;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 传感器数据异常提醒定时任务
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SensorDataXxlJob {

	private static final Logger logger = LoggerFactory.getLogger(SensorDataXxlJob.class);

	/**
	 * 传感器数据超时提醒 定时任务
	 *
	 * @param param 参数：{"timeout_in_minute":120,"receiver_role_ids":"123456,123457"}
	 *              timeout_in_minute: 超时时长（分钟），如果某个位号最新一条数据的create_time已超过此时长，则报警；
	 *              receiver_role_ids：超时报警消息接收者角色ids，多个用英文逗号（','）分隔。
	 * @return result
	 */
	@XxlJob("sensorDataTimeoutJobHandler")
	public ReturnT<String> sensorDataTimeoutJobHandler(String param) {
		logger.info("传感器数据超时提醒 - 定时任务 - 【开始】");

		//1、参数解析
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}
		JSONObject jsonObject = JSONObject.parseObject(param);
		logger.info("任务参数为：{}", jsonObject);
		int timeoutInMinute = jsonObject.getIntValue("timeout_in_minute");
		String receiverRoleIds = jsonObject.getString("receiver_role_ids");
		Date timeoutTime = DateUtil.minusMinutes(new Date(), timeoutInMinute);

		logger.info("传感器数据超时提醒 - 定时任务 - 【结束】");
		return ReturnT.SUCCESS;
	}

	/**
	 * 传感器低电量提醒 定时任务
	 *
	 * @param param 参数：{"min_battery":2.8,"receiver_role_ids":"123456,123457"}
	 *              min_battery: 最低电量值，如果电量的值低于此值（根据不同厂家的传感器做响应调整），则报警
	 *              receiver_role_ids：超时报警消息接收者角色ids，多个用英文逗号（','）分隔。
	 * @return result
	 */
	@XxlJob("lowBatteryJobHandler")
	public ReturnT<String> lowBatteryJobHandler(String param) {
		logger.info("传感器低电量提醒 - 定时任务 - 【开始】");

		//1、参数解析
		if (StringUtil.isEmpty(param)) {
			return new ReturnT<>(500, "任务参数不能为空！");
		}
		JSONObject jsonObject = JSONObject.parseObject(param);
		logger.info("任务参数为：{}", jsonObject);
		double minBattery = jsonObject.getDoubleValue("min_battery");
		String receiverRoleIds = jsonObject.getString("receiver_role_ids");


		logger.info("传感器低电量提醒 - 定时任务 - 【结束】");
		return ReturnT.SUCCESS;
	}

}
