/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.logic;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.secure.utils.SecureUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.entity.MessageSetting;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageChannelEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.IMessageSettingService;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.User;
import com.snszyk.zbusiness.ops.dto.AiopsDiagnosisReportDto;
import com.snszyk.zbusiness.ops.entity.AiopsDiagnosisReport;
import com.snszyk.zbusiness.ops.enums.DiagnosisReportTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 诊断报告消息服务
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
@AllArgsConstructor
@Slf4j
public class DiagnosisReportMessageService {

    private final MessageLogicService messageLogicService;
    private final IMessageSettingService messageSettingService;
    private final IAttachService attachService;

    /**
     * 消息标题
     */
    private static final String MESSAGE_TITLE = "诊断报告通知";

    /**
     * 系统发送者
     */
    private static final String SYSTEM_SENDER = "SiDAs";

    /**
     * 消息类型：系统消息
     */
    private static final String MESSAGE_TYPE = MessageTypeEnum.NOTICE.getCode();

    /**
     * 发送诊断报告消息
     *
     * @param reportDto 诊断报告DTO（包含所有需要的信息）
     * @return 是否发送成功
     */
    public boolean sendDiagnosisReportMessage(AiopsDiagnosisReportDto reportDto) {
        if (reportDto == null) {
            log.error("诊断报告为空，无法发送消息");
            return false;
        }

        try {
            // 构建消息内容
            Map<String, Object> contentMap = buildMessageContent(reportDto);
            String content = JSON.toJSONString(contentMap);

            // 构建消息对象
            MessageVo messageVo = new MessageVo();
            messageVo.setTitle(MESSAGE_TITLE);
			messageVo.setAppKey("SiDAs");
            messageVo.setContent(content);
            messageVo.setType(MESSAGE_TYPE);
            messageVo.setSender(SYSTEM_SENDER);
            messageVo.setIsImmediate(YesNoEnum.YES.getCode()); // 立即发送
            messageVo.setBizType(MessageBizTypeEnum.REPORT_MANAGEMENT.getCode());
            messageVo.setChannel(MessageChannelEnum.MINI_PROGRAM.getCode());

            // 设置业务ID为报告ID
            messageVo.setBizId(reportDto.getId().toString());

            // 如果有附件，设置附件链接
//            if (Func.isNotEmpty(reportDto.getAttachId())) {
//                Attach attach = attachService.getById(reportDto.getAttachId());
//                if (attach != null) {
//                    messageVo.setUrl(attach.getLink());
//                    log.debug("设置诊断报告附件链接: {}", attach.getLink());
//                }
//            }

            // 设置接收人信息 - 从消息设置中获取
            setReceiverInfo(messageVo, reportDto.getTenantId());

            // 发送消息
            log.debug("准备发送诊断报告消息，报告ID: {}, 标题: {}", reportDto.getId(), reportDto.getTitle());
            R result = messageLogicService.commitMessage(messageVo);
            if (result.isSuccess()) {
                log.info("诊断报告消息发送成功，报告ID: {}, 标题: {}", reportDto.getId(), reportDto.getTitle());
                return true;
            } else {
                log.error("诊断报告消息发送失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("诊断报告消息发送异常，报告ID: " + reportDto.getId(), e);
            return false;
        }
    }

    /**
     * 构建消息内容
     *
     * @param reportDto 诊断报告DTO（包含所有需要的信息）
     * @return 消息内容Map
     */
    private Map<String, Object> buildMessageContent(AiopsDiagnosisReportDto reportDto) {
        Map<String, Object> contentMap = new HashMap<>();

        // 报告基本信息
        contentMap.put("reportId", reportDto.getId());
        contentMap.put("title", reportDto.getTitle());

        // 报告类型
        Integer typeCode = reportDto.getType();
        DiagnosisReportTypeEnum typeEnum = DiagnosisReportTypeEnum.getByCode(typeCode);
        contentMap.put("type", typeCode);
        contentMap.put("typeName", typeEnum != null ? typeEnum.getName() : "未知类型");

        // 数据时间范围
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (reportDto.getDataStartDate() != null) {
            contentMap.put("dataStartDate", reportDto.getDataStartDate().format(formatter));
        }
        if (reportDto.getDataEndDate() != null) {
            contentMap.put("dataEndDate", reportDto.getDataEndDate().format(formatter));
        }

        // 发布信息
        if (reportDto.getPublishTime() != null) {
            contentMap.put("publishTime", reportDto.getPublishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        // 发布人信息
        if (StringUtil.isNotBlank(reportDto.getCreateUserName())) {
            contentMap.put("publisher", reportDto.getCreateUserName());
        } else {
            // 如果DTO中没有创建人姓名，则通过用户缓存查询
            User user = UserCache.getUser(reportDto.getCreateUser());
            contentMap.put("publisher", user != null ? user.getName() : "系统");
        }

        // 附件信息
        if (Func.isNotEmpty(reportDto.getAttachId())) {
            contentMap.put("attachId", reportDto.getAttachId());

            // 获取附件的文件扩展名
            try {
                Attach attach = attachService.getById(reportDto.getAttachId());
                if (attach != null && StringUtil.isNotBlank(attach.getExtension())) {
                    contentMap.put("fileExtension", attach.getExtension());
                    log.debug("添加附件文件扩展名: {}", attach.getExtension());
                }
            } catch (Exception e) {
                log.warn("获取附件文件扩展名失败，附件ID: {}", reportDto.getAttachId(), e);
            }
        }

        return contentMap;
    }

    /**
     * 设置接收人信息
     *
     * @param messageVo 消息对象
     * @param tenantId 租户ID
     */
    private void setReceiverInfo(MessageVo messageVo, String tenantId) {
        // 从消息设置中获取接收人信息
        MessageSetting messageSetting = messageSettingService.getBy(tenantId, MessageBizTypeEnum.REPORT_MANAGEMENT.getCode());

        if (messageSetting != null && messageSetting.getEnabled() == 1) {
            // 如果存在已启用的消息设置，使用配置的接收人信息
            String receiverType = messageSetting.getReceiverType();
            String receiverInfo = messageSetting.getReceiverInfo();

            if (StringUtil.isNotBlank(receiverInfo)) {
                try {
                    // 解析接收人信息JSON字符串为ReceiverInfoVo对象
                    ReceiverInfoVo receiverInfoVo = JSON.parseObject(receiverInfo, ReceiverInfoVo.class);

                    // 设置接收人类型和信息
                    messageVo.setReceiverType(receiverType);
                    messageVo.setReceiverInfo(receiverInfo);
                    messageVo.setReceiverInfoVo(receiverInfoVo);

                    log.debug("使用消息设置中的接收人信息，类型: {}, 信息: {}", receiverType, receiverInfo);
                    return;
                } catch (Exception e) {
                    log.error("解析消息设置中的接收人信息失败: {}", receiverInfo, e);
                    // 解析失败时继续使用默认逻辑
                }
            }
        }

        // 如果没有配置或配置无效，默认设置为当前用户
        ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
        List<ReceiverInfoVo.UserVo> userVoList = new ArrayList<>();

        // 创建当前用户的UserVo对象
        ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
        userVo.setId(SecureUtil.getUserId());
        userVo.setRealName(SecureUtil.getUserName());
        userVoList.add(userVo);

        // 设置用户列表
        receiverInfoVo.setUserList(userVoList);
        messageVo.setReceiverInfoVo(receiverInfoVo);
        messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
        log.debug("使用当前用户作为接收人，用户ID: {}", SecureUtil.getUserId());
    }
}
