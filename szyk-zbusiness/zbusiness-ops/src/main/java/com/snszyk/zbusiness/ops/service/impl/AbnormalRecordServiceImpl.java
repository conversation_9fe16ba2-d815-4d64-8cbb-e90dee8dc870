/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import com.snszyk.zbusiness.ops.mapper.AbnormalRecordMapper;
import com.snszyk.zbusiness.ops.service.IAbnormalRecordService;
import com.snszyk.zbusiness.ops.vo.AbnormalRecordVO;
import org.springframework.stereotype.Service;

/**
 * 设备异常明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Service
public class AbnormalRecordServiceImpl extends ServiceImpl<AbnormalRecordMapper, AbnormalRecord> implements IAbnormalRecordService {

	@Override
	public IPage<AbnormalRecordVO> selectAbnormalRecordPage(IPage<AbnormalRecordVO> page, AbnormalRecordVO abnormalRecord) {
		return page.setRecords(baseMapper.selectAbnormalRecordPage(page, abnormalRecord));
	}

}
