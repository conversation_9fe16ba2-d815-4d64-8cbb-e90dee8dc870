/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ops.dto.AlarmDTO;
import com.snszyk.zbusiness.ops.dto.AlarmStatisticsDTO;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import com.snszyk.zbusiness.ops.service.logic.AlarmLogicService;
import com.snszyk.zbusiness.ops.vo.AlarmCloseVO;
import com.snszyk.zbusiness.ops.vo.AlarmRecordVO;
import com.snszyk.zbusiness.ops.vo.AlarmStatisticVO;
import com.snszyk.zbusiness.ops.vo.AlarmVO;
import com.snszyk.zbusiness.ops.wrapper.AlarmRecordWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 报警管理信息表 控制器
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/alarm")
@Api(value = "报警管理信息表", tags = "报警管理信息表接口")
public class AlarmController extends SzykController {

	private final AlarmLogicService alarmLogicService;
	private final IAlarmRecordService alarmRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<AlarmDTO> detail(@PathVariable(name = "id") Long id) {
		return R.data(alarmLogicService.detail(id));
	}

	/**
	 * 自定义分页 报警管理信息表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "alarmLevel", value = "报警等级", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "status", value = "单据状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tenantId", value = "租户id", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入alarm")
	public R<IPage<AlarmDTO>> page(@ApiIgnore AlarmVO alarm, Query query) {
		IPage<AlarmDTO> pages = alarmLogicService.page(Condition.getPage(query), alarm);
		return R.data(pages);
	}

	/**
	 * 报警记录分页列表
	 */
	@GetMapping("/alarmRecords")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "alarmId", value = "报警id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "monitorId", value = "测点id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "alarmIndex", value = "报警指标", required = true, paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "alarmType", value = "报警类型", required = true, paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "报警记录分页列表", notes = "传入alarmRecord")
	public R<IPage<AlarmRecordVO>> alarmRecords(@ApiIgnore AlarmRecordVO alarmRecord, Query query) {
		return R.data(alarmRecordService.page(Condition.getPage(query), alarmRecord));
	}

	/**
	 * 关闭报警 报警管理信息表
	 */
	@PostMapping("/close")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "关闭报警", notes = "传入alarmClose")
	public R close(@Valid @RequestBody AlarmCloseVO alarmClose) {
		return R.status(alarmLogicService.closeAlarm(alarmClose));
	}

	/**
	 * 导出列表Excel
	 */
	@GetMapping("/exportExcel")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "status", value = "单据状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "vibrateSubarea", value = "振动分区", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "alarmLevel", value = "报警等级", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "导出列表Excel", notes = "传入alarm")
	public void exportExcel(@ApiIgnore AlarmVO alarm, HttpServletResponse response) {
		alarmLogicService.exportExcel(alarm, response);
		//return R.success("操作成功");
	}

	/**
	 * 门户设备详情报警统计
	 */
	@GetMapping("/equipmentAlarmStatistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "门户设备详情报警统计", notes = "传入alarmStatistic")
	public R<AlarmStatisticsDTO> equipmentAlarmStatistics(AlarmStatisticVO alarmStatistic) {
		return R.data(alarmLogicService.equipmentAlarmStatistics(alarmStatistic));
	}

	/**
	 * 报警统计
	 */
	@GetMapping("/alarm-analysis")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "regionLevel", value = "区域层次", required = true, paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "parentId", value = "父级id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "regionId", value = "区域id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "报警统计", notes = "传入alarmStatistic")
	public R<List<AlarmStatisticsDTO>> alarmAnalysis(@ApiIgnore AlarmStatisticVO alarmStatistic, SzykUser szykUser) {
		alarmStatistic.setTenantId(szykUser.getTenantId()).setIsExport(Boolean.FALSE);
		List<AlarmStatisticsDTO> list = alarmLogicService.alarmAnalysis(alarmStatistic);
		return R.data(list);
	}

	/**
	 * 报警统计导出
	 */
	@GetMapping("/export-alarm-analysis")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "regionId", value = "区域id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "报警统计导出", notes = "传入alarmStatistic")
	public void exportAlarmAnalysis(@ApiIgnore AlarmStatisticVO alarmStatistic, SzykUser szykUser, HttpServletResponse response) {
		alarmStatistic.setTenantId(szykUser.getTenantId()).setIsExport(Boolean.TRUE);
		alarmLogicService.exportAlarmStatistics(alarmStatistic, response);
	}

	/**
	 * 根据波形id获取报警记录
	 */
	@GetMapping("/getAlarmByWave")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据波形id获取报警记录", notes = "传入waveId")
	public R<AlarmRecordVO> getAlarmByWave(@ApiParam(value = "波形id", required = true) @RequestParam Long waveId) {
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda().eq(AlarmRecord::getWaveId, waveId));
		if (Func.isNotEmpty(alarmRecordList)) {
			return R.data(AlarmRecordWrapper.build().entityVO(alarmRecordList.get(0)));
		}
		return R.data(null);
	}

}
