/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.ops.dto.AlarmThresholdDTO;
import com.snszyk.zbusiness.ops.entity.AlarmThreshold;
import com.snszyk.zbusiness.ops.service.IAlarmThresholdService;
import com.snszyk.zbusiness.ops.service.logic.AlarmThresholdLogicService;
import com.snszyk.zbusiness.ops.vo.AlarmThresholdVO;
import com.snszyk.zbusiness.ops.vo.ThresholdSelectVO;
import com.snszyk.zbusiness.ops.wrapper.AlarmThresholdWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * 报警门限表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/alarmThreshold")
@Api(value = "报警门限表", tags = "报警门限表接口")
public class AlarmThresholdController extends SzykController {

	private final IAlarmThresholdService alarmThresholdService;
	private final AlarmThresholdLogicService alarmThresholdLogicService;

	/**
	 * 详情
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<AlarmThresholdVO> detail(@PathVariable(name = "id") Long id) {
		return R.data(AlarmThresholdWrapper.build().entityVO(alarmThresholdService.getById(id)));
	}

	/**
	 * 振动指标门限分页 报警门限表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树节点id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "振动指标门限分页", notes = "传入alarmThreshold")
	public R<IPage<AlarmThresholdDTO>> page(@ApiIgnore AlarmThresholdVO alarmThreshold, Query query) {
		IPage<AlarmThresholdDTO> pages = alarmThresholdLogicService.page(Condition.getPage(query), alarmThreshold);
		return R.data(pages);
	}

	/**
	 * 应力波门限分页 报警门限表
	 */
	@GetMapping("/stressPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树节点id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "应力波门限分页", notes = "传入alarmThreshold")
	public R<IPage<AlarmThresholdDTO>> stressQuotaPage(@ApiIgnore AlarmThresholdVO alarmThreshold, Query query) {
		alarmThreshold.setSampleDataType(SampledDataTypeEnum.STRESS.getCode());
		IPage<AlarmThresholdDTO> pages = alarmThresholdLogicService.page(Condition.getPage(query), alarmThreshold);
		return R.data(pages);
	}

	/**
	 * 非振动指标门限分页 报警门限表
	 */
	@GetMapping("/nonVibrationQuotaPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树节点id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "sampleDataType", value = "采样数据类型", required = true, paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "非振动指标门限分页", notes = "传入alarmThreshold")
	public R<IPage<AlarmThresholdDTO>> nonVibrationQuotaPage(@ApiIgnore AlarmThresholdVO alarmThreshold, Query query) {
		IPage<AlarmThresholdDTO> pages = alarmThresholdLogicService.nonVibrationQuotaPage(Condition.getPage(query), alarmThreshold);
		return R.data(pages);
	}

	/**
	 * 通用指标门限新增 报警门限表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "通用指标门限-新增", notes = "传入alarmThreshold")
	public R save(@Valid @RequestBody AlarmThresholdVO alarmThreshold) {
		return R.status(alarmThresholdService.save(alarmThreshold));
	}

	/**
	 * 通用指标门限修改 报警门限表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "通用指标门限-修改", notes = "传入alarmThreshold")
	public R update(@Valid @RequestBody AlarmThreshold alarmThreshold) {
		return R.status(alarmThresholdService.updateById(alarmThreshold));
	}

	/**
	 * 通用指标门限新增或修改 报警门限表
	 */
	@PostMapping("/submitGeneral")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "通用指标门限-新增或修改", notes = "传入alarmThreshold")
	public R submitGeneral(@Valid @RequestBody AlarmThresholdVO alarmThreshold) {
		return R.status(alarmThresholdLogicService.submitGeneral(alarmThreshold));
	}

	/**
	 * 删除 报警门限表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(alarmThresholdService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 门限自动铺设 报警门限表
	 */
	@PostMapping("/standardAutoSet")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "门限自动铺设", notes = "传入user")
	public R standardAutoSet(@ApiParam(value = "门限类型", required = true) @RequestParam Integer type, SzykUser user) {
		return R.status(alarmThresholdLogicService.standardAutoSet(type, user.getDeptId(), user.getTenantId()));
	}

	/**
	 * 按选择铺设门限 报警门限表
	 */
	@PostMapping("/autoSetByIds")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "按选择铺设门限", notes = "传入ids")
	public R autoSetByIds(@ApiParam(value = "门限类型", required = true) @RequestParam Integer type,
						  @ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(alarmThresholdLogicService.autoSetByIds(type, Func.toLongList(ids)));
	}

	/**
	 * 根据波形id获取门限类型下拉列表
	 */
	@GetMapping("/thresholdSelectList")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "根据波形id获取门限类型下拉列表", notes = "传入waveId")
	public R<List<ThresholdSelectVO>> thresholdSelectList(@ApiParam(value = "波形id", required = true) @RequestParam Long waveId) {
		return R.data(alarmThresholdLogicService.thresholdSelectList(waveId));
	}

	/**
	 * 门户波形报警门限修改 报警门限表
	 */
	@PostMapping("/updateThreshold")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "门户波形报警门限修改", notes = "传入alarmThreshold")
	public R updateThreshold(@Valid @RequestBody AlarmThresholdVO alarmThreshold) {
		return R.status(alarmThresholdLogicService.updateThreshold(alarmThreshold));
	}

}
