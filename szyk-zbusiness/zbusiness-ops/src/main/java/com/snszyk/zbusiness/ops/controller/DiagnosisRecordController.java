/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
import com.snszyk.zbusiness.ops.entity.DiagnosisRecord;
import com.snszyk.zbusiness.ops.service.IDiagnosisRecordService;
import com.snszyk.zbusiness.ops.service.IDiagnosticReportService;
import com.snszyk.zbusiness.ops.service.logic.DiagnosticReportLogicService;
import com.snszyk.zbusiness.ops.vo.DiagnosisRecordVO;
import com.snszyk.zbusiness.ops.vo.DiagnosticReportVO;
import com.snszyk.zbusiness.ops.wrapper.DiagnosisRecordWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * 诊断记录表 控制器
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/diagnosisRecord")
@Api(value = "诊断记录", tags = "诊断记录接口")
@MapperScan("com.szyk.rn.*")
public class DiagnosisRecordController extends SzykController {

	private final IDiagnosisRecordService diagnosisRecordService;
	private final IDiagnosticReportService diagnosticReportService;
	private final DiagnosticReportLogicService diagnosticLogicService;

	/**
	 * 详情
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<DiagnosisRecordVO> detail(@PathVariable(name = "id") Long id) {
		DiagnosisRecord detail = diagnosisRecordService.getById(id);
		return R.data(DiagnosisRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 列表 诊断记录表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入diagnosisRecord")
	public R<List<DiagnosisRecordVO>> list(@ApiIgnore DiagnosisRecordVO diagnosisRecord) {
		List<DiagnosisRecordVO> list = diagnosisRecordService.getList(diagnosisRecord);
		return R.data(list);
	}

	/**
	 * 添加专家诊断 报警管理信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "添加专家诊断", notes = "传入diagnosisRecord")
	public R<List<DiagnosisRecordVO>> submit(@Valid @RequestBody DiagnosisRecordVO diagnosisRecord) {
		return R.data(diagnosisRecordService.submit(diagnosisRecord));
	}

	/**
	 * 诊断报告详情
	 */
	@GetMapping("/diagnosticReport/{id}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "诊断报告详情", notes = "传入id")
	public R<DiagnosticReportDTO> diagnosticReport(@PathVariable(name = "id") Long id) {
		return R.data(diagnosticLogicService.diagnosticReport(id));
	}

	/**
	 * 诊断报告分页
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "诊断报告分页", notes = "传入diagnosticReport")
	public R<IPage<DiagnosticReportDTO>> page(@ApiIgnore DiagnosticReportVO diagnosticReport, Query query) {
		IPage<DiagnosticReportDTO> pages = diagnosticReportService.page(Condition.getPage(query), diagnosticReport);
		return R.data(pages);
	}

}
