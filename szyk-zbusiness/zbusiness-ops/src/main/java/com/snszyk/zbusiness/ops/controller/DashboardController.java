//package com.snszyk.zbusiness.ops.controller;
//
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.snszyk.core.boot.ctrl.SzykController;
//import com.snszyk.core.secure.SzykUser;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.ops.dto.AlarmDeviceDetailDTO;
//import com.snszyk.zbusiness.ops.dto.AlarmTrendDTO;
//import com.snszyk.zbusiness.ops.entity.Alarm;
//import com.snszyk.zbusiness.ops.service.DashboardService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * 诊断模块门户端大屏接口
// *
// * <AUTHOR>
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("/dashboard")
//@Api(value = "诊断模块门户端大屏接口", tags = "诊断模块门户端大屏接口")
//public class DashboardController extends SzykController {
//
//	private final DashboardService dashboardService;
//
//	/**
//	 * 集团驾驶舱-设备报警状态分布
//	 */
//	@ApiOperation("集团驾驶舱-设备报警状态分布")
//	@ApiOperationSupport(order = 1)
//	@GetMapping("/group/alarm-state-distribution")
//	public R<List<Integer>> alarmStateDistribution(SzykUser szykUser) {
//		return dashboardService.groupAlarmStateDistribution(szykUser.getTenantId(), szykUser.getDeptId());
//	}
//
//	/**
//	 * 集团驾驶舱-厂区设备报警概况
//	 */
//	@ApiOperation("集团驾驶舱-厂区设备报警概况")
//	@ApiOperationSupport(order = 2)
//	@GetMapping("/group/sidas-alarm-info")
//	public R<AlarmDeviceDetailDTO> sidasAlarmInfo(SzykUser szykUser) {
//		return dashboardService.groupSidasAlarmInfo(szykUser.getTenantId(), szykUser.getDeptId());
//	}
//
//	/**
//	 * 集团驾驶舱-最新待处理报警
//	 */
//	@ApiOperation("集团驾驶舱-最新待处理报警")
//	@ApiOperationSupport(order = 3)
//	@GetMapping("/group/latest-pending-alarm")
//	public R<List<Alarm>> latestPendingAlarm(SzykUser szykUser) {
//		return dashboardService.groupLatestPendingAlarm(szykUser.getTenantId(), szykUser.getDeptId());
//	}
//
//	/**
//	 * 厂区驾驶舱-设备报警状态分布
//	 */
//	@ApiOperation("厂区驾驶舱-设备报警状态分布")
//	@ApiOperationSupport(order = 4)
//	@GetMapping("/plant/alarm-state-distribution/{id}")
//	public R<List<Integer>> plantAlarmStateDistribution(@PathVariable(name = "id") String id) {
//		return dashboardService.plantAlarmStateDistribution(id);
//	}
//
//	/**
//	 * 厂区驾驶舱-最新待处理报警
//	 */
//	@ApiOperation("厂区驾驶舱-最新待处理报警")
//	@ApiOperationSupport(order = 5)
//	@GetMapping("/plant/latest-pending-alarm/{id}")
//	public R<List<Alarm>> plantLatestPendingAlarm(@PathVariable(name = "id") String id) {
//		return null;
////		return dashboardService.plantLatestPendingAlarm(id);
//	}
//
//	/**
//	 * 获取报警趋势 - 按天展示
//	 */
//	@GetMapping("/alarm-trend")
//	@ApiOperation("报警趋势")
//	@ApiOperationSupport(order = 6)
//	public R<AlarmTrendDTO> getAlarmTrend(Integer lastDays) {
//		return R.data(dashboardService.getAlarmTrend(lastDays));
//	}
//
//	/**
//	 * 获取近1年报警趋势 - 按月展示
//	 */
//	@GetMapping("/last-year-alarm-trend")
//	@ApiOperation("获取近1年报警趋势（按月展示）")
//	@ApiOperationSupport(order = 7)
//	public R<AlarmTrendDTO> getLastYearAlarmTrend() {
//		return R.data(dashboardService.getLastYearAlarmTrend());
//	}
//}
