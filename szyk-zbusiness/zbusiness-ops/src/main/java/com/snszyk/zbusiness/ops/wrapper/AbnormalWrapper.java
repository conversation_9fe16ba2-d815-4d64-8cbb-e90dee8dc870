/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.vo.AbnormalVO;

import java.util.Objects;

/**
 * 设备异常信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public class AbnormalWrapper extends BaseEntityWrapper<Abnormal, AbnormalVO> {

	public static AbnormalWrapper build() {
		return new AbnormalWrapper();
 	}

	@Override
	public AbnormalVO entityVO(Abnormal abnormal) {
		AbnormalVO abnormalVO = Objects.requireNonNull(BeanUtil.copy(abnormal, AbnormalVO.class));

		//User createUser = UserCache.getUser(abnormal.getCreateUser());
		//User updateUser = UserCache.getUser(abnormal.getUpdateUser());
		//abnormalVO.setCreateUserName(createUser.getName());
		//abnormalVO.setUpdateUserName(updateUser.getName());

		return abnormalVO;
	}

}
