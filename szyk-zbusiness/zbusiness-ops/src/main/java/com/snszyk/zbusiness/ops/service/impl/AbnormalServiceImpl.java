/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.EquipmentDTO;
import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.enums.AlarmIndexEnum;
import com.snszyk.zbusiness.basic.enums.ModelTypeEnum;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import com.snszyk.zbusiness.basic.vo.AbnormalParamVO;
import com.snszyk.zbusiness.ops.dto.*;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.entity.AbnormalDetail;
import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import com.snszyk.zbusiness.ops.enums.*;
import com.snszyk.zbusiness.ops.vo.AbnormalCloseVO;
import com.snszyk.zbusiness.ops.vo.EquipmentMonitorQueryVO;
import com.snszyk.zbusiness.ops.mapper.AbnormalMapper;
import com.snszyk.zbusiness.ops.service.IAbnormalDetailService;
import com.snszyk.zbusiness.ops.service.IAbnormalRecordService;
import com.snszyk.zbusiness.ops.service.IAbnormalService;
import com.snszyk.zbusiness.ops.vo.AbnormalVO;
import com.snszyk.zbusiness.ops.vo.EquipmentMonitorQueryVO;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * 设备异常信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Slf4j
@AllArgsConstructor
@Service
public class AbnormalServiceImpl extends ServiceImpl<AbnormalMapper, Abnormal> implements IAbnormalService {

	private static final String THRESHOLD_ABNORMAL_RESULT = "门限%s报警，报警等级：%s级，权重：%s%%";
	private static final String MECHANISM_ABNORMAL_RESULT = "%s，等级：%s级，权重：%s%%";
	private static final String AI_ABNORMAL_RESULT = "AI报警：结果异常，权重：%s%%";
	private final IAbnormalDetailService abnormalDetailService;
	private final IAbnormalRecordService abnormalRecordService;
	private final IEquipmentService equipmentService;
	private final IMonitorService monitorService;


	/**
	 * 异常管理的分页
	 * 管理员 tenantId null or 其他
	 * 普通用户 tenantId null
	 *
	 * @param page
	 * @param abnormal
	 * @return
	 */
	@Override
	public IPage<AbnormalDTO> page(IPage<AbnormalDTO> page, AbnormalVO abnormal) {
		if (Func.isNotEmpty(abnormal.getStartDate())) {
			abnormal.setStartDate(abnormal.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(abnormal.getEndDate())) {
			abnormal.setEndDate(abnormal.getEndDate() + " 23:59:59");
		}
		List<AbnormalDTO> list = baseMapper.page(page, abnormal);
		if (Func.isNotEmpty(list)) {
			// 获取所有异常ID，用于批量查询测点信息
			List<Long> abnormalIds = list.stream()
				.map(AbnormalDTO::getId)
				.collect(Collectors.toList());

			// 批量查询所有异常的测点详情
			Map<Long, List<AbnormalDetail>> abnormalDetailMap = new HashMap<>();
			if (Func.isNotEmpty(abnormalIds)) {
				List<AbnormalDetail> allDetails = abnormalDetailService.list(Wrappers.<AbnormalDetail>query().lambda()
					.in(AbnormalDetail::getAbnormalId, abnormalIds));

				// 按异常ID分组
				abnormalDetailMap = allDetails.stream()
					.collect(Collectors.groupingBy(AbnormalDetail::getAbnormalId));
			}

			// 获取所有测点ID
			Set<Long> monitorIds = new HashSet<>();
			abnormalDetailMap.values().forEach(details ->
				details.forEach(detail -> monitorIds.add(detail.getMonitorId())));

			// 批量查询测点信息
			Map<Long, Monitor> monitorMap = new HashMap<>();
			if (Func.isNotEmpty(monitorIds)) {
				List<Monitor> monitors = monitorService.listByIds(monitorIds);
				monitorMap = monitors.stream()
					.collect(Collectors.toMap(Monitor::getId, Function.identity(), (k1, k2) -> k1));
			}

			// 最终的测点Map，用于快速查找
			final Map<Long, Monitor> finalMonitorMap = monitorMap;

			Map<Long, List<AbnormalDetail>> finalAbnormalDetailMap = abnormalDetailMap;
			list.forEach(dto -> {
				// 设置设备信息
				Equipment equipment = equipmentService.getById(dto.getEquipmentId());
				if (Func.isNotEmpty(equipment)) {
					EquipmentDTO equipmentDTO = BeanUtil.copy(equipment, EquipmentDTO.class);
					equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()))
						.setPathName(equipmentDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
					dto.setEquipmentInfo(equipmentDTO);
				}

				// 设置异常等级和原因
				dto.setAbnormalLevelName(AbnormalLevelEnum.getByCode(dto.getAbnormalLevel()).getName())
					.setReasonName(Arrays.asList(dto.getAbnormalReason().split(StringPool.COMMA)).stream().map(r ->
						AlarmBizTypeEnum.getByCode(Func.toInt(r)).getName()).collect(Collectors.joining(",")));
				dto.setStatusName(AlarmStatusEnum.getByCode(dto.getStatus()).getName());

				// 设置测点名称
				List<AbnormalDetail> details = finalAbnormalDetailMap.get(dto.getId());
				if (Func.isNotEmpty(details)) {
					// 获取测点名称列表
					List<String> monitorNames = details.stream()
						.map(AbnormalDetail::getMonitorId)
						.distinct()
						.map(monitorId -> {
							Monitor monitor = finalMonitorMap.get(monitorId);
							return monitor != null ? monitor.getName() : "";
						})
						.filter(Func::isNotEmpty)
						.collect(Collectors.toList());

					// 用逗号连接测点名称
					if (Func.isNotEmpty(monitorNames)) {
						dto.setMonitorName(String.join(",", monitorNames));
					}
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	public AbnormalDTO detail(Long id) {
		Abnormal abnormal = this.getById(id);
		if (abnormal == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		AbnormalDTO detail = Objects.requireNonNull(BeanUtil.copy(abnormal, AbnormalDTO.class));

		Equipment equipment = equipmentService.getById(abnormal.getEquipmentId());
		if (Func.isNotEmpty(equipment)) {
			EquipmentDTO equipmentDTO = BeanUtil.copy(equipment, EquipmentDTO.class);
			equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()))
				.setPathName(equipmentDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
			detail.setEquipmentInfo(equipmentDTO);
		}
		detail.setAbnormalLevelName(AbnormalLevelEnum.getByCode(detail.getAbnormalLevel()).getName())
			.setReasonName(Arrays.asList(detail.getAbnormalReason().split(StringPool.COMMA)).stream().map(r ->
				AlarmBizTypeEnum.getByCode(Func.toInt(r)).getName()).collect(Collectors.joining(",")));
		detail.setStatusName(AlarmStatusEnum.getByCode(detail.getStatus()).getName());
		List<AbnormalDetail> list = abnormalDetailService.list(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getAbnormalId, id));
		if (Func.isNotEmpty(list)) {
			// 设置测点名称
			this.setMonitorName(detail, list);
			List<AbnormalDetailDTO> monitorAbnormalList = new ArrayList<>();
			Map<Long, List<AbnormalDetail>> detailMap = list.stream()
				.collect(Collectors.groupingBy(AbnormalDetail::getMonitorId));
			// 部位
			for (Long key : detailMap.keySet()) {
				AbnormalDetailDTO monitorAbnormal = new AbnormalDetailDTO(key);
				Monitor monitor = monitorService.getById(key);
				if (ObjectUtil.isNotEmpty(monitor)) {
					MonitorDTO dto = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
					Equipment equipment1 = equipmentService.getById(monitor.getEquipmentId());
					if (ObjectUtil.isNotEmpty(equipment1)) {
						EquipmentDTO equipmentInfo = Objects.requireNonNull(BeanUtil.copy(equipment, EquipmentDTO.class));
						dto.setEquipmentInfo(equipmentInfo);
					}
					dto.setPathName(dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
					monitorAbnormal.setMonitorInfo(dto);
				}

				// 波形
				List<AbnormalDetailDTO> waveAbnormalList = detailMap.get(key).stream().map(d ->
					Objects.requireNonNull(BeanUtil.copy(d, AbnormalDetailDTO.class))).collect(Collectors.toList());
				waveAbnormalList.forEach(waveAbnormal -> {
					List<AbnormalRecord> recordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
						.eq(AbnormalRecord::getAbnormalId, id).eq(AbnormalRecord::getWaveId, waveAbnormal.getWaveId()));
					// 门限、机理、AI
					Map<Integer, List<AbnormalRecord>> recordMap = recordList.stream()
						.collect(Collectors.groupingBy(AbnormalRecord::getAbnormalType));
					List<AbnormalRecordDTO> abnormalRecordList = new ArrayList<>();
					for (Integer type : recordMap.keySet()) {
						AbnormalRecordDTO abnormalRecord = new AbnormalRecordDTO(type);
						abnormalRecord.setAbnormalTypeName(AlarmBizTypeEnum.getByCode(type).getName());
						List<AbnormalRecord> abnormalRecords = recordMap.get(type);
						List<AbnormalResultDTO> abnormalResultList = null;
						switch (AlarmBizTypeEnum.getByCode(type)) {
							case THRESHOLD:
								abnormalResultList = new ArrayList<>();
								for (AbnormalRecord r : abnormalRecords) {
									AbnormalResultDTO resultDTO;
									if (Func.toInt(StringPool.ONE) == r.getStrategyType()) {
										resultDTO = new AbnormalResultDTO(r.getStrategyType(), "连续", JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class));
									} else {
										resultDTO = new AbnormalResultDTO(r.getStrategyType(), "非连续", JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class));
									}
									resultDTO.setResultContent(String.format(THRESHOLD_ABNORMAL_RESULT,
										AlarmIndexEnum.EFFECTIVE_VALUE.getName(), r.getAbnormalLevel(), r.getWeight()));
									abnormalResultList.add(resultDTO);
								}
								break;
							case INTELLIGENCE:
								abnormalResultList = new ArrayList<>();
								for (AbnormalRecord r : abnormalRecords) {
									AbnormalResultDTO resultDTO;
									if (Func.toInt(StringPool.ONE) == r.getStrategyType()) {
										resultDTO = new AbnormalResultDTO(r.getStrategyType(), "连续", JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class));
									} else {
										resultDTO = new AbnormalResultDTO(r.getStrategyType(), "非连续", JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class));
									}
									resultDTO.setResultContent(String.format(AI_ABNORMAL_RESULT, r.getWeight()));
									abnormalResultList.add(resultDTO);
								}
								break;
							case MECHANISM:
								abnormalResultList = new ArrayList<>();
								String conStr = "";
								String disconStr = "";
								Integer conLevel = AlarmLevelEnum.NORMAL.getCode();
								Integer disconLevel = AlarmLevelEnum.NORMAL.getCode();
								Integer conWeight = 0;
								Integer disconWeight = 0;
								AbnormalParamVO continuousStrategy = null;
								AbnormalParamVO discontinuousStrategy = null;
								for (AbnormalRecord r : abnormalRecords) {
									if (Func.toInt(StringPool.ONE) == r.getStrategyType()) {
										conStr += ModelTypeEnum.getByCode(r.getMechanismType()).getName() + "、";
										conWeight = r.getWeight();
										if (conLevel < r.getAbnormalLevel()) {
											conLevel = r.getAbnormalLevel();
										}
										continuousStrategy = JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class);
									} else {
										disconStr += ModelTypeEnum.getByCode(r.getMechanismType()).getName() + "、";
										disconWeight = r.getWeight();
										if (disconLevel < r.getAbnormalLevel()) {
											disconLevel = r.getAbnormalLevel();
										}
										discontinuousStrategy = JSONUtil.toBean(r.getStrategyConfig(), AbnormalParamVO.class);
									}
								}
								if (Func.isNotEmpty(conStr)) {
									AbnormalResultDTO resultDTO = new AbnormalResultDTO(1, "连续", continuousStrategy);
									resultDTO.setResultContent(String.format(MECHANISM_ABNORMAL_RESULT, conStr.substring(0, conStr.length() - 1), conLevel, conWeight));
									abnormalResultList.add(resultDTO);
								}
								if (Func.isNotEmpty(disconStr)) {
									AbnormalResultDTO resultDTO = new AbnormalResultDTO(2, "非连续", discontinuousStrategy);
									resultDTO.setResultContent(String.format(MECHANISM_ABNORMAL_RESULT, disconStr.substring(0, disconStr.length() - 1), disconLevel, disconWeight));
									abnormalResultList.add(resultDTO);
								}
								break;
							default:
						}
						abnormalRecord.setResultList(abnormalResultList);
						abnormalRecordList.add(abnormalRecord);
					}
					waveAbnormal.setAbnormalRecordList(abnormalRecordList).setAbnormalLevelName(AbnormalLevelEnum.getByCode(waveAbnormal.getAbnormalLevel()).getName());
				});
				monitorAbnormal.setWaveAbnormalList(waveAbnormalList);
				monitorAbnormalList.add(monitorAbnormal);
			}
			detail.setMonitorAbnormalList(monitorAbnormalList.stream().map(d ->
				Objects.requireNonNull(BeanUtil.copy(d, AbnormalDetailDTO.class))).collect(Collectors.toList()));
			detail.setConfidenceLevel(this.getConfidenceLevel(id));
			//关闭的原因
			String closeReason = detail.getCloseReason();
			if (Func.isNotEmpty(closeReason)) {
				AbnormalCloseVO closeVO = JSONUtil.toBean(closeReason, AbnormalCloseVO.class);
		//alarm_close_reason字典
				closeVO.setCloseReasonName(DictBizCache.getValue(DictBizEnum.ALARM_CLOSE_REASON, closeVO.getCloseReason()));
				detail.setCloseReason(JSONUtil.toJsonStr(closeVO));
			}
		}
		return detail;
	}

	/**
	 * 获取置信度
	 *
	 * @param id
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/6/12 13:56
	 */
	private Integer getConfidenceLevel(Long id) {
		AbnormalRecord abnormalRecord = abnormalRecordService.getOne(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, id).orderByDesc(AbnormalRecord::getAbnormalLevel, AbnormalRecord::getWeight)
			.last(" limit 1"));
		if (abnormalRecord == null) {
			return null;
		}
		Integer confidenceLevel = abnormalRecord.getWeight();
		List<AbnormalRecord> list = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, id).ne(AbnormalRecord::getAbnormalType, abnormalRecord.getAbnormalType())
			.eq(AbnormalRecord::getStrategyType, abnormalRecord.getStrategyType()).orderByDesc(AbnormalRecord::getAbnormalLevel, AbnormalRecord::getWeight));
		Map<Integer, List<AbnormalRecord>> recordMap = list.stream()
			.collect(Collectors.groupingBy(AbnormalRecord::getAbnormalType));
		if (Func.isNotEmpty(recordMap)) {
			for (Integer key : recordMap.keySet()) {
				confidenceLevel += recordMap.get(key).get(0).getWeight();
			}
		}
		return confidenceLevel;
	}

	/**
	 * 设置测点名称
	 *
	 * @param abnormalDTO 异常DTO对象
	 * @param abnormalDetails 异常详情列表
	 * <AUTHOR>
	 * @date 2025-01-17
	 */
	private void setMonitorName(AbnormalDTO abnormalDTO, List<AbnormalDetail> abnormalDetails) {
		if (Func.isEmpty(abnormalDetails)) {
			return;
		}

		// 获取所有测点ID并去重
		List<Long> monitorIds = abnormalDetails.stream()
			.map(AbnormalDetail::getMonitorId)
			.distinct()
			.collect(Collectors.toList());

		if (Func.isNotEmpty(monitorIds)) {
			// 批量查询测点信息
			List<Monitor> monitors = monitorService.listByIds(monitorIds);
			Map<Long, Monitor> monitorMap = monitors.stream()
				.collect(Collectors.toMap(Monitor::getId, Function.identity(), (k1, k2) -> k1));

			// 获取测点名称列表
			List<String> monitorNames = monitorIds.stream()
				.map(monitorId -> {
					Monitor monitor = monitorMap.get(monitorId);
					return monitor != null ? monitor.getName() : "";
				})
				.filter(Func::isNotEmpty)
				.collect(Collectors.toList());

			// 用逗号连接测点名称
			if (Func.isNotEmpty(monitorNames)) {
				abnormalDTO.setMonitorName(String.join(",", monitorNames));
			}
		}
	}

	@Override
	public boolean toFault(Long abnormalId, Long faultId) {
		Abnormal abnormal = this.getById(abnormalId);
		if (abnormal == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		abnormalDetailService.update(Wrappers.<AbnormalDetail>update().lambda()
			.set(AbnormalDetail::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()).eq(AbnormalDetail::getAbnormalId, abnormalId));
		abnormalRecordService.update(Wrappers.<AbnormalRecord>update().lambda()
			.set(AbnormalRecord::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()).eq(AbnormalRecord::getAbnormalId, abnormalId));
		abnormal.setStatus(AbnormalStatusEnum.IS_FAULT.getCode()).setIsFault(Func.toInt(StringPool.ONE)).setFaultId(faultId);
		return this.updateById(abnormal);
	}

	@Override
	public List<AbnormalDTO> listBy(List<String> tenantIds, List<Long> equipmentIds, Integer status) {
		List<Abnormal> abnormalList = this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(tenantIds), Abnormal::getTenantId, tenantIds)
			.in(ObjectUtil.isNotEmpty(equipmentIds), Abnormal::getEquipmentId, equipmentIds)
			.eq(ObjectUtil.isNotEmpty(status), Abnormal::getStatus, status)
			.list();
		return ObjectUtil.isEmpty(abnormalList) ? Collections.emptyList() : BeanUtil.copy(abnormalList, AbnormalDTO.class);
	}

	/**
	 * 设备监测分页查询
	 *
	 * @param query 查询条件，包含分页参数
	 * @return 分页结果
	 */
	@Deprecated
	@Override
	public IPage<AbnormalDTO> equipmentMonitorPage(EquipmentMonitorQueryVO query) {
		// 使用MyBatis-Plus的分页功能执行查询
		IPage<AbnormalDTO> page = baseMapper.equipmentMonitorPage(query, query);
		List<AbnormalDTO> list = page.getRecords();

		if (Func.isNotEmpty(list)) {
			// 提取所有设备ID
			List<Long> equipmentIds = list.stream()
				.map(AbnormalDTO::getEquipmentId)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.toList());

			// 批量查询设备信息
			List<Equipment> equipmentList = Func.isNotEmpty(equipmentIds) ?
				equipmentService.listByIds(equipmentIds) : Collections.emptyList();

			// 转换为Map便于快速查找
			Map<Long, Equipment> equipmentMap = equipmentList.stream()
				.collect(Collectors.toMap(Equipment::getId, Function.identity(), (k1, k2) -> k1));

			// 填充设备信息和枚举值
			list.forEach(dto -> {
				// 设置设备信息
				if (dto.getEquipmentId() != null) {
					Equipment equipment = equipmentMap.get(dto.getEquipmentId());
					if (Func.isNotEmpty(equipment)) {
						EquipmentDTO equipmentDTO = BeanUtil.copy(equipment, EquipmentDTO.class);
						equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()))
							.setPathName(equipmentDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
						dto.setEquipmentInfo(equipmentDTO);
					}
				}

				// 设置异常等级名称
				if (dto.getAbnormalLevel() != null) {
					dto.setAbnormalLevelName(AbnormalLevelEnum.getByCode(dto.getAbnormalLevel()).getName());
				}

				// 设置状态名称
				if (dto.getStatus() != null) {
					dto.setStatusName(AbnormalStatusEnum.getByCode(dto.getStatus()).getName());
				}
			});
		}

		return page;
	}

	/**
	 * 设备监测简化分页查询
	 *
	 * @param query 查询条件，包含分页参数
	 * @return 简化的分页结果
	 */
	@Override
	public IPage<EquipmentMonitorSimpleDTO> equipmentMonitorSimplePage(EquipmentMonitorQueryVO query) {
		// 使用MyBatis-Plus的分页功能执行查询
		IPage<EquipmentMonitorSimpleDTO> iPage = baseMapper.equipmentMonitorSimplePage(query, query);
		if (Func.isEmpty(iPage.getRecords())) {
			return iPage;

		}
		iPage.getRecords().forEach(dto -> {
			if (dto.getAbnormalLevel() == null) {
				dto.setAbnormalLevel(0);
			}
			String name = DictBizCache.getValue("abnormal_level", dto.getAbnormalLevel());
			dto.setAbnormalLevelName(name);
		});
		return iPage;
	}

	@Override
	public AbnormalDTO getByEquipmentId(@NonNull Long equipmentId) {
		List<Abnormal> abnormalList = this.lambdaQuery()
			.eq(Abnormal::getEquipmentId, equipmentId)
			.eq(Abnormal::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode())
			.list();
		if (Func.isEmpty(abnormalList)) {
			return null;
		}
		return this.detail(abnormalList.get(0).getId());
	}
}
