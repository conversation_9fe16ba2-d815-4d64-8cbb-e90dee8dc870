/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.file.dto.CommonDeleteResultDto;
import com.snszyk.zbusiness.file.dto.FileDto;
import com.snszyk.zbusiness.file.entity.KFile;
import com.snszyk.zbusiness.file.service.IFileService;
import com.snszyk.zbusiness.file.vo.FileAVo;
import com.snszyk.zbusiness.file.vo.FilePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@AllArgsConstructor
@Service
public class FileLogicService {

	private final IFileService fileService;

	@Transactional
	public FileDto saveOrUpdate(FileAVo v) {
		// 名称的唯一性校验
		String name = v.getName();
		fileService.checkName(v.getId(), name);
		KFile copy = BeanUtil.copy(v, KFile.class);
		boolean b = fileService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<FileDto> pageList(FilePageVo v) {
		IPage<FileDto> page = fileService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public FileDto detail(Long id) {
		// 通过ID查询数据信息
		FileDto dto = fileService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			KFile data = fileService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
			deleteResultDto.setName(data.getName());

			boolean b = fileService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


}
