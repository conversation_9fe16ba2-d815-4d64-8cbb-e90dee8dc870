/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.zbusiness.file.dto.FileShareDto;
import com.snszyk.zbusiness.file.entity.FileShare;
import com.snszyk.zbusiness.file.mapper.FileShareMapper;
import com.snszyk.zbusiness.file.service.IFileShareService;
import com.snszyk.zbusiness.file.vo.FileSharePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 文件分享 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@AllArgsConstructor
@Service
public class FileShareServiceImpl extends BaseServiceImpl<FileShareMapper, FileShare> implements IFileShareService {



    /**
     * 分页查询
     */
    @Override
    public IPage<FileShareDto> pageList(FileSharePageVo v) {
        return baseMapper.pageList(v);
    }

    /**
     * 详情
     */
    @Override
    public FileShareDto detail(Long id) {
        return baseMapper.detail(id);
    }

	@Override
	public Integer noReadCount(Long userId) {
		return baseMapper.noReadCount(userId);

	}

    @Override
    public Boolean read(Long shareId) {
		return baseMapper.read(shareId);

    }

}
