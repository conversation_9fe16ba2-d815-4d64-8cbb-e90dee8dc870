/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.zbusiness.file.dto.DirectoryDto;
import com.snszyk.zbusiness.file.dto.FileTreeDto;
import com.snszyk.zbusiness.file.dto.IdNameDto;
import com.snszyk.zbusiness.file.entity.Directory;
import com.snszyk.zbusiness.file.mapper.DirectoryMapper;
import com.snszyk.zbusiness.file.service.IDirectoryService;
import com.snszyk.zbusiness.file.vo.DirectoryPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件目录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@AllArgsConstructor
@Service
public class DirectoryServiceImpl extends BaseServiceImpl<DirectoryMapper, Directory> implements IDirectoryService {

	/**
	 * 名称校验
	 */
	@Override
	public void checkName(Long id, String name) {
		Integer count = lambdaQuery().eq(Directory::getName, name).ne(id != null, Directory::getId, id).count();
		if (count > 0) {
			throw new ServiceException("名称已存在");
		}
	}

	/**
	 * 分页查询
	 */
	@Override
	public IPage<DirectoryDto> pageList(DirectoryPageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public DirectoryDto detail(Long id) {
		return baseMapper.detail(id);
	}

	@Override
	public Directory getByIdIgnoreDel(Long id) {
		return baseMapper.getByIdIgnoreDel(id);

	}

	@Override
	public List<FileTreeDto> lazyTree(Long parentId, String tenantId, Boolean excludePlatform) {
		return baseMapper.lazyTree(parentId, tenantId,excludePlatform);
	}

	@Override
	public List<IdNameDto> listAllIdName() {
		return baseMapper.listAllIdName();

	}

	@Override
	public List<Directory> listByIdsIgnoreTenant(List<Long> dirList) {
		return baseMapper.listByIdsIgnoreTenant(dirList);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Boolean initTenantDir(String tenantId) {
		// 查询租户为 "000000" 的目录树
		List<Directory> sourceDirectories = lambdaQuery().eq(Directory::getTenantId, "000000").list();

		// 构建源目录的树形结构
		Map<Long, Directory> sourceDirectoryMap = sourceDirectories.stream()
			.collect(Collectors.toMap(Directory::getId, Function.identity()));

		// 复制树形结构并设置新的租户 ID
		for (Directory sourceDir : sourceDirectories) {
			if (sourceDir.getParentId() == 0) { // 根节点
				Directory newDir = copyDirectory(sourceDir, tenantId, null);
				copyChildren(sourceDir, newDir, sourceDirectoryMap, tenantId);
			}
		}
		return true;
	}

	@Override
	public List<Directory> listBy(String tenantId) {
		return this.lambdaQuery()
			.eq(Directory::getTenantId, tenantId)
			.list();

	}

	private Directory copyDirectory(Directory sourceDir, String tenantId, String parentAncestors) {
		Directory newDir = new Directory();
		newDir.setName(sourceDir.getName());
		newDir.setTenantId(tenantId); // 设置新的租户 ID
		newDir.setParentId(sourceDir.getParentId());
		// set sort
		newDir.setSort(sourceDir.getSort());
		if (parentAncestors == null) {
			newDir.setAncestors("0"); // 根节点的祖先为空
		} else {
			newDir.setAncestors(parentAncestors);
		}
		save(newDir); // 保存新的目录
		// 复制其他必要字段
		return newDir;
	}

	private void copyChildren(Directory sourceDir, Directory newDir, Map<Long, Directory> sourceDirectoryMap, String tenantId) {
		for (Directory child : sourceDirectoryMap.values()) {
			if (sourceDir.getId().equals(child.getParentId())) {
				String newAncestors = newDir.getAncestors().isEmpty() ? String.valueOf(newDir.getId()) : newDir.getAncestors() + "," + newDir.getId();
				Directory newChild = copyDirectory(child, tenantId, newAncestors);
				newChild.setParentId(newDir.getId()); // 设置新的父节点 ID
				updateById(newChild); // 更新新的目录
				copyChildren(child, newChild, sourceDirectoryMap, tenantId);
			}
		}
	}


}
