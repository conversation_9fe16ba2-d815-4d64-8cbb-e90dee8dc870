/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service.logic;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.common.utils.FileUtils;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.elasticsearch.service.IEsKnowledgeService;
import com.snszyk.elasticsearch.vo.EsKnowledgeVo;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.config.CommonConfig;
import com.snszyk.system.config.LlmConfig;
import com.snszyk.system.entity.Dict;
import com.snszyk.zbusiness.file.dto.*;
import com.snszyk.zbusiness.file.entity.Directory;
import com.snszyk.zbusiness.file.entity.KFile;
import com.snszyk.zbusiness.file.entity.Stat;
import com.snszyk.zbusiness.file.enums.FileTreeNodeType;
import com.snszyk.zbusiness.file.service.IDirectoryService;
import com.snszyk.zbusiness.file.service.IFileService;
import com.snszyk.zbusiness.file.service.IGraphApi;
import com.snszyk.zbusiness.file.service.IStatService;
import com.snszyk.zbusiness.file.vo.*;
import com.snszyk.zbusiness.llm.service.logic.LlmLogic;
import lombok.AllArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 文件目录 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@AllArgsConstructor
@Service
@Slf4j
public class DirectoryLogicService {

	private final IDirectoryService directoryService;
	private final IFileService filesService;

	private final SzykRedis redis;

	private final IEsKnowledgeService esKnowledgeService;

	private final IStatService statService;

	private final IAttachService attachService;
	private final LlmConfig llmConfig;

	private final IGraphApi graphApi;

	private final LlmLogic llmLogic;

	private final CommonConfig commonConfig;

	@Transactional
	public DirectoryDto saveOrUpdate(DirectoryAVo v) {
		Long parentId = v.getParentId();
		if (isNull(parentId)) {
			v.setParentId(0l);
		}
		// 名称的唯一性校验
		String name = v.getName();
		Directory same = directoryService.lambdaQuery().eq(Directory::getParentId, v.getParentId()).eq(Directory::getName, name).orderByDesc(Directory::getId).last("limit 1").one();
		// 如果新增的目录是重名的，则新增后在名称后增加（1）、（2）····（N）
		if (same != null) {
			int i = 1;
			while (true) {
				String newName = name + "(" + i + ")";
				same = directoryService.lambdaQuery().eq(Directory::getParentId, v.getParentId()).eq(Directory::getName, newName).orderByDesc(Directory::getId).last("limit 1").one();
				if (same == null) {
					v.setName(newName);
					break;
				}
				i++;
			}
		}

		Directory copy = BeanUtil.copy(v, Directory.class);
		// 设置祖籍列表
		if (copy != null && copy.getParentId() == 0) {
			copy.setAncestors("0");
		} else {
			Directory parent = directoryService.getByIdIgnoreDel(parentId);
			copy.setAncestors(parent.getAncestors() + "," + parent.getId());
		}
		boolean b = directoryService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<DirectoryDto> pageList(DirectoryPageVo v) {
		IPage<DirectoryDto> page = directoryService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public DirectoryDto detail(Long id) {
		// 通过ID查询数据信息
		DirectoryDto dto = directoryService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<DirectoryDVo> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		List<Long> removeEsFileIds = new ArrayList<>();
		for (DirectoryDVo vo : ids) {
			Long id = vo.getId();
			String type = vo.getType();
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);
			// 如果是删除的文件
			if (type.equals(CommonConstant.TWO_STR)) {
				KFile data = filesService.getByIdIgnoreDel(id);
				if (data == null) {
					throw new ServiceException("系统异常,数据不存在");
				}
				deleteResultDto.setName(data.getName());
				filesService.removeById(id);
				removeEsFileIds.add(id);
				deleteResultDto.setResult(true);
			}
			// 如果是删除的目录,删除下面的目录和目录里的文件
			if (type.equals(CommonConstant.ONE_STR)) {
				Directory data = directoryService.getByIdIgnoreDel(id);
				if (data == null) {
					throw new ServiceException("系统异常,数据不存在");
				}
				deleteResultDto.setName(data.getName());
				// 删除目录下的文件
				List<KFile> files = filesService.lambdaQuery().eq(KFile::getDirectoryId, id).list();
				for (KFile file : files) {
					filesService.removeById(file.getId());
					removeEsFileIds.add(file.getId());
				}
				// 删除目录
				directoryService.removeById(id);
				// 子级的目录
				List<Directory> list = directoryService.lambdaQuery().like(Directory::getAncestors, id).list();
				if (Func.isNotEmpty(list)) {
					directoryService.removeByIds(list.stream().map(BaseEntity::getId).collect(Collectors.toList()));
				}
				// 子级目录下的文件
				List<Long> collect = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
				if (Func.isNotEmpty(collect)) {
					List<KFile> fileList = filesService.lambdaQuery().in(KFile::getDirectoryId, collect).list();
					if (!fileList.isEmpty()) {
						List<Long> fileIds = fileList.stream().map(BaseEntity::getId).collect(Collectors.toList());
						filesService.removeByIds(fileIds);
						removeEsFileIds.addAll(fileIds);
					}
				}
				deleteResultDto.setResult(true);
			}
		}
		// 同步删除es中的数据
		if (Func.isNotEmpty(removeEsFileIds)) {
			// 同步删除es中的数据
			Integer remove = esKnowledgeService.removeByIds(removeEsFileIds);
			log.info("删除es中的文件数据：" + remove);
			//<=0
			if (remove <= 0) {
//				throw new ServiceException("删除es中的文件数据失败");
			}
		}
		// 同步删除向量数据库中的数据
		llmLogic.removeEmbeddingByFileId(removeEsFileIds);
		return result;
	}


	public Boolean rename(DirectoryRenameVo v) {
		// 如果是目录的命名
		if (v.getType().equals(CommonConstant.ONE_STR)) {
			directoryService.checkName(v.getId(), v.getName());
			directoryService.lambdaUpdate().eq(Directory::getId, v.getId()).set(Directory::getName, v.getName())
				.set(Directory::getUpdateTime, LocalDateTime.now())
				.update();

		} else {
			// 如果是文件的重命名
			filesService.checkName(v.getId(), v.getName());
			filesService.lambdaUpdate().eq(KFile::getId, v.getId()).set(KFile::getName, v.getName())
				.set(KFile::getUpdateTime, LocalDateTime.now())
				.update();
			// 更新es中的文件名
			esKnowledgeService.rename(v.getId(), v.getName());
		}
		return true;
	}

	public Boolean checkHasFile(List<Long> ids) {
		for (Long id : ids) {
			// 所有的子级的目录
			List<Directory> list = directoryService.lambdaQuery().like(Directory::getAncestors, id).list();
			List<Long> dirIds = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
			dirIds.add(id);
			Integer count = filesService.lambdaQuery().in(KFile::getDirectoryId, dirIds).count();
			if (count > 0) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 文件和目录上传
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Synchronized
	public Boolean upload(FileUploadVo v) {
		Long attachId = v.getAttachId();
		String path = v.getPath();
		Long directoryId = v.getDirectoryId();
		String batchId = v.getBatchId();
		String tenantId = AuthUtil.getTenantId();
		// 1.是文件还是有文件夹 路径的分隔符是/
		if (!path.contains("/")) {
			// 是文件
			KFile file = new KFile();
			file.setTenantId(tenantId);
			file.setName(path);
			file.setAttachId(attachId);
			file.setDirectoryId(directoryId);
			// 名称的唯一性校验
			String name = v.getPath();
			KFile same = filesService.lambdaQuery().eq(KFile::getDirectoryId, v.getDirectoryId()).eq(KFile::getName, name).orderByDesc(KFile::getId).last("limit 1").one();
			// 如果新增的文件是重名的，则新增后在名称后增加（1）、（2）····（N）
			if (same != null) {
				int i = 1;
				while (true) {
					String baseName = name.substring(0, name.lastIndexOf('.'));
					String extension = name.substring(name.lastIndexOf('.'));
					String newName = baseName + "(" + i + ")" + extension;
					same = filesService.lambdaQuery().eq(KFile::getDirectoryId, v.getDirectoryId()).eq(KFile::getName, newName).orderByDesc(KFile::getId).last("limit 1").one();
					if (same == null) {
						file.setName(newName);
						break;
					}
					i++;
				}
			}
			// 文件add
			saveAndSyncEs(file);
			// 更新目录的更新时间
			updateDirUpdateTime(file);
		} else {
			// 如果是有文件夹的
			String[] split = path.split("/");
			// 获取path最后一个/之前的所有字符
			String dirPath = path.substring(0, path.lastIndexOf("/"));
			String redisPrefix = "fileCollect:upload:";
//			String lockKey = redisPrefix + batchId;
//			Long redisDirID = redis.hGet(redisPrefix + batchId, dirPath);
			String[] dirList = dirPath.split("/");
			String dir = dirList[0];
			// 当前的目录id
			Long dirId = null;
			// 先处理第一级的目录
			Long redisDirId = redis.hGet(redisPrefix + batchId, dir);
			// 本批次没有第一级的目录,新建第一级的目录
			if (redisDirId == null) {
				DirectoryAVo directory = new DirectoryAVo();
				directory.setName(dir);
				directory.setParentId(directoryId);
				DirectoryDto directoryDtoFirst = ((DirectoryLogicService) AopContext.currentProxy()).saveOrUpdate(directory);
				redis.hSet(redisPrefix + batchId, dir, directoryDtoFirst.getId());
				dirId = directoryDtoFirst.getId();
			} else {
				dirId = redisDirId;
			}
			// 其他层级的目录
			if (dirList.length > 1) {
				for (int i = 1; i < dirList.length; i++) {
					dir = dir + "/" + dirList[i];
					redisDirId = redis.hGet(redisPrefix + batchId, dir);
					if (redisDirId == null) {
						DirectoryAVo directory = new DirectoryAVo();
						directory.setName(dirList[i]);
						directory.setParentId(dirId);
						DirectoryDto directoryDto = ((DirectoryLogicService) AopContext.currentProxy()).saveOrUpdate(directory);
						redis.hSet(redisPrefix + batchId, dir, directoryDto.getId());
						dirId = directoryDto.getId();
					} else {
						dirId = redisDirId;
					}
				}
			}
			// 添加文件
			KFile file = new KFile();
			file.setName(split[split.length - 1]);
			file.setAttachId(attachId);
			file.setDirectoryId(dirId);
			file.setTenantId(tenantId);
			saveAndSyncEs(file);
			// 更新目录的更新时间
			updateDirUpdateTime(file);
		}
		return true;
	}

	@Transactional(rollbackFor = Exception.class)
	public void saveAndSyncEs(KFile file) {
		boolean save = filesService.save(file);
		if (!save) {
			throw new RuntimeException("文件保存失败");
		}
		// 添加一个stat
		Stat stat = new Stat();
		stat.setId(file.getId());
		statService.save(stat);
		execFile(file);
	}

	public void execFile(KFile file) {
		// 附件内容抽取使用多线程处理
		CompletableFuture<String> contentFuture = CompletableFuture.supplyAsync(() -> {
			try {
				return attachService.readOffice(file.getAttachId());
			} catch (Exception e) {
				log.error("附件内容抽取失败", e);
				throw new RuntimeException(e);
			}
		});

		// 同步处理第一步, 第二步和第三步  thenAccept 也是异步执行的
		contentFuture.thenAccept(str -> {
			if (Func.isBlank(str)) {
				str = "";
			}
			log.info("文件内容:{}", str);
			//es同步 没有文本数据的时候同步文件名
			syncToElasticsearch(file, str);
			if (Func.isNotBlank(str)) {
				syncToVectorDatabase(file, str);
				syncToKnowledgeGraph(file);
			}
		}).exceptionally(ex -> {
			log.error("附件内容抽取失败", ex);
			return null;
		});
	}

	private void syncToElasticsearch(KFile file, String content) {
		CompletableFuture.runAsync(() -> {
			try {
				EsKnowledgeVo esKnowledge = new EsKnowledgeVo();
				esKnowledge.setId(file.getId());
				esKnowledge.setFileName(file.getName());
				esKnowledge.setAttachId(file.getAttachId());
				esKnowledge.setFileContent(content);
				esKnowledge.setTenantId(file.getTenantId());
				Integer saveEs = esKnowledgeService.save(esKnowledge);
				log.info("第一步:同步到es:文档id{},同步的结果:{}", file.getId(), saveEs);
			} catch (Exception e) {
				log.error("同步到Elasticsearch失败", e);
			}
		});
	}

	private void syncToVectorDatabase(KFile file, String content) {
		CompletableFuture.runAsync(() -> {
			try {
				HashMap<String, Object> embeddingMap = new HashMap<>();
				embeddingMap.put("source_name", file.getName());
				embeddingMap.put("source_id", file.getId().toString());
				embeddingMap.put("full_text", content);
				embeddingMap.put("tenant_id", file.getTenantId());

				String post = HttpUtil.post(llmConfig.getUrl() + llmConfig.getEmbeddingPath(), JSONUtil.toJsonStr(embeddingMap));
				log.info("向量化的处理结果:{}", post);
				log.info("第二步:同步到向量数据库:文档id{},同步的结果:{}", file.getId(), post);
			} catch (Exception e) {
				log.error("同步到向量数据库失败", e);
			}
		});
	}

	private void syncToKnowledgeGraph(KFile file) {
		CompletableFuture.runAsync(() -> {
			try {
				Attach attach = attachService.getById(file.getAttachId());
				if (attach != null) {
					FilePushVo build = FilePushVo.builder().knowledgeDocId(file.getId()).docUrl(attach.getLink()).fileName(file.getName()).build();
					Boolean b = graphApi.filePush(build);
					log.info("第三步:同步到知识图谱:文档id{},同步的结果:{}", file.getId(), b);
				}
			} catch (Exception e) {
				log.error("同步到知识图谱失败", e);
			}
		});
	}


	/**
	 * 更新目录的更新时间
	 *
	 * @param file
	 */
	private void updateDirUpdateTime(KFile file) {

		Long directoryId = file.getDirectoryId();
		// 如果文件是挂在根目录下面的
		if (isNull(directoryId) || directoryId == 0) {
			return;
		}
		Directory directory = directoryService.getById(directoryId);
		List<String> list = new ArrayList<>();
		list.add(directoryId.toString());
		if (nonNull(directory)) {
			String ancestors = directory.getAncestors();
			String[] split = ancestors.split(",");
			list.addAll(Arrays.asList(split));
		}
		if (Func.isEmpty(list)) {
			return;
		}
		directoryService.lambdaUpdate().in(Directory::getId, list.stream().map(Long::valueOf).filter(e -> e != 0).collect(Collectors.toList())).set(Directory::getUpdateTime, LocalDateTime.now()).update();

	}

	public List<FileTreeDto> lazyTree(Long parentId, String tenantIdParam) {
		Boolean excludePlatform = false;
		String tenantId = "";
		// 平台的可以选择租户,其他的按登录的租户
		if (Func.isNotBlank(tenantIdParam) && "000000".equals(AuthUtil.getTenantId())) {
			//企业内容库管理员查询全部的目录
			if (tenantIdParam.equals("all")) {
				tenantId = "";
				excludePlatform = true;
			} else {
				tenantId = tenantIdParam;
			}
		} else {
			tenantId = AuthUtil.getTenantId();
		}
		// 查询下级的目录
		List<FileTreeDto> directoryDtos = directoryService.lazyTree(parentId, tenantId, excludePlatform);
		for (FileTreeDto directoryDto : directoryDtos) {
			directoryDto.setHasChildren(directoryDto.getHasChildrenDir() || directoryDto.getHasChildrenFile());
			directoryDto.setParentId(parentId);
		}
		// 查询下级的文件
		List<FileTreeDto> fileTreeDtos = filesService.listData(parentId, tenantId, excludePlatform);
		directoryDtos.addAll(fileTreeDtos);
		fileTreeDtos.forEach(e -> e.setParentId(parentId));
		//先根据租户名排正序,在根据sort排倒序
		//处理排序的空指针异常

		directoryDtos.sort(Comparator
			.comparing(FileTreeDto::getTenantName, Comparator.nullsFirst(String::compareTo))
			.thenComparing(FileTreeDto::getSort, Comparator.nullsFirst(Integer::compareTo).reversed()));
		for (int i = 0; i < directoryDtos.size(); i++) {
			// set index
			directoryDtos.get(i).setIndex(i + 1);
		}
		return directoryDtos;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean drag(FileMoveVo v) {
		Long id = v.getId();
		String type = v.getType();
		Long parentId = v.getParentId();
		List<FileMoveItemVo> itemList = v.getItemList();
		// 1.如果拖拽的是文件
		if (FileTreeNodeType.FILE.getKey().equals(type)) {
			// 1.1 获取所有需要移动的文件
			boolean update = filesService.lambdaUpdate().eq(BaseEntity::getId, id).set(KFile::getDirectoryId, parentId).update();
			if (!update) {
				throw new ServiceException("系统异常,文件移动失败");
			}
		} else {
			// 2.如果拖拽的是目录
			Directory directory = directoryService.getById(id);
			if (isNull(directory)) {
				throw new ServiceException("系统异常,目录不存在");
			}
			String ancestors;
			Directory parentDir = directoryService.getById(parentId);
			if (isNull(parentDir) || "0".equals(parentId.toString())) {
				ancestors = "0";
			} else {
				ancestors = parentDir.getAncestors();
			}
			String newAncestors = ancestors + "," + parentId;
			// 更新这个目录的信息
			directoryService.lambdaUpdate().eq(BaseEntity::getId, id).set(Directory::getAncestors, newAncestors).set(Directory::getParentId, parentId).update();
			// 更新这个目录的子集目录ancestors
			List<Directory> childrenDirList = directoryService.lambdaQuery().like(Directory::getAncestors, id).list();
			if (Func.isNotEmpty(childrenDirList)) {
				String oldAncestors = directory.getAncestors() + "," + id;
				String childrenAncestors = newAncestors + "," + id;
				childrenDirList.forEach(e -> {
					e.setAncestors(e.getAncestors().replace(oldAncestors, childrenAncestors));
					directoryService.lambdaUpdate().eq(BaseEntity::getId, e.getId()).set(Directory::getAncestors, e.getAncestors())
//						.set(Directory::getParentId, parentId)
						.update();
				});
			}
		}

		// 3.sort处理
		for (int i = 0; i < itemList.size(); i++) {
			FileMoveItemVo item = itemList.get(i);
			if (FileTreeNodeType.FILE.getKey().equals(item.getType())) {
				KFile file = filesService.getById(item.getId());
				if (file == null) {
					throw new ServiceException("系统异常,文件不存在");
				}
				file.setSort(itemList.size() - i);
				filesService.updateById(file);
			} else {
				Directory directory = directoryService.getById(item.getId());
				if (directory == null) {
					throw new ServiceException("系统异常,目录不存在");
				}
				directory.setSort(itemList.size() - i);
				directoryService.updateById(directory);
			}


		}
		return true;
	}

	public void importDirectoryExcel(MultipartFile file) throws IOException {
		// easyexcel的导入
		EasyExcel.read(file.getInputStream(), DirectoryExcel.class, new DirectoryExcelListener(directoryService)).extraRead(CellExtraTypeEnum.MERGE).sheet().doRead();

	}

	public List<SpecialProductDto> getSpecialProducts() {
		// 在系统字典中获取
		List<Dict> list = DictCache.getList(DictEnum.SPECIAL_PRODUCT.getName());
		if(Func.isEmpty(list)){
			return new ArrayList<>();
		}
		List<SpecialProductDto> result = new ArrayList<>();
		for (Dict dict : list) {
			SpecialProductDto dto = new SpecialProductDto();
			dto.setName(dict.getDictValue());
			dto.setLink(dict.getDictKey());
			result.add(dto);
		}
		return result;
	}

	public void downloadFiles(List<FileTreeDto> fileTreeDtos, String downloadTenantId, HttpServletResponse response) {
		// 判断是否按租户下载
		boolean isTenantDownload = Func.isNotBlank(downloadTenantId);
		if (isTenantDownload) {
			fileTreeDtos = lazyTree(0L, downloadTenantId);
		}
		//是否是单个文件的下载,这里是单独处理的
		if (fileTreeDtos.size() == 1 && FileTreeNodeType.FILE.getKey().equals(fileTreeDtos.get(0).getType())) {
			// 下载文件
			KFile file = filesService.getById(fileTreeDtos.get(0).getId());
			if (file != null) {
				Long attachId = file.getAttachId();
				attachService.download(String.valueOf(attachId), WebUtil.getRequest(), response);
				return;
			} else {
				throw new ServiceException("文件不存在");
			}
		}
		// 初始化目录和文件ID列表
		List<Long> dirIds = new ArrayList<>();
		List<Long> fileIds = new ArrayList<>();

		// 区分目录和文件
		for (FileTreeDto dto : fileTreeDtos) {
			if (FileTreeNodeType.FILE.getKey().equals(dto.getType())) {
				fileIds.add(dto.getId());
			} else {
				dirIds.add(dto.getId());
			}
		}

		// 查询所有目录的名称
		List<IdNameDto> idNameDtos = directoryService.listAllIdName();
		Map<Long, String> idNameMap = idNameDtos.stream()
			.collect(Collectors.toMap(IdNameDto::getId, IdNameDto::getName));

		// 查询所有目录及其子目录
		Map<Long, Directory> dirMap = new HashMap<>();
		if (!dirIds.isEmpty()) {
			List<Directory> dirList = directoryService.lambdaQuery().in(Directory::getId, dirIds).list();
			dirMap = dirList.stream().collect(Collectors.toMap(Directory::getId, e -> e, (k1, k2) -> k1));

			// 查询子目录
			List<Long> dirIdsTemp = new ArrayList<>();
			for (Long dirId : dirIds) {
				List<Directory> childrenDirList = directoryService.lambdaQuery().like(Directory::getAncestors, dirId).list();
				if (Func.isNotEmpty(childrenDirList)) {
					Map<Long, Directory> childrenDirMap = childrenDirList.stream()
						.collect(Collectors.toMap(Directory::getId, e -> e, (k1, k2) -> k1));
					dirIdsTemp.addAll(childrenDirMap.keySet());
					dirMap.putAll(childrenDirMap);
				}
			}
			dirIds.addAll(dirIdsTemp);
		}

		// 查询所有文件
		List<KFile> fileList = new ArrayList<>();
		if (!dirIds.isEmpty()) {
			fileList.addAll(filesService.lambdaQuery().in(KFile::getDirectoryId, dirIds).list());
		}
		if (!fileIds.isEmpty()) {
			fileList.addAll(filesService.lambdaQuery().in(KFile::getId, fileIds).list());
		}
		Map<Long, KFile> fileMap = fileList.stream()
			.collect(Collectors.toMap(KFile::getId, e -> e, (k1, k2) -> k1));

		// 创建临时目录
		String tempPath = commonConfig.getTempFilePath();
		String formatDate = DateUtil.format(new Date(), "yyyy-MM-dd");
		String uuid = IdUtil.fastSimpleUUID();
		String rootPath = tempPath + formatDate + File.separator + uuid + File.separator;
		File rootDir = new File(rootPath);
		if (!rootDir.exists()) {
			rootDir.mkdirs();
		}

		// 下载目录
		for (Long dirId : dirIds) {
			Directory directory = dirMap.get(dirId);
			if (directory != null) {
				String name = directory.getName();
				String parentDir = Arrays.stream(directory.getAncestors().split(","))
					.filter(e -> !Objects.equals(e, "0"))
					.map(e -> idNameMap.get(Long.valueOf(e)))
					.collect(Collectors.joining(File.separator));
				File dir = Func.isNotBlank(parentDir) ?
					new File(rootPath + parentDir + File.separator + name) :
					new File(rootPath + name);
				if (!dir.exists()) {
					dir.mkdirs();
				}
			}
		}

		// 下载文件
		for (Map.Entry<Long, KFile> entry : fileMap.entrySet()) {
			KFile file = entry.getValue();
			String name = file.getName();
			Long directoryId = file.getDirectoryId();
			Directory directory = dirMap.get(directoryId);
			if (directory == null) {
				directory = directoryService.getById(directoryId);
			}

			File dir;
			if (directory != null) {
				String parentDir = Arrays.stream(directory.getAncestors().split(","))
					.filter(e -> !Objects.equals(e, "0"))
					.map(e -> idNameMap.get(Long.valueOf(e)))
					.collect(Collectors.joining(File.separator));
				dir = Func.isNotBlank(parentDir) ?
					new File(rootPath + parentDir + File.separator + directory.getName()) :
					new File(rootPath + directory.getName());
				if (!dir.exists()) {
					dir.mkdirs();
				}
			} else {
				dir = new File(rootPath);
			}

			Attach attach = attachService.getById(file.getAttachId());
			if (attach != null) {
				File newFile = new File(dir.getPath() + File.separator + name);
				HttpUtil.downloadFile(attach.getLink(), newFile);
			}
		}

		// 压缩文件并下载
		java.io.File zipFile = new File(tempPath + formatDate + File.separator + uuid + ".zip");
		ZipUtil.zip(rootPath, zipFile.getPath(), false);
		FileUtils.downloadZip(zipFile, response);
	}

	public IPage<FileDto> myUploads(DirectoryPageVo v) {
		v.setCreateUser(AuthUtil.getUserId());
		IPage<FileDto> fileDtoIPage = filesService.myUploads(v);
		//添加目录名称
		List<FileDto> fileDtoList = fileDtoIPage.getRecords();
		if (Func.isEmpty(fileDtoList)) {
			return fileDtoIPage;
		}
		List<Directory> directoryList = directoryService.lambdaQuery().in(Directory::getId, fileDtoList.stream().map(FileDto::getDirectoryId).distinct().collect(Collectors.toList())).list();
		List<String> allDirIds = directoryList.stream().map(e -> e.getAncestors().split(",")).flatMap(Arrays::stream).filter(e -> !Objects.equals(e, "0"))
			.distinct().collect(Collectors.toList());
		//查询所有的父级目录
		List<Directory> parnetList = new ArrayList<>();
		if (Func.isNotEmpty(allDirIds)) {
			parnetList = directoryService.lambdaQuery().in(Directory::getId, allDirIds).list();
		}
		Map<Long, Directory> dirMap = parnetList.stream().collect(Collectors.toMap(Directory::getId, e -> e, (k1, k2) -> k1));
		dirMap.putAll(directoryList.stream().collect(Collectors.toMap(Directory::getId, e -> e, (k1, k2) -> k1)));
		//添加目录名称
		fileDtoList.forEach(e -> {
			if (e.getDirectoryId() != null && e.getDirectoryId() != 0) {
				Directory directory = dirMap.get(e.getDirectoryId());
				if (directory != null) {
					String[] split = directory.getAncestors().split(",");
					String parentDir = Arrays.stream(split).filter(e1 -> !Objects.equals(e1, "0")).map(e1 -> dirMap.get(Long.valueOf(e1)).getName()).collect(Collectors.joining("-"));
					if (Func.isNotBlank(parentDir)) {
						e.setDirectoryName("知识空间-" + parentDir + "-" + directory.getName());
					} else {
						e.setDirectoryName(Func.isBlank(directory.getName()) ? "知识空间" : "知识空间" + "-" + directory.getName());
					}
				}
			} else {
				e.setDirectoryName("知识空间");
			}
		});
		return fileDtoIPage;
	}

	public List<FileDto> latestFiles() {
		String tenantId = AuthUtil.getTenantId();
		return filesService.latestFiles(tenantId);
	}

	/**
	 * 查询目录树
	 *
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<Tree<Long>> listTree() {
		final List<Directory> directoryList = directoryService.listBy(AuthUtil.getTenantId());
		if (ObjectUtil.isEmpty(directoryList)) {
			return Collections.emptyList();
		}
		directoryList.sort(Comparator
			.comparing(Directory::getSort, Comparator.nullsLast(Integer::compareTo).reversed()));
		return TreeUtil.build(directoryList, SzykConstant.TOP_PARENT_ID, (node, tree) -> {
			tree.setId(node.getId());
			tree.setName(node.getName());
			tree.setParentId(node.getParentId());
		});
	}

	public List<TenantStatVo> tenantStat(String tenantName) {
		return filesService.tenantStat(tenantName);
	}

	public Boolean allSyncVector() {
		List<KFile> fileDtos = filesService.listAll();
		for (KFile fileDto : fileDtos) {
			CompletableFuture.runAsync(() -> {
				try {
					// 同步删除向量数据库中的数据
					llmLogic.removeEmbeddingByFileId(Collections.singletonList(fileDto.getId()));
					String content = attachService.readOffice(fileDto.getAttachId());
					syncToVectorDatabase(fileDto, content);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			});
		}
		return true;
	}

	public Boolean allSyncEs() {
		List<KFile> fileDtos = filesService.listAll();
		for (KFile fileDto : fileDtos) {
			CompletableFuture.runAsync(() -> {
				try {
					// 同步删除Es数据库中的数据
					Integer remove = esKnowledgeService.removeByIds(Collections.singletonList(fileDto.getId()));
					String content = attachService.readOffice(fileDto.getAttachId());
					syncToElasticsearch(fileDto, content);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			});
		}
		return true;
	}

	/**
	 * 移动目录时排除的目录id
	 *
	 * @param
	 * @return
	 */
	public List<Long> excludeIds(List<Long> ids) {
		if (Func.isEmpty(ids)) {
			return Collections.emptyList();
		}
		List<Directory> directories = directoryService.listByIds(ids);
		List<Long> excludeIds = new ArrayList<>();
		for (Directory directory : directories) {
			excludeIds.add(directory.getId());
			List<Directory> children = directoryService.lambdaQuery().like(Directory::getAncestors, directory.getId()).list();
			if (Func.isNotEmpty(children)) {
				excludeIds.addAll(children.stream().map(Directory::getId).collect(Collectors.toList()));
			}
		}
		return excludeIds;
	}

	public Boolean moveV2(FileMoveVoV2 v) {

		Long parentId = v.getParentId();
		String anscestors = "";
		Directory parentDir = directoryService.getById(parentId);
		if (parentDir == null) {
			anscestors = "0";
		} else {
			anscestors = parentDir.getAncestors() + "," + parentId;
		}
		List<FileTreeDto> fileTreeDtos = v.getFileTreeDtos();
		//需要移动的目录的ids
		List<Long> needMoveDirIds = new ArrayList<>();
		//需要移动 的文件的ids
		List<Long> needMoveFileIds = new ArrayList<>();

		//文件
		List<Long> fileIds = fileTreeDtos.stream().filter(e -> FileTreeNodeType.FILE.getKey().equals(e.getType())).map(FileTreeDto::getId).collect(Collectors.toList());
		//目录
		List<Long> dirIds = fileTreeDtos.stream().filter(e -> FileTreeNodeType.DIR.getKey().equals(e.getType())).map(FileTreeDto::getId).collect(Collectors.toList());
		List<Long> excludeIds = excludeIds(dirIds);
		//目录的查询
		List<Directory> directories = new ArrayList<>();
		if (Func.isNotEmpty(dirIds)) {
			directories = directoryService.lambdaQuery().in(Directory::getId, dirIds).list();
		}
		//移动的目录map
		Map<Long, Directory> dirMap = directories.stream().collect(Collectors.toMap(Directory::getId, e -> e, (k1, k2) -> k1));
		for (Map.Entry<Long, Directory> entry : dirMap.entrySet()) {
			Directory directory = entry.getValue();
			Directory value = entry.getValue();
			String ancestors = directory.getAncestors();
			ancestors.split(",");
			String[] split = ancestors.split(",");
			Optional<String> any = Arrays.stream(split).filter(e -> !Objects.equals(e, "0") && dirMap.containsKey(Long.valueOf(e))).findAny();
			if (any.isPresent()) {
				continue;
			}
			needMoveDirIds.add(value.getId());
		}
		List<FileDto> fileDtos = filesService.listDetailByIds(fileIds);
		for (FileDto dto : fileDtos) {
			Long directoryId = dto.getDirectoryId();
			if (excludeIds.contains(directoryId)) {
				continue;
			}
			needMoveFileIds.add(dto.getId());
		}
		//目录移动
		for (Long needMoveDirId : needMoveDirIds) {
			Directory directory = dirMap.get(needMoveDirId);
			if (directory != null) {
				directory.setParentId(parentId);
				directory.setAncestors(anscestors);
				directoryService.updateById(directory);
			}
			//子级的目录的ancestors更新
			List<Directory> list = directoryService.lambdaQuery().like(Directory::getAncestors, needMoveDirId).list();
			if (Func.isNotEmpty(list)) {
				for (Directory child : list) {
					String oldAncestors = directory.getAncestors() + "," + needMoveDirId;
					String childrenAncestors = anscestors + "," + needMoveDirId;
					child.setAncestors(child.getAncestors().replace(oldAncestors, childrenAncestors));
					directoryService.lambdaUpdate().eq(BaseEntity::getId, child.getId()).set(Directory::getAncestors, child.getAncestors())
//						.set(Directory::getParentId, parentId)
						.update();
				}
			}

		}
		//文件移动
		if (!needMoveFileIds.isEmpty()) {
			filesService.lambdaUpdate().in(BaseEntity::getId, needMoveFileIds).set(KFile::getDirectoryId, parentId).update();
		}
		return true;
	}
}
