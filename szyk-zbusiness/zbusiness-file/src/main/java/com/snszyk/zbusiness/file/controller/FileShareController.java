/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.file.dto.FileShareDto;
import com.snszyk.zbusiness.file.service.logic.FileShareLogicService;
import com.snszyk.zbusiness.file.vo.FileShareAVo;
import com.snszyk.zbusiness.file.vo.FileSharePageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 文件分享 控制器
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("fileCollect/fileshare")
@Api(value = "文件分享", tags = "文件分享接口")
public class FileShareController extends SzykController {

    private final FileShareLogicService fileShareLogicService;



    /**
     * 分享
     */
    @PostMapping("/share")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分享", notes = "FileShareVo")
    public R<Boolean> share(@RequestBody @Valid FileShareAVo v) {
		Boolean baseCrudDto = fileShareLogicService.share(v);
        return R.data(baseCrudDto);
    }


	/**
	 * 分页
	 */
	@GetMapping("/noReadCount")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "未读的消息的数量", notes = "")
	public R<Integer> noReadCount() {
		Integer i = fileShareLogicService.noReadCount();
		return R.data(i);
	}

	/**
	 * 分页
	 */
	@GetMapping("/msgPage")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "消息分页", notes = "FileSharePageVo")
	public R<IPage<FileShareDto>> msgPage(FileSharePageVo v) {
		IPage<FileShareDto> pageQueryResult = fileShareLogicService.msgPage(v);
		return R.data(pageQueryResult);
	}


    @GetMapping("/read")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "已读", notes = "id")
    public R<Boolean> read(String shareId) {
		Boolean result = fileShareLogicService.read(Long.valueOf(shareId));
        return R.data(result);
    }
}
