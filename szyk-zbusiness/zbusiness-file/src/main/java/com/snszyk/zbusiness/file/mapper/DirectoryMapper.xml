<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.file.mapper.DirectoryMapper">

        <sql id="selectData">
        select t.* from k_directory t
    </sql>
    <select id="pageList" resultType="com.snszyk.zbusiness.file.dto.DirectoryDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.zbusiness.file.dto.DirectoryDto">
        <include refid="selectData"/>
       where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="getByIdIgnoreDel" resultType="com.snszyk.zbusiness.file.entity.Directory">
        select t.* from k_directory t where t.id=#{id}
    </select>
    <select id="lazyTree" resultType="com.snszyk.zbusiness.file.dto.FileTreeDto">
        SELECT t.id,
        t.tenant_id,
        t.name,
        t.parent_id,
        t.create_user,
        t.create_dept,
        t.create_time,
        t.update_user,
        t.update_time,
        t.sort,
        u.real_name AS "create_user_name",
        t1.tenant_name,
        1 as type,
        (SELECT CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
        FROM k_directory
        WHERE parent_id = t.id
        and is_deleted = 0) AS "has_children_dir",
        (SELECT CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
        FROM k_file
        WHERE directory_id = t.id
        and is_deleted = 0) AS "has_children_file"
        FROM k_directory t
        left join szyk_user u on u.id = t.create_user
        left join szyk_tenant t1 on t1.tenant_id = t.tenant_id
        WHERE t.parent_id = #{parentId}
        AND t.is_deleted = 0
        <if test="tenantId != null and tenantId != ''">
            and t.tenant_id = #{tenantId}
        </if>
        <if test="excludePlatform != null and excludePlatform ==true">
            and t.tenant_id != '000000'

        </if>
        and t1.tenant_id is not null

        order by t1.tenant_name asc ,t.sort desc,t.create_time desc
    </select>
    <select id="listAllIdName" resultType="com.snszyk.zbusiness.file.dto.IdNameDto">
        select id,name from k_directory where is_deleted = 0
    </select>
    <select id="listByIdsIgnoreTenant" resultType="com.snszyk.zbusiness.file.entity.Directory"
            parameterType="java.lang.Long">

        select * from k_directory where id in
        <foreach collection="dirList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        and is_deleted = 0
    </select>

</mapper>
