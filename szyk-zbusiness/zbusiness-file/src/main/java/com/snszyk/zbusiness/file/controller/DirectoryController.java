/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.file.dto.*;
import com.snszyk.zbusiness.file.service.logic.DirectoryLogicService;
import com.snszyk.zbusiness.file.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 文件目录 控制器
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("fileCollect/directory")
@Api(value = "文件收集目录", tags = "文件收集目录")
public class DirectoryController extends SzykController {

	private final DirectoryLogicService directoryLogicService;


	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "目录保存", notes = "DirectoryVo")
	public R<DirectoryDto> save(@RequestBody @Valid DirectoryAVo v) {
		DirectoryDto baseCrudDto = directoryLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}


	@GetMapping("/lazyTree")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "懒加载目录树", notes = "DirectoryVo")
	public R<List<FileTreeDto>> lazyTree(@RequestParam String parentId, @RequestParam(required = false) String tenantId) {
		List<FileTreeDto> baseCrudDto = directoryLogicService.lazyTree(Long.valueOf(parentId), tenantId);
		return R.data(baseCrudDto);
	}

	/**
	 * 文件和目录上传
	 */
	@PostMapping("/upload")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "文件和目录上传", notes = "DirectoryVo")
	public R<Boolean> upload(@RequestBody @Valid FileUploadVo v) {
		Boolean b = directoryLogicService.upload(v);
		return R.status(b);
	}

	/**
	 * 重新同步向量数据库的所有数据
	 */
	@PostMapping("/syncVector")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "重新同步向量数据库的所有数据", notes = "")
	public R<Boolean> allSyncVector() {
		Boolean b = directoryLogicService.allSyncVector();
		return R.status(b);
	}

	/**
	 * 重新同步Es的所有数据
	 */
	@PostMapping("/syncEs")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "重新同步Es的所有数据", notes = "")
	public R<Boolean> allSyncEs() {
		Boolean b = directoryLogicService.allSyncEs();
		return R.status(b);
	}

	/**
	 * 文件拖拽
	 */
	@PostMapping("/move")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "文件拖拽", notes = "DirectoryVo")
	public R<Boolean> drag(@RequestBody @Valid FileMoveVo v) {
		Boolean b = directoryLogicService.drag(v);
		return R.status(b);
	}

	/**
	 * 重命名
	 */
	@PostMapping("/rename")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "重命名目录", notes = "DirectoryVo")
	public R<Boolean> rename(@RequestBody @Valid DirectoryRenameVo v) {
		Boolean baseCrudDto = directoryLogicService.rename(v);
		return R.status(baseCrudDto);
	}

	/**
	 * 移动目录时排除的目录id
	 */
	@PostMapping("/excludeIds")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "移动目录时排除的目录id", notes = "")
	public R<List<Long>> excludeIds(@RequestBody List<Long> ids) {
		List<Long> baseCrudDto = directoryLogicService.excludeIds(ids);
		return R.data(baseCrudDto);
	}

	/**
	 * 移动目录时排除的目录id
	 */
	@PostMapping("/moveV2")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "目录的移动V2", notes = "")
	public R<Boolean> moveV2(@RequestBody FileMoveVoV2 v) {
		Boolean baseCrudDto = directoryLogicService.moveV2(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 检测目录及子级的目录下是否有文件
	 */
	@GetMapping("/checkHasFile")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "检测目录及子级的目录下是否有文件", notes = "id")
	public R<Boolean> checkHasFile(@RequestParam(value = "id") String dirIds) {
		List<Long> ids = Func.toLongList(dirIds);
		Boolean baseCrudDto = directoryLogicService.checkHasFile(ids);
		return R.data(baseCrudDto);
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "目录详情", notes = "id")
	public R<DirectoryDto> detail(Long id) {
		DirectoryDto baseCrudDto = directoryLogicService.detail(id);
		return R.data(baseCrudDto);
	}

	/**
	 * 删除
	 */
	@PostMapping("/removeByIds")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "目录删除", notes = "id")
	public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<DirectoryDVo> ids) {
		List<CommonDeleteResultDto> result = directoryLogicService.removeByIds(ids);
		return R.data(result);
	}

	@PostMapping("/importExcel")
	public ResponseEntity<String> importExcel(@RequestParam("file") MultipartFile file) {
		try {
			directoryLogicService.importDirectoryExcel(file);
			return ResponseEntity.ok("导入成功");
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("导入失败：" + e.getMessage());
		}
	}

	/**
	 * 获取特色产品列表
	 */
	@GetMapping("/specialProducts")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取特色产品列表", notes = "从系统字典中获取 code 为 specialProduct 的数据")
	public R<List<SpecialProductDto>> getSpecialProducts() {
		List<SpecialProductDto> specialProducts = directoryLogicService.getSpecialProducts();
		return R.data(specialProducts);
	}


	@PostMapping("/download")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "文件的批量下载", notes = "传id和type")
	public void downloadFiles(@RequestBody FileDownloadVo fileDownloadVo, HttpServletResponse response) {
		String downloadTenantId = fileDownloadVo.getDownloadTenantId();
		List<FileTreeDto> fileTreeDtos = fileDownloadVo.getFileTreeDtos();
		directoryLogicService.downloadFiles(fileTreeDtos, downloadTenantId, response);
	}

	/**
	 * 我上传的文件分页查询
	 */
	@PostMapping("/myUploads")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "我上传的文件分页查询", notes = "分页查询")
	public R<IPage<FileDto>> myUploads(@RequestBody DirectoryPageVo v) {
		IPage<FileDto> page = directoryLogicService.myUploads(v);
		return R.data(page);
	}

	/**
	 * 知识空间最新的文件list
	 */
	@GetMapping("/latestFiles")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "首页知识空间最新的文件list", notes = "分页查询")
	public R<List<FileDto>> latestFiles() {
		List<FileDto> page = directoryLogicService.latestFiles();
		return R.data(page);
	}

	/**
	 * 目录树
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "目录树", notes = "")
	public R<List<Tree<Long>>> tree() {
		return R.data(directoryLogicService.listTree());
	}

	/**
	 * 查询租户的统计信息
	 */
	@GetMapping("/tenantStat")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "查询租户的统计信息", notes = "")
	public R<List<TenantStatVo>> stat(@RequestParam(required = false) String tenantName) {
		List<TenantStatVo> stat = directoryLogicService.tenantStat(tenantName);
		return R.data(stat);
	}


}
