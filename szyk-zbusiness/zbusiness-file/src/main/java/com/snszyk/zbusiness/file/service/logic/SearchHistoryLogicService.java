/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.file.service.logic;


import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.config.LlmConfig;
import com.snszyk.zbusiness.file.dto.FileDto;
import com.snszyk.zbusiness.file.dto.SearchHistoryDto;
import com.snszyk.zbusiness.file.entity.SearchHistory;
import com.snszyk.zbusiness.file.service.IFileService;
import com.snszyk.zbusiness.file.service.ISearchHistoryService;
import com.snszyk.zbusiness.file.service.IStatService;
import com.snszyk.zbusiness.file.vo.SearchHistoryAVo;
import com.snszyk.zbusiness.file.vo.SearchHistoryPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@AllArgsConstructor
@Service
public class SearchHistoryLogicService {

	private final ISearchHistoryService searchHistoryService;

	private final IFileService fileService;
	private final LlmConfig llmConfig;

	private final IStatService statService;

	@Transactional
	public SearchHistoryDto saveOrUpdate(SearchHistoryAVo v) {
		// 名称的唯一性校验
		SearchHistory copy = BeanUtil.copy(v, SearchHistory.class);
		boolean b = searchHistoryService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<SearchHistoryDto> pageList(SearchHistoryPageVo v) {
		IPage<SearchHistoryDto> page = searchHistoryService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public SearchHistoryDto detail(Long id) {
		// 通过ID查询数据信息
		SearchHistoryDto dto = searchHistoryService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}


	public List<SearchHistoryDto> historyList(Long num) {
		Long userId = AuthUtil.getUserId();
		return searchHistoryService.historyList(userId, num);
	}

	/**
	 * 知识推荐
	 * @return
	 */
	public List<FileDto> suggestList() {
		List<SearchHistoryDto> searchHistoryDtos = historyList(10L);
		if (Func.isEmpty(searchHistoryDtos)) {
			return fileService.listLast(7);
		}
		//http工具类发送一个post请求
		List<String> contentList = searchHistoryDtos.stream().map(SearchHistoryDto::getContent).collect(Collectors.toList());

		String tenantId = AuthUtil.getTenantId();
		List<String> tenantIds = new ArrayList<>();
		tenantIds.add(tenantId);
		tenantIds.add("000000");
		Map<String, Object> params = new HashMap<>();
		params.put("tenantIds", tenantIds);
		params.put("contentList", contentList);
		String post = HttpUtil.post(llmConfig.getUrl() + llmConfig.getSuggestPath(), JSONUtil.toJsonStr(params));
		post = post.replaceAll("\"", "");
		String[] split = post.split(",");
		List<Long> fileIds = new ArrayList<>();
		for (String s : split) {
			if (Func.isNotEmpty(s)) {
				//判断是否可以转换为long类型

				try {
					Long.valueOf(s);
				} catch (Exception e) {
					continue;
				}
			}
		}
		List<FileDto> fileDtos = fileService.listDetailByIds(fileIds);
		// 如果数量不足7个，则补全
		if (fileDtos.size() < 7) {
			fileDtos.addAll(fileService.listLast(7 - fileDtos.size()));
		}
		return fileDtos.stream().limit(7).collect(Collectors.toList());

	}

	public List<FileDto> hotList() {
		String tenantId = AuthUtil.getTenantId();
		List<String> tenantIds = new ArrayList<>();
		tenantIds.add(tenantId);
		tenantIds.add("000000");
		List<Long> hotIds = statService.hotList(tenantIds);
		return fileService.listDetailByIds(hotIds);

	}

	/**
	 * 热门搜索
	 * @return
	 */
	public List<String> hotSearchList() {
		String tenantId = AuthUtil.getTenantId();
		List<String> tenantIds = new ArrayList<>();
		tenantIds.add(tenantId);
		tenantIds.add("000000");
		return searchHistoryService.hotSearchList(tenantIds);
	}
}
