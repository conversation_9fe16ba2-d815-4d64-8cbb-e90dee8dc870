<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.file.mapper.FileShareMapper">

        <sql id="selectData">
            select t.id,
                   t.tenant_id,
                   t.send_user,
                   t.receive_user,
                   t.file_id,
                   t.file_name,
                   t.remark,
                   t.create_user,
                   t.create_dept,
                   t.create_time,
                   t.update_user,
                   t.update_time,
                   f.attach_id,
                   t.has_read,
                   a.link,
                   u.real_name as send_user_name
            from k_file_share t
                     left join k_file f on t.file_id = f.id and f.is_deleted = 0
                     left join szyk_attach a on f.attach_id = a.id and a.is_deleted = 0
                     left join szyk_user u on t.send_user = u.id and u.is_deleted = 0
        </sql>
    <update id="read" parameterType="java.lang.Long">

        update k_file_share
        set has_read = 1
        where id = #{shareId}


    </update>
    <select id="pageList" resultType="com.snszyk.zbusiness.file.dto.FileShareDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
        <if test="v.receiveUser != null ">
            and t.receive_user = #{v.receiveUser}
        </if>
        order by t.has_read asc, t.create_time desc
    </select>

    <select id="detail" resultType="com.snszyk.zbusiness.file.dto.FileShareDto">
        <include refid="selectData"/>
       where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="noReadCount" resultType="java.lang.Integer" parameterType="java.lang.Long">

        select count(1) from k_file_share t where t.is_deleted = 0 and t.receive_user = #{userId} and t.has_read = 0

    </select>

</mapper>
