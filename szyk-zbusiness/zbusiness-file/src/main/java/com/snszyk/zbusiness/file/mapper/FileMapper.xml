<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.file.mapper.FileMapper">

    <sql id="selectData">
        select t.*
        from k_file t
    </sql>
    <select id="pageList" resultType="com.snszyk.zbusiness.file.dto.FileDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.zbusiness.file.dto.FileDto">
        <include refid="selectData"/>
        where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="getByIdIgnoreDel" resultType="com.snszyk.zbusiness.file.entity.KFile">
        select t.*
        from k_file t
        where t.id = #{id}
    </select>
    <select id="listData" resultType="com.snszyk.zbusiness.file.dto.FileTreeDto"
    >

        select t.id,
               t.tenant_id,
               t.directory_id,
               t.attach_id,
               t.name      as name,
               t.create_user,
               t.create_dept,
               t.create_time,
               t.update_user,
               t.update_time,
               t.status,
               t.is_deleted,
               u.real_name as createUserName,
               2           as type,
               a.link,
               t.sort,
               t1.tenant_name
        from k_file t
                 left join szyk_user u on u.id = t.create_user
                 left join szyk_attach a on t.attach_id = a.id
        left join szyk_tenant t1 on t.tenant_id = t1.tenant_id
        where t.is_deleted = 0
          and t.directory_id = #{parentId}
            <if test="tenantId != null and tenantId != ''">
                and t.tenant_id = #{tenantId}
            </if>
        <if test=" excludePlatform!= null and excludePlatform==true">
        and t.tenant_id != '000000'
        </if>
        order by t.sort desc, t.create_time desc

    </select>
    <select id="listEsDetailByIds" resultType="com.snszyk.zbusiness.file.dto.FileDto"
            parameterType="java.util.List">
        select t.id, t.tenant_id, t.directory_id, t.attach_id, t.name, t.create_user, t.create_dept, t.create_time,
        t.update_user, t.update_time, t.status, t.is_deleted, t.sort,
        a.link,
        s.view_num,
        s.star_num,
        s.favorite_num,
        u.real_name as createUserName
        from k_file t
        left join szyk_attach a on t.attach_id = a.id and a.is_deleted = 0
        left join k_stat s on t.id = s.id and s.is_deleted = 0
        left join szyk_user u on t.create_user = u.id and u.is_deleted = 0
        where t.is_deleted = 0 and t. id in
        <foreach collection="fileIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listLast" resultType="com.snszyk.zbusiness.file.dto.FileDto" parameterType="int">

        <include refid="detailSql"/>
        where t.is_deleted = 0
        order by t.create_time desc
        limit #{num}


    </select>
    <select id="listDetailByIds" resultType="com.snszyk.zbusiness.file.dto.FileDto"
            parameterType="java.util.List">
        <include refid="detailSql"/>
        where t.is_deleted = 0 and t. id in
        <foreach collection="fileIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by field(t.id,
        <foreach collection="fileIds" item="item" index="index" separator=",">
            #{item}
        </foreach>)
    </select>
    <select id="getByIdIgnoreTenant" resultType="com.snszyk.zbusiness.file.entity.KFile"
            parameterType="java.lang.Long">

        select t.*,
               a.link
        from k_file t
                 left join szyk_attach a on t.attach_id = a.id and a.is_deleted = 0
        where t.is_deleted = 0
          and t.id = #{fileId}

    </select>
    <select id="myUploads" resultType="com.snszyk.zbusiness.file.dto.FileDto"
            parameterType="com.snszyk.zbusiness.file.vo.DirectoryPageVo">


        <include refid="detailSql"/>
        where t.is_deleted = 0
        and t.create_user = #{v.createUser}
        <if test="v.name != null and v.name != ''">
            and t.name like concat('%', #{v.name}, '%')
        </if>
        order by t.create_time desc
    </select>
    <select id="latestFiles" resultType="com.snszyk.zbusiness.file.dto.FileDto"
            parameterType="java.lang.String">

        <include refid="detailSql"/>
        where t.is_deleted = 0
        and t.tenant_id = #{tenantId}
        order by t.create_time desc limit 4

    </select>
    <select id="tenantStat" resultType="com.snszyk.zbusiness.file.vo.TenantStatVo">

        select count(t.id) as count , t1.tenant_id, t1.tenant_name
        from szyk_tenant t1
        left join k_file t on t.tenant_id = t1.tenant_id and t.is_deleted = 0
        <where>
            <if test="tenantName != null and tenantName != ''">
                and t1.tenant_name like concat('%', #{tenantName}, '%')
            </if>
            and t1.is_deleted = 0 and t1.tenant_id !='000000'
        </where>
        group by t1.tenant_id, t1.tenant_name
        order by t1.tenant_name asc


    </select>
    <select id="listAll" resultType="com.snszyk.zbusiness.file.entity.KFile">
        <include refid="detailSql"/>
        where t.is_deleted = 0
        order by t.create_time desc
    </select>
    <sql id="detailSql">
        select t.id,
               t.tenant_id,
               t.directory_id,
               t.attach_id,
               t.name,
               t.create_user,
               t.create_dept,
               t.create_time,
               t.update_user,
               t.update_time,
               t.status,
               t.is_deleted,
               t.sort,
               a.link,
               u.real_name as updateUserName
        from k_file t
                 left join szyk_attach a on t.attach_id = a.id and a.is_deleted = 0
                 left join szyk_user u on u.id = t.update_user and u.is_deleted = 0

    </sql>
</mapper>
