<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.AiModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiModelResultMap" type="com.snszyk.zbusiness.basic.entity.AiModel">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="description" property="description"/>
        <result column="params" property="params"/>
        <result column="dict" property="dict"/>
        <result column="version" property="version"/>
        <result column="is_pretreat" property="isPretreat"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="aiModelDTOResultMap" type="com.snszyk.zbusiness.basic.dto.AiModelDTO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_path_name" property="equipmentPathName"/>
        <result column="description" property="description"/>
        <result column="params" property="params"/>
        <result column="dict" property="dict"/>
        <result column="version" property="version"/>
        <result column="is_pretreat" property="isPretreat"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="page" resultMap="aiModelDTOResultMap">
        select ai.*,eq.name as equipment_name,REPLACE(eq.path_name, ',', '/') as equipment_path_name
        from eolm_ai_model as ai left join eolm_equipment as eq on eq.id=ai.equipment_id
        where ai.is_deleted = 0
        <if test="aiModel.name!=null and aiModel.name!=''">
            and ai.name like concat(concat('%', #{aiModel.name}),'%')
        </if>

        <if test="aiModel.applyStatus != null and aiModel.applyStatus == 0">
            and (ai.equipment_id is null or ai.equipment_id = 0)
        </if>

        <if test="aiModel.applyStatus!=null and aiModel.applyStatus==1">
            and ai.equipment_id is not null
        </if>

        <if test="aiModel.status!=null">
            and ai.status = #{aiModel.status}
        </if>
        order by ai.create_time desc
    </select>

    <select id="queryAiModelParams" resultMap="aiModelDTOResultMap">
        select ai.name,ai.params,ai.equipment_id
        from eolm_ai_model as ai left join basic_sensor_instance as si on si.equipment_id = ai.equipment_id
        where si.code=#{sensorCode}
    </select>

</mapper>
