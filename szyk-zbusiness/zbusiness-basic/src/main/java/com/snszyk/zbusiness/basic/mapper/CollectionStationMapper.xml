<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.CollectionStationMapper">

    <resultMap id="stationWithChannelResultMap" type="com.snszyk.zbusiness.basic.dto.CollectionStationDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="online" property="online"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="channel_count" property="channelCount"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <collection property="channelList" ofType="com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO">
            <result column="channel_id" property="id"/>
            <result column="station_id" property="stationId"/>
            <result column="channel_name" property="name"/>
            <result column="channel_online" property="online"/>
            <result column="sensor_code" property="sensorCode"/>
            <result column="path_name" property="pathName"/>
            <result column="sensor_name" property="sensorName"/>
            <result column="is_wireless" property="isWireless"/>
        </collection>
    </resultMap>

    <select id="selectStationPage" resultType="com.snszyk.zbusiness.basic.dto.CollectionStationDTO">
        SELECT *
        FROM eolm_collection_station
        <where>
            is_deleted = 0
            <if test="vo.tenantId != null and vo.tenantId != ''">
                AND tenant_id = #{vo.tenantId}
            </if>
            <if test="vo.code != null and vo.code != ''">
                AND code LIKE CONCAT('%', #{vo.code}, '%')
            </if>
            <if test="vo.name != null and vo.name != ''">
                AND name LIKE CONCAT('%', #{vo.name}, '%')
            </if>
            <if test="vo.online != null">
                AND online = #{vo.online}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="onlineStat" resultType="com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO">
        SELECT count(*) AS totalCount, sum(`online` = 0) AS offlineCount, sum(`online` = 1) AS onlineCount
        FROM eolm_collection_station
        <where>
            is_deleted = 0
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </select>

</mapper>
