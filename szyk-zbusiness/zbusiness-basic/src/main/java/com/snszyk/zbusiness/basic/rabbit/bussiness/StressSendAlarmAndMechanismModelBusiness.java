package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.zbusiness.basic.enums.InvalidEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.feign.PythonServerFeign;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;


/**
 * 2、应力波保存数据后，发送报警消息 & 调用能量值 & 调用机理模型。
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class StressSendAlarmAndMechanismModelBusiness extends AbstractBusiness {

	private final RabbitTemplate rabbitTemplate;
	private final PythonServerFeign pythonServerFeign;

	@Override
	public String getCommand() {
		return Command.STRESS_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);
		SensorDataVO sensorDataVO = message.getSensorDataVO();
		if (sensorDataVO != null && !InvalidEnum.INVALID.getCode().equals(sensorDataVO.getInvalid())) {
			log.info("发送报警MQ消息给报警检测：sensorDataId = {}, monitorId = {}。",
				message.getSensorDataId(), message.getMonitorId());
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
				EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM, sensorDataVO);

			// 应力波调用机理模型处理
			if (SampledDataTypeEnum.STRESS.getCode().equals(message.getType())) {
				// 2、调用机理模型
				log.info("调用机理模型：sensorDataId = {}, monitorId = {}", message.getSensorDataId(), message.getMonitorId());
				JSONObject mechanismModel = pythonServerFeign.mechanismModel(DateUtil.format(sensorDataVO.getOriginTime(), DateUtil.PATTERN_DATETIME),
					sensorDataVO.getWaveId() + "",
					PythonServerFeign.SEND_ALARM_YES,
					sensorDataVO.getMonitorId() + "");
				log.info("调用机理模型结果：{}", mechanismModel);
			}
		} else {
			log.warn("VibrateSendAlarmBusiness.business() - 异常的传感器数据，不发送报警！sensorDataId = {}, monitorId = {}",
				message.getSensorDataId(), message.getMonitorId());
		}
	}

}
