/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
import com.snszyk.zbusiness.basic.service.ICollectionStationService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.vo.CollectionStationChannelVO;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.common.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采集站表 逻辑服务实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
@AllArgsConstructor
public class CollectionStationLogicService {

	private final ICollectionStationService stationService;
	private final ICollectionStationChannelService stationChannelService;
	private final ISensorInstanceService sensorInstanceService;

	/**
	 * 采集站详情
	 * @param id 采集站id
	 * @return
	 */
	public CollectionStationDTO detail(Long id) {
		//查询采集站
		CollectionStation collectionStation = stationService.getById(id);
		if (collectionStation == null) {
			throw new ServiceException("未获取到采集站信息，id = " + id);
		}
		//查询采集站通道列表
		CollectionStationDTO collectionStationDTO = BeanUtil.copy(collectionStation, CollectionStationDTO.class);
		List<CollectionStationChannelDTO> channelDTOList = stationChannelService.selectChannelList(id);
		if (CollectionUtil.isNotEmpty(channelDTOList)) {
			channelDTOList.forEach(dto -> dto.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, dto.getOnline())));
			assert collectionStationDTO != null;
			collectionStationDTO.setChannelList(channelDTOList);
		}
		collectionStationDTO.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, collectionStation.getOnline()));
		return collectionStationDTO;
	}

	/**
	 * 采集站分页
	 * @param page 分页参数
	 * @param vo 查询条件
	 * @return
	 */
	public IPage<CollectionStationDTO> selectStationPage(IPage<CollectionStationDTO> page, CollectionStationVO vo) {
		return stationService.selectStationPage(page, vo);
	}

	/**
	 * 新增采集站
	 * @param vo vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean save(CollectionStationVO vo) {
		//保存采集站
		CollectionStation collectionStation = BeanUtil.copy(vo, CollectionStation.class);
		assert collectionStation != null;
		collectionStation.setCode(BizCodeUtil.generate("CJ"));
		//设置采集站的在线状态（有任何一个在线的通道即认为采集站在线）
		List<CollectionStationChannelVO> channelList = vo.getChannelList();
		for (CollectionStationChannelVO channelVO : channelList) {
			if (channelVO.getOnline() != null && channelVO.getOnline() == 1) {
				collectionStation.setOnline(1);
				break;
			}
		}
		boolean save = stationService.save(collectionStation);
		if (!save) {
			throw new ServiceException("保存采集站失败！");
		}

		//保存通道
		channelList.forEach(channelVO -> channelVO.setStationId(collectionStation.getId()));
		List<CollectionStationChannel> stationChannelList = BeanUtil.copy(channelList, CollectionStationChannel.class);
		boolean saveChannels = stationChannelService.saveBatch(stationChannelList);
		if (!saveChannels) {
			throw new ServiceException("保存采集站通道失败！");
		}

		//保存传感器实例与采集站的绑定状态
		List<String> sensorCodeList = channelList.stream().map(CollectionStationChannel::getSensorCode)
			.filter(Func::isNotEmpty).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(sensorCodeList)) {
			int bindCount = sensorInstanceService.bindCollectionStation(sensorCodeList, collectionStation.getId());
			log.info("绑定传感器实例与采集站的绑定状态，绑定数量：{}", bindCount);
		}

		return true;
	}

	/**
	 * 更新采集站
	 * @param vo vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean update(CollectionStationVO vo) {
		//更新采集站
		CollectionStation collectionStation = BeanUtil.copy(vo, CollectionStation.class);
		assert collectionStation != null;
		//更新采集站的在线状态（有任何一个在线的通道即认为采集站在线）
		List<CollectionStationChannelVO> channelList = vo.getChannelList();
		for (CollectionStationChannelVO channelVO : channelList) {
			if (channelVO.getOnline() != null && channelVO.getOnline() == 1) {
				collectionStation.setOnline(1);
				break;
			} else {
				collectionStation.setOnline(0);
			}
		}
		stationService.updateById(collectionStation);

		//更新通道表（删除旧的，增加新的）
		stationChannelService.remove(new QueryWrapper<CollectionStationChannel>().lambda()
			.eq(CollectionStationChannel::getStationId, vo.getId()));
		channelList.forEach(channelVO -> channelVO.setStationId(collectionStation.getId()));
		List<CollectionStationChannel> stationChannelList = BeanUtil.copy(channelList, CollectionStationChannel.class);
		boolean saveChannels = stationChannelService.saveBatch(stationChannelList);
		if (!saveChannels) {
			throw new ServiceException("更新采集站通道失败！");
		}

		//更新传感器实例与采集站的绑定状态（删除旧的，绑定新的）
		int unbindCount = sensorInstanceService.unbindCollectionStation(Collections.singletonList(collectionStation.getId()));
		log.info("更新传感器实例与采集站的绑定状态，解绑数量：{}", unbindCount);
		if (CollectionUtil.isNotEmpty(channelList)) {
			List<String> sensorCodeList = channelList.stream()
				.map(CollectionStationChannel::getSensorCode)
				.filter(Func::isNotEmpty)
				.collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(sensorCodeList)) {
				int bindCount = sensorInstanceService.bindCollectionStation(sensorCodeList, collectionStation.getId());
				log.info("更新传感器实例与采集站的绑定状态，绑定数量：{}", bindCount);
			}
		}

		return true;
	}

	/**
	 * 删除采集站 - 逻辑删除（只删除主表，通道表数据不动）
	 * @param idList id列表
	 * @return
	 */
	public boolean remove(List<Long> idList) {
		boolean remove = stationService.removeByIds(idList);
		int unbindCollectionStation = sensorInstanceService.unbindCollectionStation(idList);
		log.info("删除采集站，解绑数量：{}", unbindCollectionStation);
		return remove;
	}

	/**
	 * 采集站在线状态统计
	 * @param tenantId 租户
	 * @return
	 */
	public CollectionStationOnlineStatDTO onlineStat(String tenantId) {
		CollectionStationOnlineStatDTO dto = stationService.onlineStat(tenantId);
		List<CollectionStation> stationList = stationService.list();
		if(Func.isNotEmpty(stationList)){
			dto.setChannelCount(stationChannelService.count(Wrappers.<CollectionStationChannel>query().lambda()
				.in(CollectionStationChannel::getStationId, stationList.stream().map(CollectionStation::getId).collect(Collectors.toList()))));
		}
		return dto;
	}

}
