/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.dto.DeviceCollectionStationDTO;
import com.snszyk.zbusiness.basic.entity.DeviceCollectionStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 厂区采集站绑定 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceCollectionStationMapper extends BaseMapper<DeviceCollectionStation> {

	int deleteByDeviceId(@Param("deviceId") Long deviceId);

    List<DeviceCollectionStationDTO> listCollectionStation(@Param("deviceId") Long deviceId);
}
