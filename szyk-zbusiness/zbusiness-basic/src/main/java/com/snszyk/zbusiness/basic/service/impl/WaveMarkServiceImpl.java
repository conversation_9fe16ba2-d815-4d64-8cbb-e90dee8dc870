/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.entity.MonitorParam;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.entity.WaveMark;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.mapper.MonitorParamMapper;
import com.snszyk.zbusiness.basic.mapper.WaveMapper;
import com.snszyk.zbusiness.basic.mapper.WaveMarkMapper;
import com.snszyk.zbusiness.basic.service.ISensorDataService;
import com.snszyk.zbusiness.basic.service.IWaveMarkService;
import com.snszyk.zbusiness.basic.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 波形标注表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Slf4j
@Service
@AllArgsConstructor
public class WaveMarkServiceImpl extends ServiceImpl<WaveMarkMapper, WaveMark> implements IWaveMarkService {

	private final ISensorDataService sensorDataService;
	private final MonitorParamMapper monitorParamMapper;
	private final WaveMapper waveMapper;

	@Override
	public boolean submit(WaveMarkVO vo) {
		boolean ret = baseMapper.removeByParams(vo) >= 0;
		if (Func.isNotEmpty(vo.getWaveMarkBearingList())) {
			List<WaveMark> list = vo.getWaveMarkBearingList().stream().map(waveMarkBearingVO -> {
				WaveMark waveMark = Objects.requireNonNull(BeanUtil.copy(vo, WaveMark.class));
				waveMark.setBearingId(waveMarkBearingVO.getId()).setCharacteristicFreq(JSONUtil.toJsonStr(waveMarkBearingVO))
					.setCreateTime(DateUtil.now());
				return waveMark;
			}).collect(Collectors.toList());
			return this.saveBatch(list);
		}
		return ret;
	}

	@Override
	public WaveMarkVO detail(WaveMarkVO vo) {
		List<WaveMark> list = this.list(Wrappers.<WaveMark>query().lambda().eq(WaveMark::getMonitorId, vo.getMonitorId())
			.eq(WaveMark::getWaveId, vo.getWaveId()));
		WaveMarkVO waveMarkVO = null;
		if (Func.isNotEmpty(list)) {
			waveMarkVO = Objects.requireNonNull(BeanUtil.copy(list.get(0), WaveMarkVO.class));
			List<WaveMarkBearingVO> children = list.stream().map(waveMark ->
				JSONUtil.toBean(waveMark.getCharacteristicFreq(), WaveMarkBearingVO.class)).collect(Collectors.toList());
			waveMarkVO.setWaveMarkBearingList(children);
		}
		return waveMarkVO;
	}

	@Override
	public double getRotatingFreqByWave(Long waveId) {
		List<WaveMark> waveMarks = baseMapper.selectList(new QueryWrapper<WaveMark>().lambda().eq(WaveMark::getWaveId, waveId));
		if (CollectionUtil.isNotEmpty(waveMarks)) {
			//转频 = 转速/60
			return waveMarks.get(0)
				.getSpeed()
				.divide(BigDecimal.valueOf(60), 4, RoundingMode.HALF_UP)
				.doubleValue();
		}
		return 0d;
	}


	@Override
	public List<ParamBearingVO> bearingSelect(Long waveId) {
		Wave wave = waveMapper.selectById(waveId);
		if (wave == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		List<ParamBearingVO> list = new ArrayList<>();
		MonitorParam monitorParam = monitorParamMapper.selectOne(Wrappers.<MonitorParam>query().lambda()
			.eq(MonitorParam::getMonitorId, wave.getMonitorId()));
		if (monitorParam != null && Func.isNotEmpty(monitorParam.getBearingInfo())) {
			list = JSONUtil.toList(monitorParam.getBearingInfo(), ParamBearingVO.class);
		}
		return list;
	}

	@Override
	public List<ParamRpmVO> rpmSelect(Long waveId, String originTime) {
		Wave wave = waveMapper.selectById(waveId);
		if (wave == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		List<ParamRpmVO> list = new ArrayList<>();
		List<Wave> rpmWaves = waveMapper.selectList(Wrappers.<Wave>query().lambda()
			.eq(Wave::getMonitorId, wave.getMonitorId()).eq(Wave::getSampleDataType, SampledDataTypeEnum.RPM.getCode()));
		if (Func.isNotEmpty(rpmWaves)) {
			// 根据部位id和当前有效值时间查询传感器转速
			SensorData sensorData = sensorDataService.queryRealRev(rpmWaves, originTime);
			if (Func.isNotEmpty(sensorData)) {
				list.add(new ParamRpmVO("当前转速", sensorData.getValue().setScale(0, RoundingMode.DOWN)));
			}
		}
		MonitorParam monitorParam = monitorParamMapper.selectOne(Wrappers.<MonitorParam>query().lambda()
			.eq(MonitorParam::getMonitorId, wave.getMonitorId()));
		if (monitorParam != null && Func.isNotEmpty(monitorParam.getRpmInfo())) {
			list.addAll(JSONUtil.toList(monitorParam.getRpmInfo(), ParamRpmVO.class));
		}
		return list;
	}

	@Override
	public List<ParamGearVO> gearTeethSelect(Long waveId) {
		Wave wave = waveMapper.selectById(waveId);
		if (wave == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		List<ParamGearVO> list = new ArrayList<>();
		MonitorParam monitorParam = monitorParamMapper.selectOne(Wrappers.<MonitorParam>query().lambda()
			.eq(MonitorParam::getMonitorId, wave.getMonitorId()));
		if (monitorParam != null && Func.isNotEmpty(monitorParam.getGearInfo())) {
			list = JSONUtil.toList(monitorParam.getGearInfo(), ParamGearVO.class);
		}
		return list;
	}

}
