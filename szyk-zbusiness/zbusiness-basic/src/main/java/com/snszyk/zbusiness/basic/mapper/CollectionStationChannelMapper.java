/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采集站通道表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CollectionStationChannelMapper extends BaseMapper<CollectionStationChannel> {

	/**
	 * 查询采集站的通道列表
	 * @param stationId 采集站id
	 * @return
	 */
    List<CollectionStationChannelDTO> selectChannelList(@Param("stationId") Long stationId);
}
