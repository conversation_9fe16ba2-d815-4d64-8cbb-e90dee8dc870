<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.GlobalConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="globalConfigResultMap" type="com.snszyk.zbusiness.basic.entity.GlobalConfig">
        <id column="id" property="id"/>
        <id column="tenant_id" property="tenantId"/>
        <result column="category" property="category"/>
        <result column="settings" property="settings"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


</mapper>
