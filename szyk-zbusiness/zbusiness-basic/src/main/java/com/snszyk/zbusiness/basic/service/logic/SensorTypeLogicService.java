/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tool.utils.*;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.SensorTypeDTO;
import com.snszyk.zbusiness.basic.dto.SensorTypeParamDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.entity.SensorType;
import com.snszyk.zbusiness.basic.entity.SensorTypeParam;
import com.snszyk.zbusiness.basic.enums.*;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.ISensorTypeParamService;
import com.snszyk.zbusiness.basic.service.ISensorTypeService;
import com.snszyk.zbusiness.basic.vo.SensorTypeParamVO;
import com.snszyk.zbusiness.basic.vo.SensorTypeVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 传感器类型 逻辑服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorTypeLogicService {

	private final ISensorTypeService sensorTypeService;
	private final ISensorTypeParamService sensorTypeParamService;
	private final ISensorInstanceService sensorInstanceService;

	/**
	 * 传感器库详情
	 *
	 * @param id id
	 */
	public SensorTypeDTO detail(Long id) {
		// 传感器类型
		SensorType sensorType = sensorTypeService.getById(id);
		if (sensorType == null) {
			throw new ServiceException("当前传感器已删除，请刷新后重试!");
		}
		SensorTypeDTO sensorTypeDTO = Objects.requireNonNull(BeanUtil.copy(sensorType, SensorTypeDTO.class));
		// 设置传感器厂家名称
		if (Func.isNotEmpty(sensorTypeDTO.getSupplier())) {
			sensorTypeDTO.setSupplierName(DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, sensorTypeDTO.getSupplier()));
		}
		// 设置传感器类型名称
		if (Func.isNotEmpty(sensorTypeDTO.getCategory())) {
			sensorTypeDTO.setCategoryName(DictBizCache.getValue(DictBizEnum.SENSOR_CATEGORY, sensorTypeDTO.getCategory()));
		}

		// 设置是否有传感器实例
		int sensorInstanceCount = sensorInstanceService.count(Wrappers.<SensorInstance>query()
			.lambda()
			.eq(SensorInstance::getTypeId, id)
			.eq(SensorInstance::getIsDeleted, 0));
		if (sensorInstanceCount > 0) {
			sensorTypeDTO.setHasSensorInstance(1);
		} else {
			sensorTypeDTO.setHasSensorInstance(0);
		}

		// 传感器类型参数列表
		List<SensorTypeParam> sensorTypeParamList = sensorTypeParamService.list(Wrappers.<SensorTypeParam>query()
			.lambda()
			.eq(SensorTypeParam::getTypeId, id)
			.orderByAsc(SensorTypeParam::getSort));
		if (Func.isNotEmpty(sensorTypeParamList)) {
			sensorTypeDTO.setSensorTypeParamList(sensorTypeParamList.stream()
				.map(param -> {
					SensorTypeParamDTO paramDTO = Objects.requireNonNull(BeanUtil.copy(param, SensorTypeParamDTO.class));

					if (Func.isNotEmpty(paramDTO.getVibrationType())) {
						paramDTO.setVibrationTypeName(DictBizCache.getValue(DictBizEnum.VIBRATION_TYPE, paramDTO.getVibrationType()));
					}

					if (Func.isNotEmpty(paramDTO.getSampleDataType())) {
						paramDTO.setSampleDataTypeName(DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_TYPE, paramDTO.getSampleDataType()));
					}

					if (Func.isNotEmpty(paramDTO.getAxialDirection())) {
						paramDTO.setAxialDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, paramDTO.getAxialDirection()));
					}

					// 设置默认特征值名称
					if (Func.isNotEmpty(paramDTO.getDefaultFeatures())) {
						List<String> featuresName = Arrays.stream(paramDTO.getDefaultFeatures().split(StringPool.COMMA))
							.map(feature -> NonVibrationDataEnum.getByCode(feature).getName())
							.collect(Collectors.toList());
						paramDTO.setDefaultFeaturesName(StringUtil.join(featuresName, StringPool.COMMA));
					}

					// 当前传感器参数是否可编辑 - 如果有实例化传感器则不能修改
					paramDTO.setCanEdit(sensorTypeDTO.getHasSensorInstance() == 0);
					return paramDTO;
				}).collect(Collectors.toList()));
		}
		return sensorTypeDTO;
	}

	/**
	 * 提交（新增or修改传感器类型）
	 *
	 * @param vo vo
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(SensorTypeVO vo) {
		boolean retType;
		// 校验 传感器测量方向和主轴的唯一性
		if (Func.isNotEmpty(vo.getSensorTypeParamList())) {
			checkSensorTypeParam(vo.getCategory(), vo.getSensorTypeParamList());
		} else {
			throw new ServiceException("请添加传感器类型参数！");
		}

		// 校验 传感器名称+型号+厂家 唯一性
		SensorType existedSensorType;
		if (Func.isNotEmpty(vo.getId())) {
			existedSensorType = sensorTypeService.getOne(Wrappers.<SensorType>query().lambda()
				.eq(SensorType::getName, vo.getName())
				.eq(SensorType::getModel, vo.getModel())
				.eq(SensorType::getSupplier, vo.getSupplier())
				.ne(SensorType::getId, vo.getId()));
		} else {
			existedSensorType = sensorTypeService.getOne(Wrappers.<SensorType>query().lambda()
				.eq(SensorType::getName, vo.getName())
				.eq(SensorType::getModel, vo.getModel())
				.eq(SensorType::getSupplier, vo.getSupplier()));
		}
		if (Func.isNotEmpty(existedSensorType)) {
			throw new ServiceException("传感器名称：" + vo.getName() + "，传感器型号：" + vo.getModel()
				+ "，传感器厂家：" + DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, vo.getSupplier()) + "已存在");
		}

		SensorType sensorType = Objects.requireNonNull(BeanUtil.copy(vo, SensorType.class));
		retType = sensorTypeService.saveOrUpdate(sensorType);
		log.info("提交传感器类型 = {}，传感器类型id：{}", retType, sensorType.getId());

		// 更新or新增传感器类型参数列表
		List<SensorTypeParam> oldParams = sensorTypeParamService.list(Wrappers.<SensorTypeParam>query().lambda()
			.eq(SensorTypeParam::getTypeId, sensorType.getId()));
		List<Long> oldParamIds = new ArrayList<>();
		if (Func.isNotEmpty(oldParams)) {
			oldParamIds = oldParams.stream().map(SensorTypeParam::getId).collect(Collectors.toList());
		}
		// 更新传感器参数
		if (Func.isNotEmpty(vo.getSensorTypeParamList())) {
			if (Func.isNotEmpty(oldParamIds)) {
				List<Long> newParamIds = vo.getSensorTypeParamList().stream()
					.map(SensorTypeParam::getId)
					.filter(Func::isNotEmpty)
					.collect(Collectors.toList());
				List<Long> removeParamIds = oldParamIds.stream()
					.filter(oldParamId -> !newParamIds.contains(oldParamId))
					.collect(Collectors.toList());
				if (Func.isNotEmpty(removeParamIds)) {
					sensorTypeParamService.removeByIds(removeParamIds);
				}
			}
			AtomicReference<Integer> sort = new AtomicReference<>(0);
			List<SensorTypeParam> list = vo.getSensorTypeParamList().stream()
				.map(paramVO -> {
					SensorTypeParam sensorTypeParam = Objects.requireNonNull(BeanUtil.copy(paramVO, SensorTypeParam.class));
					if (Func.isEmpty(sensorTypeParam.getId())) {
						sensorTypeParam.setTypeId(sensorType.getId());
						// 单轴传感器的默认轴方向为Z轴
						if (sensorType.getAxisCount() != null && sensorType.getAxisCount() == 1) {
							sensorTypeParam.setAxialDirection(MeasureDirectionEnum.AXIAL.getCode());
						}
						sensorTypeParam.setSort(sort.get() + 1);
						sensorTypeParam.setCreateTime(DateUtil.now());
					} else {
						sort.set(sensorTypeParam.getSort());
					}
					return sensorTypeParam;
				}).collect(Collectors.toList());
			boolean saveOrUpdateBatch = sensorTypeParamService.saveOrUpdateBatch(list);
			log.info("更新传感器类型参数列 = {}", saveOrUpdateBatch);
		} else {
			boolean remove = sensorTypeParamService.remove(Wrappers.<SensorTypeParam>query().lambda()
				.eq(SensorTypeParam::getTypeId, sensorType.getId()));
			log.info("删除传感器类型参数列 = {}", remove);
		}

		return retType;
	}

	/**
	 * 校验传感器测量方向和主轴的唯一性
	 *
	 * @param category 传感器类型
	 * @param paramList 传感器类型参数列表
	 */
	private void checkSensorTypeParam(Integer category, List<SensorTypeParamVO> paramList) {
		if (SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(category)) {
			// 校验振动类型参数
			List<String> measureDirections = paramList.stream()
				.filter(sensorParamVO -> VibrationTypeEnum.IS_VIBRATION == VibrationTypeEnum.getByCode(sensorParamVO.getVibrationType()))
				.map(sensorParamVO -> sensorParamVO.getSampleDataType() + sensorParamVO.getAxialDirection())
				.collect(Collectors.toList());
			if (Func.isNotEmpty(measureDirections)) {
				long count = measureDirections.stream().distinct().count();
				if (count < measureDirections.size()) {
					throw new ServiceException("存在相同测量方向的振动传感器参数");
				}
			}
		} else if (SensorCategoryEnum.STRESS_WAVE.getCode().equals(category)) {
			// 最多只能配置1个应力波参数
			long stressParamCount = paramList.stream()
				.filter(paramVO -> VibrationTypeEnum.IS_VIBRATION.getCode().equals(paramVO.getVibrationType())
					&& SampledDataTypeEnum.STRESS.getCode().equals(paramVO.getSampleDataType()))
				.count();
			if (stressParamCount > 1) {
				throw new ServiceException("只能配置1个应力波参数！");
			}

			// 最多只能配置1个振动（加速度、速度、位移）参数
			long vibrateParamCount = paramList.stream()
				.filter(paramVO -> VibrationTypeEnum.IS_VIBRATION.getCode().equals(paramVO.getVibrationType())
					&& !SampledDataTypeEnum.STRESS.getCode().equals(paramVO.getSampleDataType()))
				.count();
			if (vibrateParamCount > 1) {
				throw new ServiceException("只能配置1个振动（加速度、速度、位移）参数！");
			}

		} else if (SensorCategoryEnum.ELECTRIC.getCode().equals(category)) {
			// 只能配置1个电流参数
			long stressParamCount = paramList.stream()
				.filter(paramVO -> VibrationTypeEnum.IS_VIBRATION.getCode().equals(paramVO.getVibrationType())
					&& SampledDataTypeEnum.ELECTRIC.getCode().equals(paramVO.getSampleDataType()))
				.count();
			if (stressParamCount > 1) {
				throw new ServiceException("只能配置1个电流参数！");
			}
		}
	}

	/**
	 * 带校验的删除
	 *
	 * @param ids ids
	 */
	public boolean removeSensor(List<Long> ids) {
		// 判断传感器库是否已生成传感器实例
		List<SensorInstance> list = sensorInstanceService.list(Wrappers.<SensorInstance>query().lambda()
			.in(SensorInstance::getTypeId, ids)
			.eq(BaseEntity::getIsDeleted, 0));
		if (Func.isNotEmpty(list)) {
			throw new ServiceException("已生成传感器实例，无法删除!");
		}

		return sensorTypeService.deleteLogic(ids);
	}


	/**
	 * 分页
	 * @param page 分页
	 * @param vo vo
	 * @return
	 */
	public IPage<SensorTypeDTO> page(IPage<SensorTypeDTO> page, SensorTypeVO vo) {
		return sensorTypeService.page(page, vo);
	}

}
