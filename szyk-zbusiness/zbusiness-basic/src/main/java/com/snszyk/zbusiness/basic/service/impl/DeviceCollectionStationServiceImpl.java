/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.zbusiness.basic.dto.DeviceCollectionStationDTO;
import com.snszyk.zbusiness.basic.entity.DeviceCollectionStation;
import com.snszyk.zbusiness.basic.mapper.DeviceCollectionStationMapper;
import com.snszyk.zbusiness.basic.service.IDeviceCollectionStationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 厂区采集站绑定表 服务实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class DeviceCollectionStationServiceImpl extends BaseServiceImpl<DeviceCollectionStationMapper, DeviceCollectionStation>
	implements IDeviceCollectionStationService {

	private final DeviceCollectionStationMapper deviceCollectionStationMapper;

	@Override
	public int deleteByDeviceId(Long deviceId) {
		return deviceCollectionStationMapper.deleteByDeviceId(deviceId);
	}

	@Override
	public List<DeviceCollectionStationDTO> listCollectionStation(Long deviceId) {
		return deviceCollectionStationMapper.listCollectionStation(deviceId);
	}
}
