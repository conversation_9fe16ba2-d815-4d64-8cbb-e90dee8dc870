/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.IntelligentDiagnosisResult;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.enums.FaultSuggestionEnum;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备点检计划配置表 逻辑服务实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@AllArgsConstructor
@Service
public class EquipmentSpotCheckConfigLogicService {

	private final IMonitorService monitorService;

	/**
	 * 获取机理模型诊断结论、建议
	 *
	 * @param mechanismResult 机理模型诊断结果
	 *                        {
	 *                        "alarmLevel": fault_grade,
	 *                        "monitorId": monitor_id,
	 *                        "result": data, # data="0"表示正常；data="1,2,3" 表示存在故障，多个故障用','分隔
	 *                        "waveConfigId": wave_config_id,
	 *                        "originTime": origin_time
	 *                        }
	 * @return
	 */
	public IntelligentDiagnosisResult getIntelligentDiagnosis(JSONObject mechanismResult) {
		IntelligentDiagnosisResult result = new IntelligentDiagnosisResult();
		if (mechanismResult == null) {
			result.setSuccess(false);
			result.setFailReason("机理模型诊断返回空！诊断失败！");
		} else if (mechanismResult.getIntValue("code") == 1) {
			//未配置：{"msg":"未查询到机理模型配置信息","code":1}
			result.setSuccess(false);
			result.setFailReason("未查询到机理模型配置信息，无法进行诊断！");
		} else if (Func.isEmpty(mechanismResult.getString("result")) || "0".equals(mechanismResult.getString("result"))) {
			result.setSuccess(true);
			long monitorId = mechanismResult.getLongValue("monitorId");
			Monitor monitor = monitorService.getById(monitorId);
			result.setConclusion(String.format("通过模型分析，该%s未发现明显异常。",
				monitor.getEquipmentType() == 0 ? "设备" : DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType())));
		} else {
			result.setSuccess(true);
			String faultTypeResult = mechanismResult.getString("result");
			if (Func.isNotEmpty(faultTypeResult)) {
				// 测点
				long monitorId = mechanismResult.getLongValue("monitorId");
				Monitor monitor = monitorService.getById(monitorId);

				// 故障列表
				List<String> faultTypeList = Arrays.asList(faultTypeResult.split(StringPool.COMMA));
				List<String> faultTypeStringList = new ArrayList<>();
				faultTypeList.forEach(faultType -> faultTypeStringList.add(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, faultType)));

				// 故障等级
				Integer alarmLevel = mechanismResult.getInteger("alarmLevel");
				result.setConclusion(String.format("通过模型分析，该%s存在%s，故障为%s级",
					monitor.getEquipmentType() == 0 ? "设备" : DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType()),
					Func.join(faultTypeStringList, "、"), alarmLevel.toString()));

				// 诊断建议
				List<String> faultSuggestionList = new ArrayList<>();
				faultTypeList.forEach(faultType -> faultSuggestionList.add(FaultSuggestionEnum.getByCode(Integer.parseInt(faultType)).getSuggestion()));
				List<String> suggestionList = faultSuggestionList.stream().distinct().collect(Collectors.toList());
				result.setSuggestion(Func.join(suggestionList, "；"));
			} else {
				log.warn("机理模型返回的result为空！！！");
			}
		}

		return result;
	}

}
