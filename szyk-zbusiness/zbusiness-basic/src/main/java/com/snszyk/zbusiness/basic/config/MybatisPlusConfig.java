package com.snszyk.zbusiness.basic.config;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.snszyk.core.launch.props.SzykPropertySource;
import com.snszyk.core.mp.SzykMetaObjectHandler;
import com.snszyk.core.mp.injector.SzykSqlInjector;
import com.snszyk.core.mp.intercept.QueryInterceptor;
import com.snszyk.core.mp.plugins.SqlLogInterceptor;
import com.snszyk.core.mp.plugins.SzykPaginationInterceptor;
import com.snszyk.core.mp.props.MybatisPlusProperties;
import com.snszyk.core.mp.resolver.PageArgumentResolver;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MybatisPlus配置动态表名
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AllArgsConstructor
@MapperScan("com.snszyk.**.mapper.**")
@EnableConfigurationProperties(MybatisPlusProperties.class)
@SzykPropertySource(value = "classpath:/szyk-mybatis.yml")
public class MybatisPlusConfig implements WebMvcConfigurer {

	/**
	 * 租户拦截器
	 */
	@Bean
	@ConditionalOnMissingBean(TenantLineInnerInterceptor.class)
	public TenantLineInnerInterceptor tenantLineInnerInterceptor() {
		return new TenantLineInnerInterceptor(new TenantLineHandler() {
			@Override
			public Expression getTenantId() {
				return new StringValue(Func.toStr(AuthUtil.getTenantId(), SzykConstant.ADMIN_TENANT_ID));
			}

			@Override
			public boolean ignoreTable(String tableName) {
				return true;
			}
		});
	}

	@Bean
	SzykMetaObjectHandler szykMetaObjectHandler() {
		return new SzykMetaObjectHandler();
	}

	/**
	 * mybatis-plus 拦截器集合
	 */
	@Bean
	@ConditionalOnMissingBean(MybatisPlusInterceptor.class)
	public MybatisPlusInterceptor mybatisPlusInterceptor(ObjectProvider<QueryInterceptor[]> queryInterceptors,
														 TenantLineInnerInterceptor tenantLineInnerInterceptor,
														 MybatisPlusProperties mybatisPlusProperties) {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
		// 配置租户拦截器
		if (mybatisPlusProperties.getTenantMode()) {
			interceptor.addInnerInterceptor(tenantLineInnerInterceptor);
		}

		// 动态表名
		DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
		Map<String, TableNameHandler> tableNameHandlerMap = new HashMap<>(16);
		tableNameHandlerMap.put(MonitorTableNameHandler.SENSOR_DATA_TABLE_NAME, new MonitorTableNameHandler());
		dynamicTableNameInnerInterceptor.setTableNameHandlerMap(tableNameHandlerMap);
		//以拦截器的方式处理表名称
		interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);

		// 配置分页拦截器
		SzykPaginationInterceptor paginationInterceptor = new SzykPaginationInterceptor();
		// 配置自定义查询拦截器
		QueryInterceptor[] queryInterceptorArray = queryInterceptors.getIfAvailable();
		if (ObjectUtil.isNotEmpty(queryInterceptorArray)) {
			AnnotationAwareOrderComparator.sort(queryInterceptorArray);
			paginationInterceptor.setQueryInterceptors(queryInterceptorArray);
		}
		paginationInterceptor.setMaxLimit(mybatisPlusProperties.getPageLimit());
		paginationInterceptor.setOverflow(mybatisPlusProperties.getOverflow());
		paginationInterceptor.setOptimizeJoin(mybatisPlusProperties.getOptimizeJoin());
		interceptor.addInnerInterceptor(paginationInterceptor);

		return interceptor;
	}

	/**
	 * sql 日志
	 */
	@Bean
	public SqlLogInterceptor sqlLogInterceptor(MybatisPlusProperties mybatisPlusProperties) {
		return new SqlLogInterceptor(mybatisPlusProperties);
	}

	/**
	 * sql 注入
	 */
	@Bean
	@ConditionalOnMissingBean(ISqlInjector.class)
	public ISqlInjector sqlInjector() {
		return new SzykSqlInjector();
	}

	/**
	 * page 解析器
	 */
	@Override
	public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
		argumentResolvers.add(new PageArgumentResolver());
	}

}
