/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.base.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.annotation.Log;
import com.snszyk.common.dto.CommonDeleteResultDto;
import com.snszyk.common.enums.LogOpEnum;
import com.snszyk.common.enums.LogTemplate;
import com.snszyk.core.annotation.ApiAuth;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.base.dto.TemplateDetailDto;
import com.snszyk.zbusiness.base.dto.TemplateDto;
import com.snszyk.zbusiness.base.dto.TemplateMetadataDto;
import com.snszyk.zbusiness.base.dto.TemplateMetadataViewDto;
import com.snszyk.zbusiness.base.service.ITemplateService;
import com.snszyk.zbusiness.base.service.logic.TemplateLogicService;
import com.snszyk.zbusiness.base.service.logic.TemplateMetadataLogicService;
import com.snszyk.zbusiness.base.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 业务模板 控制器
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("szyk-base/template")
@Api(value = "业务模板", tags = "业务模板接口")
public class TemplateController extends BaseCrudController {

	// private final ITemplateService templateService;

	private final TemplateLogicService templateLogicService;
	private final TemplateMetadataLogicService templateMetadataLogicService;
	private final ITemplateService templateService;


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return templateLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模板保存", notes = "TemplateVo")
	@Log(template = LogTemplate.TEMPLATE_SAVE,op= LogOpEnum.SAVE)
	@ApiAuth(template = LogTemplate.TEMPLATE_SAVE)
	public R<TemplateDto> save(@RequestBody TemplateAVo v) {
		TemplateDto TemplateDto = templateLogicService.saveOrUpdate(v);
		return R.data(TemplateDto);
	}
	/**
	 * 模板复制
	 */
	@PostMapping("/copy")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模板复制", notes = "authCode")
	@ApiAuth(template = LogTemplate.TEMPLATE_SAVE)
	public R<TemplateDto> copy(@RequestBody @Valid  TemplateCVo v) {
		TemplateDto TemplateDto = templateLogicService.copy(v);
		return R.data(TemplateDto);
	}
	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模板分页", notes = "TemplateVo")
	public R<IPage<TemplateDto>> page(TemplateQVo v) {
		IPage<TemplateDto> pageQueryResult = templateLogicService.pageList(v);
		return R.data(pageQueryResult);
	}
	@GetMapping("/globalPage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "全局模板分页", notes = "TemplateVo")
	public R<IPage<TemplateDto>> globalPage(TemplateQVo v) {
		IPage<TemplateDto> pageQueryResult = templateLogicService.globalPage(v);
		return R.data(pageQueryResult);
	}


	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/globalFetchById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "全局模板详情", notes = "id")
	public R<TemplateDto> globalFetchById(Long id) {
		TemplateDto TemplateDto = templateLogicService.doDetail(id, templateService::globalDetail);
		return R.data(TemplateDto);
	}
	/**
	 * 根据ID获取数据
	 */
	@Override
	@GetMapping("/fetchById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模板详情", notes = "id")
	public R<TemplateDto> fetchById(Long id) {
		TemplateDto TemplateDto = templateLogicService.doDetail(id, templateService::detail);
		return R.data(TemplateDto);
	}
	/**
	 * 删除
	 */
	@PostMapping("/removeByIds")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "模板删除", notes = "id")
	@Log(template = LogTemplate.TEMPLATE_REMOVE,op= LogOpEnum.REMOVE)
	@ApiAuth(template = LogTemplate.TEMPLATE_REMOVE)
	public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
		List<CommonDeleteResultDto> result = templateLogicService.removeByIds(ids);
		return R.data(result);
	}

	/**
	 * 添加元数据
	 */
	@PostMapping("/addMetadata")	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "批量添加元数据", notes = "TemplateVo")
	@ApiAuth(template = LogTemplate.TEMPLATE_DESIGN)
	public R<Boolean> addMetadata(@RequestBody @Valid TemplateMetadataAVo v) {
		Boolean b = templateMetadataLogicService.addMetadata(v);
		return R.data(b);
	}

	/**
	 * 修改元数据
	 */
	@PostMapping("/updateMetadata")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "修改元数据", notes = "TemplateVo")
	@ApiAuth(template = LogTemplate.TEMPLATE_DESIGN)
	public R<TemplateMetadataDto> updateMetadata(@RequestBody @Valid TemplateMetadataUVo v) {
		TemplateMetadataDto b = templateMetadataLogicService.updateMetadata(v);
		return R.data(b);
	}

	/**
	 * 删除元数据
	 */
	@PostMapping("/deleteMetadata")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "删除元数据", notes = "TemplateVo")
	@ApiAuth(template = LogTemplate.TEMPLATE_DESIGN)
	public R<Boolean> deleteMetadata(@RequestBody @Valid TemplateMetadataDVo v) {
		Boolean b = templateMetadataLogicService.deleteMetadata(v.getId());
		return R.data(b);
	}
	/**
	 * 排序
	 */
	@PostMapping("/sort")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "元数据排序", notes = "ids")
	public R<Boolean> sort(@RequestBody @Validated List<MetadataSortVo> list) {
		Boolean baseCrudDto = templateMetadataLogicService.sort(list);
		return R.data(baseCrudDto);
	}


	/**
	 * 模版设计的详情
	 */
	@GetMapping("/templateDesignDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模版设计的详情", notes = "id")
	public R<TemplateDetailDto> templateDesignDetail(Long id) {
		TemplateDetailDto TemplateDto = templateLogicService.templateDesignDetail(id, templateService::detail);
		return R.data(TemplateDto);
	}
	/**
	 * 模版设计的详情
	 */
	@GetMapping("/globalTemplateDesignDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模版设计的详情", notes = "id")
	public R<TemplateDetailDto> globalTemplateDesignDetail(Long id) {
		TemplateDetailDto TemplateDto = templateLogicService.templateDesignDetail(id, templateService::globalDetail);
		return R.data(TemplateDto);
	}
	/**
	 * 模版设计的著录项详情
	 */
	@GetMapping("/templateMetadataDetail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "模版设计的著录项详情", notes = "模版著录项的id")
	public R<TemplateMetadataDto> templateMetadataDetail(@RequestParam("templateMetadataId")Long templateMetadataId) {
		TemplateMetadataDto TemplateDto = templateLogicService.templateMetadataDetail(templateMetadataId);
		return R.data(TemplateDto);
	}

	/**
	 * 模版设计的详情
	 */
	@GetMapping("/formView")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "表单预览,档案管理的表头", notes = "id")
	public R<List<TemplateMetadataViewDto>> formView(@RequestParam Long templateId,
													 @RequestParam(required = false) @ApiParam(value = "是否是查询的条件") Integer searchable,
													 @RequestParam(required = false) @ApiParam(value = "是否是列表展示") Integer listDisplay
													) {
		List<TemplateMetadataViewDto> list = templateLogicService.formView(templateId,searchable,listDisplay);
		return R.data(list);
	}
	/**
	 * 同步es索引
	 */
	@GetMapping("/syncEsIndex")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "同步es索引", notes = "id")
	public R<Boolean> syncEsIndex() {
		Boolean b = templateLogicService.syncEsIndex();
		return R.data(b);
	}
}
