/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.llm.service.logic;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.file.dto.CommonDeleteResultDto;
import com.snszyk.zbusiness.llm.dto.LlmHistoryDto;
import com.snszyk.zbusiness.llm.entity.LlmHistory;
import com.snszyk.zbusiness.llm.service.ILlmHistoryService;
import com.snszyk.zbusiness.llm.vo.LlmHistoryAVo;
import com.snszyk.zbusiness.llm.vo.LlmHistoryPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 智能问题的历史记录 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@AllArgsConstructor
@Service
public class LlmHistoryLogicService {

	private final ILlmHistoryService llmHistoryService;

	@Transactional
	public LlmHistoryDto saveOrUpdate(LlmHistoryAVo v) {

		LlmHistory copy = BeanUtil.copy(v, LlmHistory.class);
		boolean b = llmHistoryService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<LlmHistoryDto> pageList(LlmHistoryPageVo v) {
		v.setCreateUser(AuthUtil.getUserId());
		IPage<LlmHistoryDto> page = llmHistoryService.pageList(v);
		List<LlmHistoryDto> records = page.getRecords();
		//record的顺序反转
		Collections.reverse(records);
		for (LlmHistoryDto record : records) {
			String source = record.getSource();
			if (Func.isNotBlank(source)) {
				record.setSourceJson(JSON.parseArray(source));
			}
			String graphSource = record.getGraphSource();
			if (Func.isNotBlank(graphSource)) {
				record.setGraphSourceJson(JSON.parseObject(graphSource));
			}
		}
		page.setRecords(records);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public LlmHistoryDto detail(Long id) {
		// 通过ID查询数据信息
		LlmHistoryDto dto = llmHistoryService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			LlmHistory data = llmHistoryService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
			boolean b = llmHistoryService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


}
