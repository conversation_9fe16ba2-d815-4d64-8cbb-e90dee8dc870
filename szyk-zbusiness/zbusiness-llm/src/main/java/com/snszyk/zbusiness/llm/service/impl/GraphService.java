package com.snszyk.zbusiness.llm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.qcloudsms.httpclient.HTTPMethod;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.service.IUserService;
import com.snszyk.zbusiness.file.dto.AnswerDto;
import com.snszyk.zbusiness.file.service.IGraphApi;
import com.snszyk.zbusiness.file.vo.FilePushVo;
import com.snszyk.zbusiness.llm.config.ApiProperties;
import com.snszyk.zbusiness.llm.service.ApiClient;
import com.snszyk.zbusiness.llm.service.ApiResponse;
import com.snszyk.zbusiness.llm.service.ClientConstant;
import com.snszyk.zbusiness.llm.vo.GraphPreviewVo;
import com.snszyk.zbusiness.llm.vo.GraphQuestionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * @atuthor zhanghongyu
 * @date 2024-05-22
 * @apiNote
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GraphService implements IGraphApi {

	@Value("${graph.tenantId}")
	private String tenantId;

	@Value("${graph.clientId}")
	private String clientId;

	@Value("${graph.clientSecret}")
	private String clientSecret;

	@Value("${graph.grantType}")
	private String grantType;

	@Value("${graph.url}")
	private String url;
	@Value("${llm.url}")
	private String llmUrl;

	@Value("${graph.graphId}")
	private String graphId;

	@Value("${graph.templateId}")
	private String templateId;
	private final SzykRedis szykRedis;
	private final String REDIS_PRO = "graph:label:";
	private final IUserService userService;


	@Override
	public Boolean filePush(FilePushVo filePushVo) {
		JSONObject data = new JSONObject();
		data.put("graphId", graphId); // 图谱ID
		data.put("pushType", 1);//是否推送内置字段 0：否，1：是
		data.put("docUrl", filePushVo.getDocUrl());
		data.put("templateId", templateId);
		data.put("fileName", filePushVo.getFileName());
		data.put("knowledgeDocId", filePushVo.getKnowledgeDocId());
		ApiProperties properties = new ApiProperties(tenantId, clientId, clientSecret, grantType, url);
		//调用服务
		log.info("知识图谱的api参数:{}", JSON.toJSONString(data));
		ApiResponse response = ApiClient.callApi(data, ClientConstant.ROUTER_API_PUSH_TASK, properties, HTTPMethod.POST.name());
		if (response.getCode() == ClientConstant.HTTP_STATUS_SUCCESS) {
			JSONObject result = response.getData();
			log.info("知识图谱api数据推送结果:{}", JSON.toJSONString(result));
			return true;
		} else {
			log.error("知识图谱数据推送异常");
			throw new BusinessException(response.getMsg());
		}
	}

	// 返回示例:{"result":"田陈煤矿设备在线检测硬件采购合同的付款方式是电汇。","question":"田陈煤矿设备在线检测硬件采购合同的付款方式"}

	@Override
	public AnswerDto graphQuestion(GraphQuestionVo v) {
		AnswerDto answerDto = new AnswerDto();
		JSONObject data = new JSONObject();
		data.put("graphId", graphId); // 图谱ID
		data.put("msg", v.getMsg()); // 提问内容
		ApiProperties properties = new ApiProperties(tenantId, clientId, clientSecret, grantType, url);
		//调用服务
		log.info("知识图谱:开始调用NL2Cypher接口:");
		long l = System.currentTimeMillis();
		ApiResponse response = ApiClient.callApi(data, ClientConstant.ROUTER_API_NL2CYPHER_QA, properties, HTTPMethod.POST.name());
		log.info("====知识图谱,NL2Cypher接口返回结果{}====", JSON.toJSONString(response));
		log.info("====NL2Cypher接口调用耗时：{}ms", System.currentTimeMillis() - l);
		if (response.getCode() == ClientConstant.HTTP_STATUS_SUCCESS) {
			JSONObject result = response.getData();
			String answer = result.getString("result");
			Integer code = result.getInteger("code");
			if (code != 1) {
				//code：1 是成功  -1异常或没有查询到结果
				answerDto.setAnswer("");
			} else {
				answerDto.setAnswer(answer);
				JSONObject graph = result.getJSONObject("graph");
				if (graph != null) {
					String graphName = graph.getString("graphName");
					String graphId = graph.getString("graphId");
					answerDto.setGraphName(graphName);
					answerDto.setGraphId(graphId);
					//todo
					answerDto.setCommand("");
				}
			}
		} else {
			throw new ServiceException("知识图谱服务异常，暂时无法回答您的问题");
		}
		return answerDto;
	}

	@Override
	public JSONObject graphPreview(GraphPreviewVo v) {
		String command = v.getCommand();
		if (Func.isEmpty(command)) {
			command = "MATCH (n) WHERE ANY(prop in keys(n) WHERE n[prop] CONTAINS '' and n.graphId=" + v.getGraphId() + ") OPTIONAL MATCH (n)-[r]-(m) RETURN n, r, m";
		}

		JSONObject data = new JSONObject();
		data.put("graphId", v.getGraphId()); // 图谱ID
		data.put("command", command); // vilk
		ApiProperties properties = new ApiProperties(tenantId, clientId, clientSecret, grantType, url);
		//调用服务
		log.info("知识图谱:开始调用预览接口:");
		long l = System.currentTimeMillis();
		ApiResponse response = ApiClient.callApi(data, ClientConstant.ROUTER_API_PREVIEW, properties, HTTPMethod.POST.name());
		log.info("====知识图谱,预览接口返回结果{}====", JSON.toJSONString(response));
		log.info("====预览接口调用耗时：{}ms", System.currentTimeMillis() - l);
		if (response.getCode() == ClientConstant.HTTP_STATUS_SUCCESS) {
			JSONObject result = response.getData();
			return result;
		} else {
			throw new ServiceException("知识图谱服务异常，暂时无法预览图谱");
		}
	}

	@Override
	public JSONObject graphGetById(String graphId) {


		JSONObject data = new JSONObject();
		data.put("id", graphId);
		ApiProperties properties = new ApiProperties(tenantId, clientId, clientSecret, grantType, url);
		//调用服务
		log.info("知识图谱:开始调用详情接口:");
		long l = System.currentTimeMillis();
		ApiResponse response = ApiClient.callApi(data, ClientConstant.FETCH_BY_ID, properties, HTTPMethod.GET.name());
		log.info("====知识图谱,详情接口返回结果{}====", JSON.toJSONString(response));
		log.info("====详情接口调用耗时：{}ms", System.currentTimeMillis() - l);
		if (response.getCode() == ClientConstant.HTTP_STATUS_SUCCESS) {
			JSONObject result = response.getData();
			return result;
		} else {
			throw new ServiceException("知识图谱服务异常，暂时无法查看图谱");
		}
	}

	@Override
	public JSONObject ontologiesGetById(String graphId) {

		JSONObject data = new JSONObject();
		data.put("id", graphId);
		ApiProperties properties = new ApiProperties(tenantId, clientId, clientSecret, grantType, url);
		//调用服务
		log.info("知识图谱:开始调用拓扑详情接口:");
		long l = System.currentTimeMillis();
		ApiResponse response = ApiClient.callApi(data, ClientConstant.ONTOLOGIES_FETCH_BY_ID, properties, HTTPMethod.GET.name());
		log.info("====知识图谱,拓扑详情接口返回结果{}====", JSON.toJSONString(response));
		log.info("====拓扑详情接口调用耗时：{}ms", System.currentTimeMillis() - l);
		if (response.getCode() == ClientConstant.HTTP_STATUS_SUCCESS) {
			JSONObject result = response.getData();
			return result;
		} else {
			throw new ServiceException("知识图谱服务异常，暂时无法查看拓扑");
		}
	}
}
