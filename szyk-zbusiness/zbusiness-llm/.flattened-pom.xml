<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.snszyk</groupId>
    <artifactId>szyk-zbusiness</artifactId>
    <version>2.0.0.RELEASE</version>
  </parent>
  <groupId>com.snszyk</groupId>
  <artifactId>zbusiness-demo</artifactId>
  <version>2.0.0.RELEASE</version>
  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-common</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>system-rest</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>zbusiness-api</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-core-boot</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>szyk-core-cloud</artifactId>
          <groupId>com.snszyk</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-core-crud</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
    </resources>
  </build>
</project>
