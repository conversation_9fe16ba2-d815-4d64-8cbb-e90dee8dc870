# Python虚拟环境使用指南

## 1. 虚拟环境概述

Python虚拟环境是一个独立的Python解释器环境，它允许在同一台计算机上隔离不同项目的依赖关系。使用虚拟环境的主要优势包括：

- **依赖隔离**：每个项目可以使用自己特定版本的依赖包，避免版本冲突
- **环境一致性**：确保开发、测试和生产环境使用相同的依赖版本
- **简化部署**：通过`requirements.txt`文件轻松复制环境
- **避免权限问题**：无需管理员权限即可安装包

本项目使用标准的Python `venv` 模块创建虚拟环境，该模块是Python 3.3+的内置功能。

## 2. 项目虚拟环境配置

本项目的虚拟环境配置如下：

- **Python版本**：3.10.11
- **虚拟环境目录**：`./venv/`
- **系统包引用**：禁用（`include-system-site-packages = false`）
- **主要依赖**：详见`requirements.txt`文件

## 3. 虚拟环境操作指南

### 3.1 创建虚拟环境

如果项目中尚未包含虚拟环境，可以使用以下命令创建：

```bash
# Windows
python -m venv venv

# Linux/macOS
python3 -m venv venv
```

### 3.2 激活虚拟环境

在使用虚拟环境之前，需要先激活它。激活方式因操作系统而异：

#### Windows系统

```bash
# CMD
venv\Scripts\activate.bat

# PowerShell
venv\Scripts\Activate.ps1
```

> **注意**：如果在PowerShell中遇到执行策略限制，可能需要先运行以下命令：
> ```powershell
> Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
> ```

#### Linux/macOS系统

```bash
source venv/bin/activate
```

激活成功后，命令提示符前会出现`(venv)`前缀，表示当前处于虚拟环境中。

### 3.3 安装依赖包

本项目使用`requirements.txt`和`requirements.in`文件管理依赖：

- `requirements.in`：包含直接依赖的列表，手动维护
- `requirements.txt`：包含所有依赖（包括间接依赖）的固定版本，通过pip-compile生成

#### 安装所有依赖

```bash
pip install -r requirements.txt
```

#### 更新依赖

如果需要更新依赖，建议使用`pip-tools`：

```bash
# 安装pip-tools
pip install pip-tools

# 从requirements.in生成更新的requirements.txt
pip-compile requirements.in

# 安装更新后的依赖
pip install -r requirements.txt
```

### 3.4 添加新依赖

当需要添加新的依赖包时，建议按照以下步骤操作：

1. 将新依赖添加到`requirements.in`文件
2. 运行`pip-compile requirements.in`更新`requirements.txt`
3. 执行`pip install -r requirements.txt`安装更新后的依赖

### 3.5 退出虚拟环境

完成工作后，可以使用以下命令退出虚拟环境：

```bash
deactivate
```

## 4. 项目启动

### 4.1 使用虚拟环境启动项目

在激活虚拟环境后，可以使用以下命令启动项目：

```bash
# 开发环境启动
python app.py

# 或使用uvicorn启动（生产环境推荐）
uvicorn app:app --host 0.0.0.0 --port 8089
```

### 4.2 初始化Milvus向量库

项目启动时会自动尝试初始化Milvus向量库。如果需要单独初始化，可以使用以下命令：

```bash
# Windows
python server/init_milvus.py

# Linux
./init_milvus.sh
```

如需删除现有集合并重新创建，可以添加`--drop`参数：

```bash
# Windows
python server/init_milvus.py --drop

# Linux
./init_milvus.sh --drop
```

## 5. 常见问题与解决方案

### 5.1 依赖冲突

如果安装依赖时遇到版本冲突，可以尝试以下方法：

1. 使用`pip install --upgrade pip`更新pip
2. 清除pip缓存：`pip cache purge`
3. 使用`pip install -r requirements.txt --force-reinstall`强制重新安装所有依赖

### 5.2 虚拟环境激活失败

如果虚拟环境激活失败，可能的原因包括：

1. 执行策略限制（PowerShell）：运行`Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
2. 路径问题：确保在正确的目录中执行激活命令
3. 虚拟环境损坏：删除`venv`目录并重新创建虚拟环境

### 5.3 找不到已安装的模块

如果Python报错找不到已安装的模块，可能的原因包括：

1. 虚拟环境未激活：确认命令提示符前是否有`(venv)`前缀
2. 模块未正确安装：尝试单独安装该模块`pip install <模块名>`
3. 路径问题：检查`sys.path`是否包含正确的路径

## 6. 最佳实践

1. **始终使用虚拟环境**：即使是小项目也应该使用虚拟环境，养成良好习惯
2. **固定依赖版本**：在`requirements.in`中指定确切的版本号，避免意外升级
3. **定期更新依赖**：定期检查并更新依赖，修复安全漏洞
4. **不要提交虚拟环境**：将`venv/`目录添加到`.gitignore`文件中
5. **记录Python版本**：在项目文档中明确指定所需的Python版本

## 7. 相关资源

- [Python venv官方文档](https://docs.python.org/3/library/venv.html)
- [pip-tools文档](https://pip-tools.readthedocs.io/en/latest/)
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [Uvicorn文档](https://www.uvicorn.org/)
