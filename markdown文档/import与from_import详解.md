# Python 中 `import` 与 `from ... import ...` 的区别详解

在 Python 中，`import` 和 `from ... import ...` 是两种常见的模块导入方式。它们的主要区别在于 **导入的内容** 和 **使用方式**。

---

## 一、`import` 的作用

### 🧩 基本语法：
```python
import module_name
```

### 🔍 含义：
- 导入 **整个模块**。
- 模块可以是一个 `.py` 文件，也可以是一个包含多个模块的包（package）。

### 📌 示例：
```python
import config.config_env
```

这表示你导入了模块名为 `config.config_env` 的模块，你可以通过模块名访问它的变量、函数和类：

```python
print(config.config_env.get_env_name())
```

---

## 二、`from ... import ...` 的作用

### 🧩 基本语法：
```python
from module_name import name1, name2, ...
```

### 🔍 含义：
- 从指定模块中 **导入特定的名称（如函数、变量、类等）**。
- 这些名称可以直接使用，不需要加上模块前缀。

### 📌 示例：
```python
from config.config_env import get_env_name
```

这表示你只从 `config.config_env` 模块中导入了 `get_env_name` 函数，你可以直接调用它：

```python
print(get_env_name())
```

---

## 三、两者的区别总结

| 特性 | `import module` | `from module import name` |
|------|------------------|----------------------------|
| 导入内容 | 整个模块 | 模块中的具体名称（如函数、变量） |
| 使用方式 | 必须带模块名前缀：`module.name` | 可以直接使用：`name` |
| 可读性 | 更清晰，知道名字来自哪个模块 | 简洁但可能模糊来源 |
| 名称冲突风险 | 低（需带模块名） | 高（可能与其他同名变量/函数冲突） |
| 推荐使用场景 | 导入整个模块或频繁使用多个成员 | 只需要使用模块中的少数几个名称 |

---

## 四、示例对比

### 使用 `import`

```python
import config.config_env

print(config.config_env.get_env_name())
```

### 使用 `from ... import ...`

```python
from config.config_env import get_env_name

print(get_env_name())
```

两者功能相同，只是写法不同。

---

## 五、进阶用法

### 1. 导入多个名称

```python
from config.config_env import get_env_name, set_env_name
```

### 2. 导入全部公开名称（不推荐）

```python
from config.config_env import *

# ⚠️ 不推荐，因为会污染命名空间，难以追踪来源
```

### 3. 使用别名简化长模块名

```python
import config.config_env as env

print(env.get_env_name())
```

---

## 六、总结

| 写法 | 含义 | 适用场景 |
|------|------|----------|
| `import module` | 导入整个模块 | 需要使用模块中多个成员时 |
| `from module import name` | 导入模块中的某个具体名称 | 只需要使用模块中的一两个函数或变量时 |
| `import module as alias` | 给模块起别名 | 模块名太长或想简化写法 |
| `from module import *` | 导入模块中所有公共名称 | 少数情况使用，一般不推荐 |

---

如果你还想了解相对导入（`from . import module`）、动态导入（`__import__()`）、模块缓存等内容，也欢迎继续提问！