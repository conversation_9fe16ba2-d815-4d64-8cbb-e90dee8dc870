#数据源配置
spring:
  redis:
    ##redis 单机环境配置
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************************
    username: root
    password: root

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#szyk配置
szyk:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://127.0.0.1:6379
    password: 123456
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html
