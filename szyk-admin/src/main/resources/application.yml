#服务器配置
server:
  port: 8088
  undertow:
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
captcha:
  expire:
    default: 10000
    # 文字点选验证码 过期时间设置大一点
    WORD_IMAGE_CLICK: 60000
  cache:
    enabled: true
    cache-size: 20
  secondary:
    enabled: false
  # 不初始化默认的资源文件
  init-default-resource: false

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #driver-class-name: org.postgresql.Driver
    #driver-class-name: oracle.jdbc.OracleDriver
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # MySql、PostgreSQL、SqlServer校验
      validation-query: select 1
      # Oracle校验
      #validation-query: select 1 from dual
      validation-query-timeout: 2000
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      stat-view-servlet:
        enabled: true
        login-username: szyk
        login-password: 1qaz@WSX
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: '*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*'
        session-stat-enable: true
        session-stat-max-count: 10
  servlet:
    multipart:
      enabled: true #是否启用http上传处理
      max-request-size: 100MB #最大请求文件的大小
      max-file-size: 20MB #设置单个文件最大长度
      file-size-threshold: 20MB #当文件达到多少时进行磁盘写入

# mybatis
mybatis-plus:
  mapper-locations: classpath:com/snszyk/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.snszyk.**.entity
  tenant-mode: false
  #typeEnumsPackage: com.snszyk.dashboard.entity.enums
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增", 1:"不操作", 2:"用户输入ID",3:"数字型snowflake", 4:"全局唯一ID UUID", 5:"字符串型snowflake";
      id-type: assign_id
      #字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
      #驼峰下划线转换
      table-underline: true
      # 逻辑删除配置
      # 逻辑删除全局值（1表示已删除，这也是Mybatis Plus的默认配置）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除，这也是Mybatis Plus的默认配置）
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: szyk
    password: szyk
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2022 Szyk All Rights Reserved

#swagger公共信息
swagger:
  title: 统一快速开发框架接口文档系统
  description: 统一快速开发框架接口文档系统
  version: 2.0.0.RELEASE
  license: Powered By 山东能源数智云科技有限公司
  license-url: https://snszyk.com
  terms-of-service-url: https://snszyk.com
  contact:
    name: 数智云科
    email: 数智云科@163.com
    url: https://snszyk.com

#flowable配置
flowable:
  activity-font-name: \u5B8B\u4F53
  label-font-name: \u5B8B\u4F53
  annotation-font-name: \u5B8B\u4F53
  check-process-definitions: false
  database-schema-update: false


#报表配置
report:
  enabled: false
  database:
    provider:
      prefix: szyk-

#oss默认配置
oss:
  enabled: true
  name: qiniu
  tenant-mode: false
  endpoint: http://prt1thnw3.bkt.clouddn.com
  access-key: N_Loh1ngBqcJovwiAJqR91Ifj2vgOWHOf8AwBA_h
  secret-key: AuzuA1KHAbkIndCU0dB3Zfii2O3crHNODDmpxHRS
  bucket-name: szyk

#第三方登陆配置
social:
  oauth:
    GITHUB:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/github
    GITEE:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/gitee
    WECHAT_OPEN:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/wechat
    QQ:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/qq
    DINGTALK:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/dingtalk

#szyk配置
szyk:
  #token配置
  token:
    #是否有状态
    state: true
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr1USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: false
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /szyk-chat/weixin
      - /szyk-desk/notice/submit
      - /smart/**
  #安全框架配置
  secure:
    #接口放行
    skip-url:
      - /szyk-auth/oauth/gen
      - /szyk-auth/oauth/code
      - /szyk-test/**
      - /smart/**
    #授权认证配置
    auth:
      - method: ALL
        pattern: /szyk-chat/weixin/**
        expression: "hasAuth()"
      - method: POST
        pattern: /szyk-desk/dashboard/upload
        expression: "hasTimeAuth(9, 17)"
      - method: POST
        pattern: /szyk-desk/dashboard/submit
        expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      - method: ALL
        pattern: /szyk-desk/dashboard/info
        username: "szyk"
        password: "szyk"
    #动态签名认证配置
    sign:
      - method: ALL
        pattern: /szyk-desk/dashboard/sign
        crypto: "sha1"
    #多终端认证配置
    client:
      - client-id: sword
        path-patterns:
          - /szyk-sword/**
      - client-id: saber
        path-patterns:
          - /szyk-saber/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: true
    #多租户授权保护
    license: false
    #动态数据源功能
    dynamic-datasource: false
    #动态数据源全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
      - szyk_user
      -

      # 文件存储路径
  attach:
    path: /opt/upload


xxl:
  job:
    # 调度中心相关
    admin:
      addresses: http://**************:32384/xxl-job-admin
      #      addresses: http://127.0.0.1:8080/xxl-job-admin
      timeout: 3
      accessToken:
    # 执行器相关
    executor:
      # 日志保存时间
      logretentiondays: 30
