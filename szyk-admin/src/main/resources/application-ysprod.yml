#数据源配置
spring:
  redis:
    ##redis 单机环境配置
    host: r-2zeqancowgpribyqbqpd.redis.rds.aliyuncs.com
    port: 60379
    password: DH^WFYtw@!mAEvT
    database: 21
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    # MySql
    url: **************************************************************************************************************************************************************************************************************************************************************************
    username: ys
    password: YsgH%85#4*gPV8#d
    # PostgreSQL
    #url: ******************************************
    #username: postgres
    #password: 123456
    # Oracle
    #url: *************************************
    #username: SZYK_BOOT
    #password: SZYK_BOOT
    # SqlServer
    #url: ******************************************************
    #username: szyk_boot
    #password: szyk_boot
  kafka:
    custom:
      enable: false
    bootstrap-servers: 127.0.0.1:9092
    producer:
      # 发生错误后，消息重发的次数。
      retries: 0
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
      batch-size: 16384
      # 设置生产者内存缓冲区的大小。
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      # acks: 1
    consumer:
      # 指定默认消费者group id
      group-id: GROUP1
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      # 自动提交的时间间隔 在spring boot 2.X 版本中这里采用的是值的类型为Duration 需要符合特定的格式，如1S,1M,2H,5D
      # auto-commit-interval: 1S
      enable-auto-commit: false
      # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
      # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
      # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
      auto-offset-reset: latest
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        session.timeout.ms: 60000
    listener:
      log-container-config: false
      # 在侦听器容器中运行的线程数。
      concurrency: 2
      #listner负责ack，每调用一次，就立即commit
      ack-mode: manual_immediate
      # missing-topics-fatal: false

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#szyk配置
szyk:
  #分布式锁配置
  lock:
    enabled: false
    ##redis服务地址
    address: redis://r-2zeqancowgpribyqbqpd.redis.rds.aliyuncs.com:60379
    password: DH^WFYtw@!mAEvT
    database: 21
  #本地文件上传
#  file:
#    remote-mode: true
#    upload-domain: http://localhost:8999
#    remote-path: /usr/share/nginx/html

#oss默认配置
oss:
  enabled: true
  name: minio
  tenant-mode: false
  endpoint-display: http://************:9000
  endpoint: http://************:9000
  access-key: adminminio
  secret-key: Ys8d9D#FIdfPb
  bucket-name: cmks
easy-es:
  enable: true #默认为true,若为false则认为不启用本框架
  #  address : *************:31551 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  global-config:
    db-config:
      index-prefix: prod_ #索引前缀
      field-strategy: ignored

  address: 127.0.0.1:9200
  username: elastic
  password: TeY%Ks82S^Mg
llm:
  url: http://127.0.0.1:8089
  embedding_path: /api/v1/knowledge/embedding
  query_path: /api/v1/knowledge/answer
  query_stream_path: /api/v1/knowledge/answer_stream
  suggest_path: /api/v1/knowledge/suggest
  delete_embedding_by_source_id: /api/v1/knowledge/delete_embedding_by_source_id
  cmks_server_path: http://************:80/api
graph:
  tenantId: "000000"
  clientId: tongji
  clientSecret: tongji
  grantType: client
  url: http://************:88
  graphId: 1892410704257937410
  templateId: 1872472291357032450
common:
  tempFilePath: /usr/temp/downloadPath/
#knife4j配置
knife4j:
  #启用
  enable: false
  #基础认证
  basic:
    enable: true
    username: szyk
    password: 1qaz@WSX
