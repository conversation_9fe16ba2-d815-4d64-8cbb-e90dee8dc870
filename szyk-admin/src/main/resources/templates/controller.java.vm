/*
 *      Copyright (c) 2018-2028
 */
package $!{package.Controller};

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;

#set($servicePackage=$package.Entity.replace("entity","service"))
#set($voPackage=$package.Entity.replace("entity","vo"))

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import $!{voPackage}.$!{entity}Vo;
import $!{dtoPackage}.CommonDeleteResultDto;
import $!{voPackage}.$!{entity}PageVo;
import $!{dtoPackage}.$!{entity}Dto;
import javax.validation.Valid;
import java.util.List;
import com.snszyk.core.tool.api.R;

/**
 * $!{table.comment} 控制器
 *
 * <AUTHOR>
 * @since $!{date}
 */
@RestController
@AllArgsConstructor
@RequestMapping("$!{cfg.serviceName}/$!{cfg.entityKey}")
@Api(value = "$!{table.comment}", tags = "$!{table.comment}接口")
#if($!{superControllerClass})
public class $!{table.controllerName} extends SzykController {
#else
public class $!{table.controllerName} {
#end

    private final $!{entity}LogicService $!{table.entityPath}LogicService;



    /**
     * 保存
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "$!{table.comment}保存", notes = "$!{entity}Vo")
    public R<$!{entity}Dto> save(@RequestBody $!{entity}AVo v) {
        $!{entity}Dto baseCrudDto = $!{table.entityPath}LogicService.saveOrUpdate(v);
        return R.data(baseCrudDto);
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "$!{table.comment}分页", notes = "$!{entity}PageVo")
    public R<IPage<$!{entity}Dto>> page($!{entity}PageVo v) {
        IPage<$!{entity}Dto> pageQueryResult = $!{table.entityPath}LogicService.pageList(v);
        return R.data(pageQueryResult);
    }

    /**
     * 根据ID获取数据
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "$!{table.comment}详情", notes = "id")
    public R<$!{entity}Dto> detail(Long id) {
        $!{entity}Dto baseCrudDto = $!{table.entityPath}LogicService.detail(id);
        return R.data(baseCrudDto);
    }

    /**
     * 删除
     */
    @PostMapping("/removeByIds")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "$!{table.comment}删除", notes = "id")
    public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
        List<CommonDeleteResultDto> result = $!{table.entityPath}LogicService.removeByIds(ids);
        return R.data(result);
    }
}
