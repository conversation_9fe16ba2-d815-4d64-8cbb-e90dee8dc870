package com.snszyk.core.log.error;

import com.snszyk.core.log.props.SzykRequestLogProperties;
import lombok.RequiredArgsConstructor;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.log.publisher.ErrorLogPublisher;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.core.secure.exception.SecureException;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.UrlUtil;
import com.snszyk.core.tool.utils.WebUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.DispatcherServlet;

import javax.servlet.Servlet;

@Slf4j
@Order(0) // 确保优先级高于原始类
@RequiredArgsConstructor
@Configuration(proxyBeanMethods = false)
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnClass({ Servlet.class, DispatcherServlet.class })
@RestControllerAdvice
public class SzykRestExceptionTranslator {

    private final SzykRequestLogProperties properties;

    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleError(ServiceException e) {
        log.error("业务异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    @ExceptionHandler(SecureException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R handleError(SecureException e) {
        log.error("认证异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleError(Throwable e) {
        log.error("服务器异常", e);
        if (properties.getErrorLog()) {
            ErrorLogPublisher.publishEvent(e, UrlUtil.getPath(WebUtil.getRequest().getRequestURI()));
        }
        return R.fail(ResultCode.INTERNAL_SERVER_ERROR,  ResultCode.INTERNAL_SERVER_ERROR.getMessage() );
    }
}
